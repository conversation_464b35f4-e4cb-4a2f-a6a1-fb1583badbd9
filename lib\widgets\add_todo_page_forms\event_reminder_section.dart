import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/color_util.dart';
import '../../utils/text_util.dart';

/// 事项前提示 Section 组件
class EventReminderSection extends StatefulWidget {
  final Color cardColor;
  final TextEditingController travelTimeController;
  final TextEditingController reminderAdvanceController;
  final TextEditingController notesController;

  const EventReminderSection({
    super.key,
    required this.cardColor,
    required this.travelTimeController,
    required this.reminderAdvanceController,
    required this.notesController,
  });

  @override
  State<EventReminderSection> createState() => _EventReminderSectionState();
}

class _EventReminderSectionState extends State<EventReminderSection> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              '事项前提示',
              style: TextUtil.base.sp(16).semiBold.withColor(widget.cardColor),
            ),
          ),
          SizedBox(height: 16.h),
          
          // 出行时间
          Row(
            children: [
              Text(
                '出行时间',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _showTravelTimePicker,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: widget.cardColor),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Text(
                    widget.travelTimeController.text.isEmpty ? '00 分钟' : widget.travelTimeController.text,
                    style: TextUtil.base.sp(12).withColor(
                      widget.travelTimeController.text.isEmpty ? CupertinoColors.systemGrey : widget.cardColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          
          // 设置提醒
          Row(
            children: [
              Text(
                '设置提醒',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _showAdvanceReminderPicker,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: widget.cardColor),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        widget.reminderAdvanceController.text.isEmpty ? '00 分钟' : widget.reminderAdvanceController.text,
                        style: TextUtil.base.sp(12).withColor(
                          widget.reminderAdvanceController.text.isEmpty ? CupertinoColors.systemGrey : widget.cardColor,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '前',
                        style: TextUtil.base.sp(12).withColor(widget.cardColor),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 其他备注
          Text(
            '其他备注',
            style: TextUtil.base.sp(14).withColor(widget.cardColor),
          ),
          SizedBox(height: 8.h),
          Container(
            width: double.infinity,
            height: 120.h,
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              border: Border.all(color: widget.cardColor),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: CupertinoTextField(
              controller: widget.notesController,
              placeholder: '记录一些重要信息',
              placeholderStyle: TextUtil.base.sp(14).withColor(CupertinoColors.systemGrey),
              style: TextUtil.base.sp(14).withColor(widget.cardColor),
              decoration: null,
              padding: EdgeInsets.zero,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
            ),
          ),
        ],
      ),
    );
  }

  // 显示出行时间选择器
  void _showTravelTimePicker() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          color: CupertinoColors.white,
          child: Column(
            children: [
              Container(
                height: 50.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        '取消',
                        style: TextUtil.base.sp(16).withColor(CupertinoColors.systemGrey),
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32.h,
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      widget.travelTimeController.text = '${(index + 1) * 5} 分钟';
                    });
                  },
                  children: List.generate(24, (index) {
                    final minutes = (index + 1) * 5;
                    return Center(
                      child: Text(
                        '$minutes 分钟',
                        style: TextUtil.base.sp(16),
                      ),
                    );
                  }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 显示提前提醒选择器
  void _showAdvanceReminderPicker() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          color: CupertinoColors.white,
          child: Column(
            children: [
              Container(
                height: 50.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        '取消',
                        style: TextUtil.base.sp(16).withColor(CupertinoColors.systemGrey),
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32.h,
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      widget.reminderAdvanceController.text = '${(index + 1) * 10} 分钟';
                    });
                  },
                  children: List.generate(12, (index) {
                    final minutes = (index + 1) * 10;
                    return Center(
                      child: Text(
                        '$minutes 分钟',
                        style: TextUtil.base.sp(16),
                      ),
                    );
                  }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
