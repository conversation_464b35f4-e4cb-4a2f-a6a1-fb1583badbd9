import 'package:flutter_test/flutter_test.dart';
import 'package:align/models/user_models.dart';

void main() {
  group('DueDateSetup Tests', () {
    test('UserModel with dueDate should work correctly', () {
      // 创建包含预产期的用户模型
      final user = UserModel(
        id: 'test_user',
        username: 'testuser',
        nickname: '测试用户',
        dueDate: '2024-12-25T00:00:00.000Z',
      );

      // 验证基本属性
      expect(user.id, 'test_user');
      expect(user.username, 'testuser');
      expect(user.nickname, '测试用户');
      expect(user.dueDate, '2024-12-25T00:00:00.000Z');

      // 验证显示名称
      expect(user.displayName, '测试用户');

      // 验证预产期相关方法
      expect(user.dueDateAsDateTime, isNotNull);
      expect(user.daysToDueDate, isNotNull);
      expect(user.dueDateStatusText, isNotEmpty);
    });

    test('UserModel copyWith should include dueDate', () {
      final originalUser = UserModel(
        id: 'test_user',
        username: 'testuser',
        dueDate: '2024-12-25T00:00:00.000Z',
      );

      // 使用 copyWith 更新预产期
      final updatedUser = originalUser.copyWith(
        dueDate: '2024-12-31T00:00:00.000Z',
      );

      expect(updatedUser.id, 'test_user');
      expect(updatedUser.username, 'testuser');
      expect(updatedUser.dueDate, '2024-12-31T00:00:00.000Z');
    });

    test('UserModel without dueDate should handle gracefully', () {
      final user = UserModel(
        id: 'test_user',
        username: 'testuser',
      );

      expect(user.dueDate, isNull);
      expect(user.dueDateAsDateTime, isNull);
      expect(user.daysToDueDate, isNull);
      expect(user.dueDateStatusText, '未设置预产期');
    });

    test('UserModel JSON serialization should include dueDate', () {
      final user = UserModel(
        id: 'test_user',
        username: 'testuser',
        dueDate: '2024-12-25T00:00:00.000Z',
      );

      // 序列化为 JSON
      final json = user.toJson();
      expect(json['dueDate'], '2024-12-25T00:00:00.000Z');

      // 从 JSON 反序列化
      final userFromJson = UserModel.fromJson(json);
      expect(userFromJson.dueDate, '2024-12-25T00:00:00.000Z');
    });
  });
}
