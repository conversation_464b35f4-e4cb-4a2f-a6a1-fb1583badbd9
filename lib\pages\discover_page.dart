import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/widgets/post_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:waterfall_flow/waterfall_flow.dart';

class DiscoverPage extends StatefulWidget {
  const DiscoverPage({super.key});

  @override
  State<DiscoverPage> createState() => _DiscoverPageState();
}

class _DiscoverPageState extends State<DiscoverPage> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: Safe<PERSON>rea(
        child: CustomScrollView(
          slivers: [
            SliverPersistentHeader(
              pinned: true,
              delegate: _SliverHeaderDelegate(
                minHeight: 55.h,
                maxHeight: 55.h,
                child: Container(
                  color: CupertinoColors.systemBackground,
                  child: _buildHeader(),
                ),
              ),
            ),
            if (_selectedTabIndex == 0) ...[
              SliverPadding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                sliver: SliverList(
                  delegate: SliverChildListDelegate(
                    [
                      SizedBox(height: 24.h),
                      _buildTopCard(),
                      SizedBox(height: 32.h),
                      _buildSectionTitle('文章类别'),
                      SizedBox(height: 16.h),
                      _buildCategories(),
                      SizedBox(height: 32.h),
                      _buildSectionTitle('推荐的文章'),
                      SizedBox(height: 16.h),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: _buildRecommendedArticles(),
              ),
            ] else ...[
              SliverPadding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                sliver: SliverList(
                  delegate: SliverChildListDelegate(
                    [
                      SizedBox(height: 24.h),
                      _buildSearchBar(),
                      SizedBox(height: 32.h),
                      _buildSectionTitle('发现'),
                      SizedBox(height: 16.h),
                      _buildSurroundingsCategories(),
                      SizedBox(height: 32.h),
                      _buildSectionTitle('推荐'),
                      SizedBox(height: 16.h),
                    ],
                  ),
                ),
              ),
            ],
            _buildRecommendationGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.fromLTRB(24.w, 12.h, 24.w, 0),
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildTabItem(0, CupertinoIcons.heart, '科普'),
            _buildTabItem(1, CupertinoIcons.location, '周边'),
          ],
        ),
      ),
    );
  }

  Widget _buildTabItem(int index, IconData icon, String title) {
    bool isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTabIndex = index;
          });
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFFFFB4B4) : Colors.transparent,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 16.sp, color: isSelected ? Colors.white : Colors.black),
              SizedBox(width: 4.w),
              Text(title, style: TextUtil.base.sp(14).semiBold.withColor(isSelected ? Colors.white : Colors.black)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: ColorUtil.grey,
        borderRadius: BorderRadius.circular(999.r),
      ),
      child: Row(
        children: [
          Icon(CupertinoIcons.search, color: CupertinoColors.black, size: 20.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text('搜索...', style: TextUtil.base.sp(14).grey),
          ),
          Container(
            width: 1.w,
            height: 16.h,
            color: CupertinoColors.systemGrey4,
          ),
          SizedBox(width: 12.w),
          Text('北京', style: TextUtil.base.sp(14).black),
          SizedBox(width: 4.w),
          Icon(CupertinoIcons.chevron_down, size: 14.sp, color: CupertinoColors.black),
        ],
      ),
    );
  }

  Widget _buildTopCard() {
    return Container(
      height: 150.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        image: const DecorationImage(
          image: AssetImage('images/placeholder.png'), // Replace with actual image
          fit: BoxFit.cover,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.4),
              Colors.transparent,
              Colors.black.withValues(alpha: 0.4),
            ],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '你知道吗。。。',
                style: TextUtil.base.sp(16).semiBold.withColor(Colors.white),
              ),
              Text(
                '产后抑郁症影响着超过 75% 的新妈妈。',
                style: TextUtil.base.withColor(Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      children: [
        Text(
          title,
          style: TextUtil.base.sp(18).semiBold,
        ),
        SizedBox(width: 8.w),
        Container(
          width: 20.r,
          height: 20.r,
          decoration: BoxDecoration(
            color: ColorUtil.grey,
            borderRadius: BorderRadius.circular(999.r),
          ),
          child: Icon(CupertinoIcons.right_chevron, color: CupertinoColors.black, size: 16.sp),
        ),
      ],
    );
  }

  Widget _buildCategories() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildCategoryItem('饮食', 'images/placeholder.png'),
        _buildCategoryItem('运动', 'images/placeholder.png'),
        _buildCategoryItem('情感关系', 'images/placeholder.png'),
        _buildCategoryItem('医学常识', 'images/placeholder.png'),
      ],
    );
  }

  Widget _buildSurroundingsCategories() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildCategoryItem('支持组织', 'images/placeholder.png'),
        _buildCategoryItem('月子中心', 'images/placeholder.png'),
        _buildCategoryItem('宝宝店铺', 'images/placeholder.png'),
        _buildCategoryItem('瑜伽', 'images/placeholder.png'),
      ],
    );
  }

  Widget _buildCategoryItem(String title, String imagePath) {
    return Column(
      children: [
        CircleAvatar(
          radius: 30.r,
          backgroundImage: AssetImage(imagePath),
        ),
        SizedBox(height: 8.h),
        Text(
          title,
          style: TextUtil.base.sp(12).medium,
        ),
      ],
    );
  }

  Widget _buildRecommendedArticles() {
    return SizedBox(
      height: 240.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(left: 24.w),
        itemCount: 3,
        itemBuilder: (context, index) {
          return _buildArticleCard();
        },
      ),
    );
  }

  Widget _buildArticleCard() {
    return Container(
      width: 160.w,
      margin: EdgeInsets.only(right: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: Image.asset(
              'images/placeholder.png', // Replace with actual image
              height: 160.h,
              width: 160.w,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '孕后抑郁症到底是什么？',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextUtil.base.sp(14).semiBold,
          ),
          SizedBox(height: 4.h),
          Text(
            '常见误区与产后抑郁症的真实面貌',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextUtil.base.sp(12).grey,
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationGrid() {
    final List<Map<String, dynamic>> recommendations = [
      {
        'image': 'images/placeholder.png',
        'title': '找到了这个完美的地方！',
        'userImage': 'images/icon/apple.png',
        'userName': '用户 111',
        'height': 180.0,
      },
      {
        'image': 'images/placeholder.png',
        'title': '好喜欢这里的刘老师，孕期控制体重绝了',
        'userImage': 'images/icon/apple.png',
        'userName': '用户 444',
        'height': 180.0,
      },
      {
        'image': 'images/placeholder.png',
        'title': '孕期瑜伽好去处',
        'userImage': 'images/icon/apple.png',
        'userName': '用户 234',
        'height': 180.0,
      },
      {
        'image': 'images/placeholder.png',
        'title': '这里的母婴用品超全！',
        'userImage': 'images/icon/apple.png',
        'userName': '用户 567',
        'height': 180.0,
      },
    ];

    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      sliver: SliverWaterfallFlow(
        gridDelegate: SliverWaterfallFlowDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16.w,
          mainAxisSpacing: 16.h,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final item = recommendations[index];
            return PostCard(
              image: item['image']!,
              title: item['title']!,
              userImage: item['userImage']!,
              userName: item['userName']!,
              imageHeight: item['height']!,
            );
          },
          childCount: recommendations.length,
        ),
      ),
    );
  }
}

class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  _SliverHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverHeaderDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight || minHeight != oldDelegate.minHeight || child != oldDelegate.child;
  }
} 