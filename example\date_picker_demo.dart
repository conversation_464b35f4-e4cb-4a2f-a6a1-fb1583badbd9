import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import '../lib/widgets/chinese_date_picker.dart';
import '../lib/utils/text_util.dart';

/// 日期选择器演示页面
/// 展示新的中文日期选择器的各种功能
class DatePickerDemo extends StatefulWidget {
  const DatePickerDemo({super.key});

  @override
  State<DatePickerDemo> createState() => _DatePickerDemoState();
}

class _DatePickerDemoState extends State<DatePickerDemo> {
  DateTime selectedDate = DateTime.now();
  String displayText = '';

  @override
  void initState() {
    super.initState();
    _updateDisplayText();
  }

  void _updateDisplayText() {
    displayText = '${selectedDate.year}年${selectedDate.month}月${selectedDate.day}日';
  }

  void _showDatePicker() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => DatePickerBottomSheet(
        initialDate: selectedDate,
        color: CupertinoColors.systemBlue,
        bgColor: CupertinoColors.systemGrey6,
        title: '选择日期',
        minimumDate: DateTime(2020),
        maximumDate: DateTime(2030),
        onDateSelected: (date) {
          setState(() {
            selectedDate = date;
            _updateDisplayText();
          });
        },
      ),
    );
  }

  void _showLeapYearDemo() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => DatePickerBottomSheet(
        initialDate: DateTime(2024, 2, 29), // 闰年2月29日
        color: CupertinoColors.systemGreen,
        bgColor: CupertinoColors.systemGrey6,
        title: '闰年演示',
        minimumDate: DateTime(2020),
        maximumDate: DateTime(2030),
        onDateSelected: (date) {
          setState(() {
            selectedDate = date;
            _updateDisplayText();
          });
        },
      ),
    );
  }

  void _showMonthChangeDemo() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => DatePickerBottomSheet(
        initialDate: DateTime(2025, 1, 31), // 1月31日，切换到2月会自动调整
        color: CupertinoColors.systemOrange,
        bgColor: CupertinoColors.systemGrey6,
        title: '月份切换演示',
        minimumDate: DateTime(2020),
        maximumDate: DateTime(2030),
        onDateSelected: (date) {
          setState(() {
            selectedDate = date;
            _updateDisplayText();
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      child: CupertinoApp(
        home: CupertinoPageScaffold(
          navigationBar: const CupertinoNavigationBar(
            middle: Text('中文日期选择器演示'),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.all(24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 当前选中日期显示
                  Container(
                    padding: EdgeInsets.all(20.w),
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemGrey6,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Column(
                      children: [
                        Text(
                          '当前选中日期',
                          style: TextUtil.base
                              .sp(16)
                              .withColor(CupertinoColors.systemGrey),
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          displayText,
                          style: TextUtil.base
                              .sp(24)
                              .semiBold
                              .withColor(CupertinoColors.systemBlue),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 32.h),

                  // 功能演示按钮
                  Text(
                    '功能演示',
                    style: TextUtil.base
                        .sp(20)
                        .semiBold
                        .withColor(CupertinoColors.label),
                  ),

                  SizedBox(height: 16.h),

                  // 基础日期选择
                  _buildDemoButton(
                    title: '基础日期选择',
                    subtitle: '选择任意日期',
                    color: CupertinoColors.systemBlue,
                    onTap: _showDatePicker,
                  ),

                  SizedBox(height: 12.h),

                  // 闰年演示
                  _buildDemoButton(
                    title: '闰年演示',
                    subtitle: '2024年2月29日（闰年）',
                    color: CupertinoColors.systemGreen,
                    onTap: _showLeapYearDemo,
                  ),

                  SizedBox(height: 12.h),

                  // 月份切换演示
                  _buildDemoButton(
                    title: '智能日期调整',
                    subtitle: '从1月31日切换到2月会自动调整',
                    color: CupertinoColors.systemOrange,
                    onTap: _showMonthChangeDemo,
                  ),

                  SizedBox(height: 32.h),

                  // 功能特点说明
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemGrey6,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '功能特点',
                          style: TextUtil.base
                              .sp(16)
                              .semiBold
                              .withColor(CupertinoColors.label),
                        ),
                        SizedBox(height: 8.h),
                        _buildFeatureItem('✅ 实时更新天数列表'),
                        _buildFeatureItem('✅ 智能日期调整'),
                        _buildFeatureItem('✅ 防抖处理优化'),
                        _buildFeatureItem('✅ 状态同步保证'),
                        _buildFeatureItem('✅ 闰年自动处理'),
                        _buildFeatureItem('✅ 中文显示界面'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDemoButton({
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextUtil.base
                        .sp(16)
                        .semiBold
                        .withColor(color),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    subtitle,
                    style: TextUtil.base
                        .sp(14)
                        .withColor(CupertinoColors.systemGrey),
                  ),
                ],
              ),
            ),
            Icon(
              CupertinoIcons.chevron_right,
              color: color,
              size: 20.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Text(
        text,
        style: TextUtil.base
            .sp(14)
            .withColor(CupertinoColors.systemGrey),
      ),
    );
  }
}
