import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('NotifyMonthChanged Optimization Tests', () {
    testWidgets('Should not call onMonthChanged excessively during year view month click', (WidgetTester tester) async {
      final testDate = DateTime(2024, 7, 15); // selectedDay在7月
      int monthChangeCallCount = 0;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthChangeHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      monthChangeCallCount++;
                      currentMonthTitle = month;
                      monthChangeHistory.add(month);
                      print('onMonthChanged called #$monthChangeCallCount: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 记录初始调用次数
      final initialCallCount = monthChangeCallCount;
      print('Initial call count: $initialCallCount');
      
      // 清空历史记录
      monthChangeHistory.clear();
      monthChangeCallCount = 0;
      
      // 在年视图中点击"二月"
      final februaryFinder = find.text('二月');
      expect(februaryFinder, findsOneWidget);
      
      await tester.tap(februaryFinder);
      await tester.pumpAndSettle();
      
      // 验证切换到月视图
      expect(currentFormat, equals(CustomCalendarFormat.month));
      expect(currentMonthTitle, contains('2024年2月'));
      
      // 关键验证：onMonthChanged应该只被调用一次（或最多两次）
      print('Month change call count after year view click: $monthChangeCallCount');
      print('Month change history: $monthChangeHistory');
      
      expect(monthChangeCallCount, lessThanOrEqualTo(2), 
        reason: 'onMonthChanged should not be called excessively during year view month click');
      
      // 验证最终状态正确
      expect(currentMonthTitle, contains('2024年2月'));
    });

    testWidgets('Should not call onMonthChanged excessively during day selection', (WidgetTester tester) async {
      final testDate = DateTime(2024, 5, 15); // selectedDay在5月
      int monthChangeCallCount = 0;
      String? currentMonthTitle;
      List<String> monthChangeHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      monthChangeCallCount++;
                      currentMonthTitle = month;
                      monthChangeHistory.add(month);
                      print('onMonthChanged called #$monthChangeCallCount: $month');
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 清空历史记录
      monthChangeHistory.clear();
      monthChangeCallCount = 0;
      
      // 在月视图中点击一个日期
      final dayFinder20 = find.text('20');
      if (tester.any(dayFinder20)) {
        await tester.tap(dayFinder20);
        await tester.pumpAndSettle();
        
        // 验证onMonthChanged调用次数合理（应该是0或1次，因为在同一个月内）
        print('Month change call count after day selection: $monthChangeCallCount');
        print('Month change history: $monthChangeHistory');
        
        expect(monthChangeCallCount, lessThanOrEqualTo(1), 
          reason: 'onMonthChanged should not be called excessively during day selection in same month');
        
        // 验证月份标题没有改变（仍然是5月）
        expect(currentMonthTitle, contains('2024年5月'));
      }
    });

    testWidgets('Should call onMonthChanged appropriately during page navigation', (WidgetTester tester) async {
      final testDate = DateTime(2024, 3, 15); // selectedDay在3月
      int monthChangeCallCount = 0;
      String? currentMonthTitle;
      List<String> monthChangeHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      monthChangeCallCount++;
                      currentMonthTitle = month;
                      monthChangeHistory.add(month);
                      print('onMonthChanged called #$monthChangeCallCount: $month');
                    },
                    initialFormat: CustomCalendarFormat.month,
                    swipeDirection: SwipeDirection.vertical,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 清空历史记录
      monthChangeHistory.clear();
      monthChangeCallCount = 0;
      
      // 模拟滑动到下个月
      final pageViewFinder = find.byType(PageView);
      if (tester.any(pageViewFinder)) {
        await tester.drag(pageViewFinder, Offset(0, -200)); // 向上滑动
        await tester.pumpAndSettle();
        
        // 验证onMonthChanged被调用了一次
        print('Month change call count after page swipe: $monthChangeCallCount');
        print('Month change history: $monthChangeHistory');
        
        expect(monthChangeCallCount, equals(1), 
          reason: 'onMonthChanged should be called exactly once during page navigation');
        
        // 验证切换到了4月
        expect(currentMonthTitle, contains('2024年4月'));
      }
    });

    testWidgets('Should minimize onMonthChanged calls during complex operations', (WidgetTester tester) async {
      final testDate = DateTime(2024, 8, 10); // selectedDay在8月
      int monthChangeCallCount = 0;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthChangeHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      monthChangeCallCount++;
                      currentMonthTitle = month;
                      monthChangeHistory.add(month);
                      print('onMonthChanged called #$monthChangeCallCount: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 清空历史记录
      monthChangeHistory.clear();
      monthChangeCallCount = 0;
      
      // 执行复杂操作：年视图点击月份 + 日期选择
      final marchFinder = find.text('三月');
      if (tester.any(marchFinder)) {
        // 1. 点击年视图中的三月
        await tester.tap(marchFinder);
        await tester.pumpAndSettle();
        
        final afterYearClickCount = monthChangeCallCount;
        
        // 2. 在三月视图中选择一个日期
        final dayFinder25 = find.text('25');
        if (tester.any(dayFinder25)) {
          await tester.tap(dayFinder25);
          await tester.pumpAndSettle();
        }
        
        final finalCallCount = monthChangeCallCount;
        
        print('Call count after year click: $afterYearClickCount');
        print('Final call count: $finalCallCount');
        print('Total month change history: $monthChangeHistory');
        
        // 验证总调用次数合理（应该不超过3次）
        expect(finalCallCount, lessThanOrEqualTo(3), 
          reason: 'onMonthChanged should not be called excessively during complex operations');
        
        // 验证最终状态正确
        expect(currentMonthTitle, contains('2024年3月'));
        expect(currentFormat, equals(CustomCalendarFormat.month));
      }
    });
  });
}
