import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/swipeable_calendar.dart';
import '../lib/utils/color_util.dart';
import '../lib/utils/text_util.dart';

/// SwipeableCalendar 演示应用
/// 
/// 这个演示展示了新的自定义日历组件的所有功能：
/// 1. 上下滑动切换月份
/// 2. 三种视图模式：周视图、月视图、年视图
/// 3. 拖拽手势切换视图
/// 4. 日期选择和高亮
/// 5. 与原有table_calendar完全兼容的API
class SwipeableCalendarDemo extends StatefulWidget {
  @override
  _SwipeableCalendarDemoState createState() => _SwipeableCalendarDemoState();
}

class _SwipeableCalendarDemoState extends State<SwipeableCalendarDemo> {
  DateTime _selectedDay = DateTime.now();
  String _currentMonthTitle = '';
  CustomCalendarFormat _currentViewFormat = CustomCalendarFormat.week;
  SwipeDirection _currentSwipeDirection = SwipeDirection.vertical;

  @override
  void initState() {
    super.initState();
    _updateMonthTitle();
  }

  void _updateMonthTitle() {
    setState(() {
      _currentMonthTitle = '${_selectedDay.year}年${_selectedDay.month}月';
    });
  }

  void _onDaySelected(DateTime selectedDay) {
    setState(() {
      _selectedDay = selectedDay;
    });
    _updateMonthTitle();
    print('选中日期: ${selectedDay.year}-${selectedDay.month}-${selectedDay.day}');
  }

  void _onMonthChanged(String month) {
    setState(() {
      _currentMonthTitle = month;
    });
    print('月份变化: $month');
  }

  void _onViewChanged(CustomCalendarFormat format) {
    setState(() {
      _currentViewFormat = format;
    });
    print('视图变化: $format');
  }

  String _getViewModeText() {
    switch (_currentViewFormat) {
      case CustomCalendarFormat.week:
        return '周视图';
      case CustomCalendarFormat.month:
        return '月视图';
      case CustomCalendarFormat.year:
        return '年视图';
    }
  }

  String _getSwipeDirectionText() {
    switch (_currentSwipeDirection) {
      case SwipeDirection.vertical:
        return '垂直滑动';
      case SwipeDirection.horizontal:
        return '水平滑动';
      case SwipeDirection.both:
        return '双向滑动';
    }
  }

  void _toggleSwipeDirection() {
    setState(() {
      switch (_currentSwipeDirection) {
        case SwipeDirection.vertical:
          _currentSwipeDirection = SwipeDirection.horizontal;
          break;
        case SwipeDirection.horizontal:
          _currentSwipeDirection = SwipeDirection.both;
          break;
        case SwipeDirection.both:
          _currentSwipeDirection = SwipeDirection.vertical;
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(393, 852),
      builder: (context, child) {
        return CupertinoApp(
          title: 'SwipeableCalendar Demo',
          theme: CupertinoThemeData(
            primaryColor: ColorUtil.primaryBgColor,
          ),
          home: CupertinoPageScaffold(
            backgroundColor: CupertinoColors.white,
            navigationBar: CupertinoNavigationBar(
              middle: Text(
                'SwipeableCalendar 演示',
                style: TextUtil.base.sp(18).semiBold,
              ),
              backgroundColor: CupertinoColors.white,
            ),
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.all(24.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题和状态信息
                    _buildHeader(),
                    SizedBox(height: 24.h),
                    
                    // 日历组件
                    if (_currentViewFormat == CustomCalendarFormat.year)
                      Expanded(
                        child: SwipeableCalendar(
                          selectedDay: _selectedDay,
                          onDaySelected: _onDaySelected,
                          onMonthChanged: _onMonthChanged,
                          onViewChanged: _onViewChanged,
                          initialFormat: _currentViewFormat,
                          swipeDirection: _currentSwipeDirection,
                        ),
                      )
                    else
                      SwipeableCalendar(
                        selectedDay: _selectedDay,
                        onDaySelected: _onDaySelected,
                        onMonthChanged: _onMonthChanged,
                        onViewChanged: _onViewChanged,
                        initialFormat: _currentViewFormat,
                        swipeDirection: _currentSwipeDirection,
                      ),
                    
                    if (_currentViewFormat != CustomCalendarFormat.year) ...[
                      SizedBox(height: 24.h),
                      _buildInstructions(),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _currentMonthTitle,
              style: TextUtil.base.sp(24).bold.withColor(CupertinoColors.black),
            ),
            Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: ColorUtil.primaryBgColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Text(
                    _getViewModeText(),
                    style: TextUtil.base.sp(14).medium.withColor(ColorUtil.primaryBgColor),
                  ),
                ),
                SizedBox(width: 8.w),
                GestureDetector(
                  onTap: _toggleSwipeDirection,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemGrey6,
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Text(
                      _getSwipeDirectionText(),
                      style: TextUtil.base.sp(14).medium.withColor(CupertinoColors.black),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Text(
          '选中日期: ${_selectedDay.year}-${_selectedDay.month.toString().padLeft(2, '0')}-${_selectedDay.day.toString().padLeft(2, '0')}',
          style: TextUtil.base.sp(16).withColor(CupertinoColors.inactiveGray),
        ),
      ],
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '使用说明',
            style: TextUtil.base.sp(16).semiBold.withColor(CupertinoColors.black),
          ),
          SizedBox(height: 8.h),
          _buildInstructionItem('📅', '点击日期进行选择'),
          _buildInstructionItem('👆', '周视图：左右滑动切换周'),
          _buildInstructionItem('📱', '月视图：滑动方向可调（点击标签切换）'),
          _buildInstructionItem('🔄', '拖拽底部横杠切换视图模式'),
          _buildInstructionItem('📆', '支持周视图、月视图、年视图'),
        ],
      ),
    );
  }

  Widget _buildInstructionItem(String icon, String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          Text(
            icon,
            style: TextStyle(fontSize: 16.sp),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              text,
              style: TextUtil.base.sp(14).withColor(CupertinoColors.black),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(SwipeableCalendarDemo());
}
