import 'api_service.dart';
import '../utils/toast_util.dart';
import '../utils/sp_util.dart';

/// API使用示例类
/// 
/// 这个文件展示了如何在实际项目中使用封装的网络请求功能
/// 包含了常见的使用场景和错误处理方式
class ApiExample {
  static final ApiService _apiService = ApiService.instance;

  // ==================== 认证相关示例 ====================

  /// 邮箱密码登录示例
  static Future<bool> loginExample({
    required String email,
    required String password,
  }) async {
    try {
      // 显示加载提示
      ToastUtil.showDataLoading();

      // 调用登录接口
      final response = await _apiService.login(
        email: email,
        password: password,
      );

      if (response.success && response.data != null) {
        // 登录成功
        ToastUtil.showLoginSuccess();

        // 这里可以保存用户信息到本地存储
        // final user = response.data!.toUserModel();
        // final token = response.data!.toAuthTokenModel();
        // await SpUtil.completeLogin(user: user!, token: token);

        return true;
      } else {
        // 登录失败
        ToastUtil.showLoginFailed(response.message);
        return false;
      }
    } catch (e) {
      // 异常处理
      ToastUtil.showError('登录过程中发生错误: $e');
      return false;
    }
  }

  /// 邮箱验证码登录示例
  static Future<bool> loginWithEmailCodeExample({
    required String email,
    required String code,
  }) async {
    try {
      ToastUtil.showDataLoading();

      final response = await _apiService.loginWithEmailCode(
        email: email,
        code: code,
      );

      if (response.success && response.data != null) {
        ToastUtil.showLoginSuccess();
        return true;
      } else {
        ToastUtil.showError(response.message ?? '验证码登录失败');
        return false;
      }
    } catch (e) {
      ToastUtil.showError('验证码登录异常: $e');
      return false;
    }
  }

  /// 发送邮箱验证码示例
  static Future<bool> sendCodeExample(String email) async {
    try {
      final response = await _apiService.sendVerificationCode(
        email: email,
        codeType: 'login',
      );

      if (response.success) {
        ToastUtil.showSuccess('验证码已发送');
        return true;
      } else {
        ToastUtil.showError(response.message ?? '发送验证码失败');
        return false;
      }
    } catch (e) {
      ToastUtil.showError('发送验证码异常: $e');
      return false;
    }
  }

  // ==================== Token验证示例 ====================

  /// 验证Token有效性示例
  static Future<bool> verifyTokenExample() async {
    try {
      final response = await _apiService.verifyToken();

      if (response.success && response.data == true) {
        return true;
      } else {
        ToastUtil.showError('Token已失效，请重新登录');
        return false;
      }
    } catch (e) {
      ToastUtil.showError('Token验证失败: $e');
      return false;
    }
  }

  /// 刷新Token示例
  static Future<bool> refreshTokenExample() async {
    try {
      // 获取当前的refresh token
      final currentToken = await SpUtil.getAuthToken();
      if (currentToken?.refreshToken == null) {
        ToastUtil.showError('没有有效的刷新令牌');
        return false;
      }

      final response = await _apiService.refreshToken(currentToken!.refreshToken!);

      if (response.success && response.data != null) {
        // 保存新的token
        await SpUtil.saveAuthToken(response.data!);
        ToastUtil.showSuccess('Token刷新成功');
        return true;
      } else {
        ToastUtil.showError('Token刷新失败');
        return false;
      }
    } catch (e) {
      ToastUtil.showError('Token刷新异常: $e');
      return false;
    }
  }

  // ==================== 错误处理示例 ====================

  /// 带重试机制的请求示例
  static Future<T?> requestWithRetry<T>(
    Future<ApiResponse<T>> Function() request, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        final response = await request();
        
        if (response.success) {
          return response.data;
        } else if (attempts == maxRetries - 1) {
          // 最后一次尝试失败，显示错误信息
          ToastUtil.showError(response.message ?? '请求失败');
        }
      } catch (e) {
        if (attempts == maxRetries - 1) {
          // 最后一次尝试异常，显示错误信息
          ToastUtil.showError('请求异常: $e');
        }
      }
      
      attempts++;
      if (attempts < maxRetries) {
        await Future.delayed(delay);
      }
    }
    
    return null;
  }

  /// 使用重试机制的示例 - 验证Token
  static Future<bool?> verifyTokenWithRetry() async {
    return await requestWithRetry<bool>(
      () => _apiService.verifyToken(),
      maxRetries: 3,
      delay: const Duration(seconds: 2),
    );
  }
}
