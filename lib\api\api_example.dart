import 'package:flutter/cupertino.dart';
import 'api_service.dart';
import '../utils/toast_util.dart';
import '../models/user_models.dart';

/// API使用示例类
/// 
/// 这个文件展示了如何在实际项目中使用封装的网络请求功能
/// 包含了常见的使用场景和错误处理方式
class ApiExample {
  static final ApiService _apiService = ApiService.instance;

  // ==================== 认证相关示例 ====================

  /// 用户登录示例
  static Future<bool> loginExample({
    required String username,
    required String password,
  }) async {
    try {
      // 显示加载提示
      ToastUtil.showDataLoading();

      // 调用登录接口
      final response = await _apiService.login(
        username: username,
        password: password,
      );

      if (response.success && response.data != null) {
        // 登录成功
        ToastUtil.showLoginSuccess();
        
        // 这里可以保存用户信息到本地存储
        // await SpUtil.saveUserInfo(response.data!);
        
        return true;
      } else {
        // 登录失败
        ToastUtil.showLoginFailed(response.message);
        return false;
      }
    } catch (e) {
      // 异常处理
      ToastUtil.showError('登录过程中发生错误: $e');
      return false;
    }
  }

  /// 验证码登录示例
  static Future<bool> loginWithCodeExample({
    required String phone,
    required String code,
  }) async {
    try {
      ToastUtil.showDataLoading();

      final response = await _apiService.loginWithCode(
        phone: phone,
        code: code,
      );

      if (response.success && response.data != null) {
        ToastUtil.showLoginSuccess();
        return true;
      } else {
        ToastUtil.showError(response.message ?? '验证码登录失败');
        return false;
      }
    } catch (e) {
      ToastUtil.showError('验证码登录异常: $e');
      return false;
    }
  }

  /// 发送验证码示例
  static Future<bool> sendCodeExample(String phone) async {
    try {
      final response = await _apiService.sendVerificationCode(
        phone: phone,
        type: 'login',
      );

      if (response.success) {
        ToastUtil.showSuccess('验证码已发送');
        return true;
      } else {
        ToastUtil.showError(response.message ?? '发送验证码失败');
        return false;
      }
    } catch (e) {
      ToastUtil.showError('发送验证码异常: $e');
      return false;
    }
  }

  // ==================== 用户信息相关示例 ====================

  /// 获取用户信息示例
  static Future<UserModel?> getUserInfoExample() async {
    try {
      final response = await _apiService.getUserInfo();

      if (response.success && response.data != null) {
        return response.data;
      } else {
        ToastUtil.showDataLoadFailed();
        return null;
      }
    } catch (e) {
      ToastUtil.showError('获取用户信息失败: $e');
      return null;
    }
  }

  /// 更新用户信息示例
  static Future<bool> updateUserInfoExample({
    String? nickname,
    String? email,
    String? avatar,
  }) async {
    try {
      ToastUtil.showDataLoading();

      final response = await _apiService.updateUserInfo(
        nickname: nickname,
        email: email,
        avatar: avatar,
      );

      if (response.success) {
        ToastUtil.showSaveSuccess();
        return true;
      } else {
        ToastUtil.showSaveFailed();
        return false;
      }
    } catch (e) {
      ToastUtil.showError('更新用户信息失败: $e');
      return false;
    }
  }

  /// 修改密码示例
  static Future<bool> changePasswordExample({
    required String oldPassword,
    required String newPassword,
  }) async {
    try {
      ToastUtil.showDataLoading();

      final response = await _apiService.changePassword(
        oldPassword: oldPassword,
        newPassword: newPassword,
      );

      if (response.success) {
        ToastUtil.showOperationSuccess('密码修改成功');
        return true;
      } else {
        ToastUtil.showOperationFailed(response.message ?? '密码修改失败');
        return false;
      }
    } catch (e) {
      ToastUtil.showError('修改密码异常: $e');
      return false;
    }
  }

  // ==================== 文件上传示例 ====================

  /// 上传头像示例
  static Future<String?> uploadAvatarExample(String imagePath) async {
    try {
      ToastUtil.showDataLoading();

      final response = await _apiService.uploadImage(
        imagePath: imagePath,
        fileName: 'avatar.jpg',
        onProgress: (sent, total) {
          // 可以在这里更新上传进度
          final progress = (sent / total * 100).toInt();
          print('上传进度: $progress%');
        },
      );

      if (response.success && response.data != null) {
        ToastUtil.showOperationSuccess('头像上传成功');
        return response.data;
      } else {
        ToastUtil.showOperationFailed('头像上传失败');
        return null;
      }
    } catch (e) {
      ToastUtil.showError('头像上传异常: $e');
      return null;
    }
  }

  /// 上传文件示例
  static Future<String?> uploadFileExample(String filePath) async {
    try {
      ToastUtil.showDataLoading();

      final response = await _apiService.uploadFile(
        filePath: filePath,
        onProgress: (sent, total) {
          final progress = (sent / total * 100).toInt();
          print('文件上传进度: $progress%');
        },
      );

      if (response.success && response.data != null) {
        ToastUtil.showOperationSuccess('文件上传成功');
        return response.data;
      } else {
        ToastUtil.showOperationFailed('文件上传失败');
        return null;
      }
    } catch (e) {
      ToastUtil.showError('文件上传异常: $e');
      return null;
    }
  }

  // ==================== 通用请求示例 ====================

  /// 通用GET请求示例
  static Future<List<dynamic>?> getListDataExample() async {
    try {
      final response = await _apiService.get<List<dynamic>>(
        '/api/v1/data/list',
        queryParameters: {
          'page': 1,
          'pageSize': 20,
        },
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.success && response.data != null) {
        return response.data;
      } else {
        ToastUtil.showDataLoadFailed();
        return null;
      }
    } catch (e) {
      ToastUtil.showError('获取列表数据失败: $e');
      return null;
    }
  }

  /// 通用POST请求示例
  static Future<bool> postDataExample(Map<String, dynamic> data) async {
    try {
      ToastUtil.showDataLoading();

      final response = await _apiService.post<bool>(
        '/api/v1/data/create',
        data: data,
        fromJson: (data) => data as bool,
      );

      if (response.success) {
        ToastUtil.showOperationSuccess('数据提交成功');
        return true;
      } else {
        ToastUtil.showOperationFailed(response.message ?? '数据提交失败');
        return false;
      }
    } catch (e) {
      ToastUtil.showError('数据提交异常: $e');
      return false;
    }
  }

  // ==================== 错误处理示例 ====================

  /// 带重试机制的请求示例
  static Future<T?> requestWithRetry<T>(
    Future<ApiResponse<T>> Function() request, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        final response = await request();
        
        if (response.success) {
          return response.data;
        } else if (attempts == maxRetries - 1) {
          // 最后一次尝试失败，显示错误信息
          ToastUtil.showError(response.message ?? '请求失败');
        }
      } catch (e) {
        if (attempts == maxRetries - 1) {
          // 最后一次尝试异常，显示错误信息
          ToastUtil.showError('请求异常: $e');
        }
      }
      
      attempts++;
      if (attempts < maxRetries) {
        await Future.delayed(delay);
      }
    }
    
    return null;
  }

  /// 使用重试机制的示例
  static Future<UserModel?> getUserInfoWithRetry() async {
    return await requestWithRetry<UserModel>(
      () => _apiService.getUserInfo(),
      maxRetries: 3,
      delay: const Duration(seconds: 2),
    );
  }
}
