import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/symptom_bottom_sheets/weight_add_bottom_sheet.dart';

void main() {
  group('WeightAddBottomSheet Tests', () {
    testWidgets('should display weight add bottom sheet correctly', (WidgetTester tester) async {
      // 初始化ScreenUtil
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => WeightAddBottomSheet(
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onWeightAdded: (weight, unit, time) {
                    print('Weight added: $weight$unit at $time');
                  },
                ),
              ),
            ),
          ),
        ),
      );

      // 等待组件渲染完成
      await tester.pumpAndSettle();

      // 验证标题是否正确显示
      expect(find.text('添加体重'), findsOneWidget);

      // 验证输入框是否存在
      expect(find.byType(CupertinoTextField), findsOneWidget);

      // 验证单位选择框是否存在
      expect(find.text('kg'), findsOneWidget);

      // 验证时间选择框是否存在
      expect(find.byIcon(CupertinoIcons.calendar), findsOneWidget);

      // 验证添加按钮是否存在
      expect(find.text('添加'), findsOneWidget);
    });

    testWidgets('should display three input fields in one row', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => WeightAddBottomSheet(
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onWeightAdded: (weight, unit, time) {},
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证三个输入框都存在
      expect(find.text('体重'), findsOneWidget); // 体重输入框的placeholder
      expect(find.text('kg'), findsOneWidget); // 单位选择框
      expect(find.text('选择时间'), findsOneWidget); // 时间选择框的placeholder

      // 验证输入框、单位选择框和时间选择框的图标都存在
      expect(find.byType(CupertinoTextField), findsOneWidget);
      expect(find.byIcon(CupertinoIcons.chevron_down), findsOneWidget);
      expect(find.byIcon(CupertinoIcons.calendar), findsOneWidget);
    });

    testWidgets('should show Chinese date picker', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => WeightAddBottomSheet(
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onWeightAdded: (weight, unit, time) {},
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击时间选择框
      await tester.tap(find.byIcon(CupertinoIcons.calendar));
      await tester.pumpAndSettle();

      // 验证中文日期选择器是否显示
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('确定'), findsOneWidget);

      // 验证年月日的中文标识
      expect(find.textContaining('年'), findsWidgets);
      expect(find.textContaining('月'), findsWidgets);
      expect(find.textContaining('日'), findsWidgets);
    });
  });
}
