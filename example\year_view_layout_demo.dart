import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/swipeable_calendar.dart';

void main() {
  runApp(YearViewLayoutDemo());
}

class YearViewLayoutDemo extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(393, 852),
      builder: (context, child) {
        return CupertinoApp(
          title: 'Year View Layout Demo',
          home: YearViewDemoPage(),
        );
      },
    );
  }
}

class YearViewDemoPage extends StatefulWidget {
  @override
  _YearViewDemoPageState createState() => _YearViewDemoPageState();
}

class _YearViewDemoPageState extends State<YearViewDemoPage> {
  DateTime _selectedDay = DateTime.now();
  String _currentMonthTitle = '';
  CustomCalendarFormat _currentFormat = CustomCalendarFormat.year;
  double _yearViewHeight = 500.0; // 可调节的年视图高度

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text('年视图布局演示'),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 控制面板
            Container(
              padding: EdgeInsets.all(16.w),
              color: CupertinoColors.systemGrey6,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '当前状态',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text('视图模式: ${_getFormatText(_currentFormat)}'),
                  Text('月份标题: $_currentMonthTitle'),
                  Text('选中日期: ${_selectedDay.year}-${_selectedDay.month}-${_selectedDay.day}'),
                  SizedBox(height: 12.h),
                  
                  // 年视图高度调节器
                  Text('年视图高度: ${_yearViewHeight.toInt()}px'),
                  CupertinoSlider(
                    value: _yearViewHeight,
                    min: 300.0,
                    max: 700.0,
                    divisions: 20,
                    onChanged: (value) {
                      setState(() {
                        _yearViewHeight = value;
                      });
                    },
                  ),
                  
                  // 视图切换按钮
                  SizedBox(height: 12.h),
                  Row(
                    children: [
                      _buildFormatButton(CustomCalendarFormat.week, '周视图'),
                      SizedBox(width: 8.w),
                      _buildFormatButton(CustomCalendarFormat.month, '月视图'),
                      SizedBox(width: 8.w),
                      _buildFormatButton(CustomCalendarFormat.year, '年视图'),
                    ],
                  ),
                ],
              ),
            ),
            
            // 日历组件
            Expanded(
              child: Container(
                padding: EdgeInsets.all(16.w),
                child: SwipeableCalendar(
                  selectedDay: _selectedDay,
                  initialFormat: _currentFormat,
                  monthViewHeight: 300.0.h, // 传入经过ScreenUtil处理的月视图高度
                  yearViewHeight: _yearViewHeight.h, // 传入经过ScreenUtil处理的年视图高度
                  onDaySelected: (day) {
                    setState(() {
                      _selectedDay = day;
                    });
                  },
                  onMonthChanged: (month) {
                    setState(() {
                      _currentMonthTitle = month;
                    });
                  },
                  onViewChanged: (format) {
                    setState(() {
                      _currentFormat = format;
                    });
                  },
                ),
              ),
            ),
            
            // 说明文字
            Container(
              padding: EdgeInsets.all(16.w),
              color: CupertinoColors.systemGrey6,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '布局改进说明：',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '• 使用 mainAxisExtent 替代 childAspectRatio，避免内容溢出\n'
                    '• 动态计算每个月份项目的高度，确保布局稳定\n'
                    '• 支持自定义年视图高度，适应不同屏幕尺寸\n'
                    '• 确保所有月份内容完整显示，无截断或滚动条',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormatButton(CustomCalendarFormat format, String text) {
    final isSelected = _currentFormat == format;
    
    return CupertinoButton(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      color: isSelected ? CupertinoColors.activeBlue : CupertinoColors.systemGrey4,
      onPressed: () {
        setState(() {
          _currentFormat = format;
        });
      },
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12.sp,
          color: isSelected ? CupertinoColors.white : CupertinoColors.black,
        ),
      ),
    );
  }

  String _getFormatText(CustomCalendarFormat format) {
    switch (format) {
      case CustomCalendarFormat.week:
        return '周视图';
      case CustomCalendarFormat.month:
        return '月视图';
      case CustomCalendarFormat.year:
        return '年视图';
    }
  }
}
