import 'package:dio/dio.dart';
import '../utils/toast_util.dart';
import '../services/auth_service.dart';
import '../utils/sp_util.dart';

/// Dio客户端封装类，提供统一的网络请求配置
class DioClient {
  static final DioClient _instance = DioClient._internal();
  static DioClient get instance => _instance;

  late Dio _dio;
  
  DioClient._internal() {
    _dio = Dio();
    _setupInterceptors();
  }

  /// 获取Dio实例
  Dio get dio => _dio;

  /// 配置拦截器
  void _setupInterceptors() {
    // 基础配置
    _dio.options = BaseOptions(
      baseUrl: 'https://api.example.com', // 替换为实际的API基础URL
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // 请求拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // 添加认证token
          await _addAuthToken(options);
          
          // 打印请求信息（仅在调试模式下）
          _logRequest(options);
          
          handler.next(options);
        },
        onResponse: (response, handler) {
          // 打印响应信息（仅在调试模式下）
          _logResponse(response);
          
          handler.next(response);
        },
        onError: (error, handler) async {
          // 统一错误处理
          await _handleError(error);
          
          handler.next(error);
        },
      ),
    );
  }

  /// 添加认证token到请求头
  Future<void> _addAuthToken(RequestOptions options) async {
    try {
      final token = await SpUtil.getAuthToken();
      if (token != null && token.accessToken != null && token.accessToken!.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer ${token.accessToken}';
      }
    } catch (e) {
      // Token获取失败，继续请求（可能是不需要认证的接口）
    }
  }

  /// 打印请求信息
  void _logRequest(RequestOptions options) {
    print('🚀 REQUEST: ${options.method} ${options.uri}');
    if (options.data != null) {
      print('📤 DATA: ${options.data}');
    }
    if (options.queryParameters.isNotEmpty) {
      print('🔍 QUERY: ${options.queryParameters}');
    }
  }

  /// 打印响应信息
  void _logResponse(Response response) {
    print('✅ RESPONSE: ${response.statusCode} ${response.requestOptions.uri}');
    print('📥 DATA: ${response.data}');
  }

  /// 统一错误处理
  Future<void> _handleError(DioException error) async {
    String errorMessage = '请求失败';

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        errorMessage = '网络连接超时，请检查网络设置';
        ToastUtil.showNetworkError(errorMessage);
        break;
        
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        switch (statusCode) {
          case 400:
            errorMessage = '请求参数错误';
            break;
          case 401:
            errorMessage = '认证失败，请重新登录';
            await _handleUnauthorized();
            break;
          case 403:
            errorMessage = '权限不足';
            ToastUtil.showPermissionError(errorMessage);
            return;
          case 404:
            errorMessage = '请求的资源不存在';
            break;
          case 500:
            errorMessage = '服务器内部错误';
            ToastUtil.showServerError(errorMessage);
            return;
          case 502:
          case 503:
          case 504:
            errorMessage = '服务器暂时不可用，请稍后重试';
            ToastUtil.showServerError(errorMessage);
            return;
          default:
            errorMessage = '服务器错误 ($statusCode)';
            ToastUtil.showServerError(errorMessage);
            return;
        }
        ToastUtil.showError(errorMessage);
        break;
        
      case DioExceptionType.cancel:
        // 请求被取消，不显示错误提示
        return;
        
      case DioExceptionType.connectionError:
        errorMessage = '网络连接失败，请检查网络设置';
        ToastUtil.showNetworkError(errorMessage);
        break;
        
      default:
        errorMessage = '网络请求异常';
        ToastUtil.showError(errorMessage);
        break;
    }

    print('❌ ERROR: ${error.type} - $errorMessage');
    if (error.response != null) {
      print('📥 ERROR DATA: ${error.response?.data}');
    }
  }

  /// 处理401未授权错误
  Future<void> _handleUnauthorized() async {
    try {
      // 清除本地认证信息
      await AuthService.instance.logout();
      
      // 显示登录失败提示
      ToastUtil.showLoginFailed('登录已过期，请重新登录');
      
      // 这里可以添加跳转到登录页面的逻辑
      // 由于需要BuildContext，建议在具体的页面中处理跳转
    } catch (e) {
      print('处理未授权错误时发生异常: $e');
    }
  }

  /// 更新基础URL
  void updateBaseUrl(String baseUrl) {
    _dio.options.baseUrl = baseUrl;
  }

  /// 设置超时时间
  void setTimeout({
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
  }) {
    if (connectTimeout != null) {
      _dio.options.connectTimeout = connectTimeout;
    }
    if (receiveTimeout != null) {
      _dio.options.receiveTimeout = receiveTimeout;
    }
    if (sendTimeout != null) {
      _dio.options.sendTimeout = sendTimeout;
    }
  }

  /// 添加自定义拦截器
  void addInterceptor(Interceptor interceptor) {
    _dio.interceptors.add(interceptor);
  }

  /// 清除所有拦截器并重新设置
  void resetInterceptors() {
    _dio.interceptors.clear();
    _setupInterceptors();
  }
}
