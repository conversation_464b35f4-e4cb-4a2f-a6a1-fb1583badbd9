import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/widgets/app_button.dart';
import 'package:align/widgets/swipeable_calendar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import 'symptom_page.dart';
import 'add_todo_page.dart';
import 'note_page.dart';
import 'prenatal_checkup_list_page.dart';

class CalendarPage extends StatefulWidget {
  const CalendarPage({super.key});

  @override
  State<CalendarPage> createState() => _CalendarPageState();
}

class _CalendarPageState extends State<CalendarPage>
    with TickerProviderStateMixin {
  final Color _card1BgColor = const Color(0xFFF5F5F5);
  final Color _card2Color = const Color(0xFFFE2C55);
  final Color _card2BgColor = const Color(0xFFFFE3E3);
  final Color _card3Color = const Color(0xFF2C8EFE);
  final Color _card3BgColor = const Color(0xFFE3F1FF);
  final Color _card4Color = const Color(0xFFFEC62C);
  final Color _card4BgColor = const Color(0xFFFFF9E3);

  final Color _progressColor1 = const Color(0xFFFFC4C4);
  final Color _progressColor2 = const Color(0xFF2C8EFE);
  final Color _progressColor3 = const Color(0xFFFEC62C);

  String _currentMonthTitle = '';
  DateTime _selectedDay = DateTime.now();
  CustomCalendarFormat _currentViewFormat = CustomCalendarFormat.week;

  /// 周视角日历高度
  final double _monthViewHeight = 250.h;

  /// 年视角日历高度
  final double _yearViewHeight = 600.h;

  late PageController _pageController;
  int _currentPageIndex = 0;

  // 添加菜单相关状态
  bool _isMenuOpen = false;
  late AnimationController _menuAnimationController;
  late AnimationController _rotationAnimationController;
  late Animation<double> _menuAnimation;
  late Animation<double> _rotationAnimation;

  // 用于获取原始+号按钮位置的GlobalKey
  final GlobalKey _addButtonKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _updateMonthTitle(DateTime.now());

    // 初始化动画控制器
    _menuAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _rotationAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 初始化动画
    _menuAnimation = CurvedAnimation(
      parent: _menuAnimationController,
      curve: Curves.easeInOut,
    );
    _rotationAnimation =
        Tween<double>(
          begin: 0.0,
          end: 0.125, // 45度 = 45/360 = 0.125
        ).animate(
          CurvedAnimation(
            parent: _rotationAnimationController,
            curve: Curves.easeInOut,
          ),
        );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _menuAnimationController.dispose();
    _rotationAnimationController.dispose();
    super.dispose();
  }

  void _updateMonthTitle(DateTime date) {
    setState(() {
      _currentMonthTitle = '${date.year}年${date.month}月';
    });
  }

  void _onDaySelected(DateTime selectedDay) {
    setState(() {
      _selectedDay = selectedDay;
    });
  }

  void _onMonthChanged(String monthTitle) {
    setState(() {
      _currentMonthTitle = monthTitle;
    });
  }

  void _onViewChanged(CustomCalendarFormat format) {
    setState(() {
      _currentViewFormat = format;
    });
  }

  void _toggleMenu() {
    if (_isMenuOpen) {
      // 关闭菜单：先执行动画，再更新状态
      _menuAnimationController.reverse();
      _rotationAnimationController.reverse();
      // 延迟更新状态，确保动画完成后再隐藏遮罩层
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            _isMenuOpen = false;
          });
        }
      });
    } else {
      // 打开菜单：先更新状态，再执行动画
      setState(() {
        _isMenuOpen = true;
      });
      // 确保widget渲染完成后再开始动画
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _menuAnimationController.forward();
        _rotationAnimationController.forward();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.white,
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: _buildHeader(),
            ),
            SizedBox(height: 24.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: SwipeableCalendar(
                selectedDay: _selectedDay,
                onDaySelected: _onDaySelected,
                onMonthChanged: _onMonthChanged,
                onViewChanged: _onViewChanged,
                initialFormat: _currentViewFormat,
                monthViewHeight: _monthViewHeight,
                yearViewHeight: _yearViewHeight,
              ),
            ),
            SizedBox(height: 12.h),
            Expanded(
              child: _currentViewFormat == CustomCalendarFormat.year
                  ? Container() // 年视图时显示空容器
                  : _currentViewFormat == CustomCalendarFormat.month
                      ? _buildMonthViewCards()
                      : Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24.w),
                        child: _buildDefaultCards(),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultCards() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildPregnancyCard(),
          SizedBox(height: 16.h),
          _buildTodoCard(),
          SizedBox(height: 16.h),
          _buildSymptomCard(),
          SizedBox(height: 16.h),
          _buildDiaryCard(),
          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  Widget _buildMonthViewCards() {
    return Column(
      children: [
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPageIndex = index;
              });
            },
            children: [
              _buildPregnancyCard(),
              _buildTodoCard(),
              _buildSymptomCard(),
              _buildDiaryCard(),
            ].map(
              (card) => Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: card,
              ),
            ).toList(),
          ),
        ),
        SizedBox(height: 16.h),
        _buildPageIndicator(),
        SizedBox(height: 24.h),
      ],
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(4, (index) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: 8.r,
          height: 8.r,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentPageIndex == index
                ? _card2Color
                : ColorUtil.borderGrey,
          ),
        );
      }),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(_currentMonthTitle, style: TextUtil.base.sp(24).semiBold),
            Row(
              children: [
                AppButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      CupertinoPageRoute(
                        builder: (context) => const PrenatalCheckupListPage(),
                      ),
                    );
                  },
                  child: ImageIcon(
                    AssetImage('images/pages/calendar/stethoscope.png'),
                    size: 24.sp,
                    color: CupertinoColors.black,
                  ),
                ),
                SizedBox(width: 16.w),
                AppButton(
                  onPressed: () {},
                  child: Icon(
                    CupertinoIcons.search,
                    size: 24.sp,
                    color: CupertinoColors.black,
                  ),
                ),
                SizedBox(width: 16.w),
                // 使用Portal的+号按钮
                PortalTarget(
                  visible: _isMenuOpen,
                  portalFollower: _buildPortalOverlay(),
                  child: Container(
                    key: _addButtonKey,
                    child: AppButton(
                      onPressed: _toggleMenu,
                      child: AnimatedBuilder(
                        animation: _rotationAnimation,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _rotationAnimation.value * 2 * 3.14159,
                            child: Icon(
                              CupertinoIcons.add,
                              size: 24.sp,
                              color: Color.lerp(
                                CupertinoColors.black,
                                CupertinoColors.white,
                                _menuAnimation.value,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPregnancyCard() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      decoration: BoxDecoration(
        color: _card1BgColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          // 顶部进度条
          _buildProgressBar(),

          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              children: [
                // 标题和倒计时区域
                Container(
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  decoration: BoxDecoration(
                    color: CupertinoColors.white,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 20.w,
                              vertical: 5.h,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(25.r),
                              border: Border.all(
                                color: ColorUtil.primaryBgColor,
                                width: 1.w,
                              ),
                            ),
                            child: Text(
                              '孕中期',
                              style: TextUtil.base.sp(24).semiBold,
                            ),
                          ),
                          SizedBox(height: 6.h),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 10.w,
                              vertical: 5.h,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(25.r),
                              border: Border.all(
                                color: ColorUtil.primaryBgColor,
                                width: 1.w,
                              ),
                            ),
                            child: Text(
                              '第15周 第3天',
                              style: TextUtil.base.sp(14).medium,
                            ),
                          ),
                        ],
                      ),

                      SizedBox(width: 40.w),

                      Column(
                        children: [
                          Text('倒计时', style: TextUtil.base.sp(14).semiBold),
                          SizedBox(height: 8.h),

                          Row(
                            children: [
                              Column(
                                children: [
                                  Row(
                                    children: [
                                      _buildCountdownNumber('2'),
                                      SizedBox(width: 1.w),
                                      _buildCountdownNumber('5'),
                                    ],
                                  ),
                                  SizedBox(height: 4.h),
                                  Text('周', style: TextUtil.base.sp(11).medium),
                                ],
                              ),
                              SizedBox(width: 8.w),

                              Column(
                                children: [
                                  _buildCountdownNumber('4'),
                                  SizedBox(height: 4.h),
                                  Text('天', style: TextUtil.base.sp(11).medium),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 12.h),

                // 宝宝与母体信息概览
                _buildBabyAndMotherInfo(),

                SizedBox(height: 12.h),

                // 底部按钮
                Row(
                  children: [
                    Expanded(child: _buildActionButton('关于宝宝')),
                    SizedBox(width: 12.w),
                    Expanded(child: _buildActionButton('关于自己')),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    // 孕期进度：30周(孕早期) + 37.5周(孕中期) + 32.5周(孕晚期) = 100周总长度
    // 实际孕期40周，所以比例为 30/100, 37.5/100, 32.5/100
    // 当前在第15周，属于孕中期，在孕早期30周后的第15周位置
    // 所以当前位置 = (30 + 15) / 100 = 45%
    const double heartPosition = 0.2; // 爱心位置45%，可以从0到1之间变化

    // 根据爱心位置动态生成渐变颜色和停止点
    List<Color> gradientColors = [];
    List<double> gradientStops = [];

    // 定义三个阶段的边界
    const double stage1End = 0.3; // 孕早期结束 (30%)
    const double stage2End = 0.675; // 孕中期结束 (67.5%)

    // 根据爱心位置所在的阶段，构建渐变
    if (heartPosition <= stage1End) {
      // 爱心在孕早期
      gradientColors = [
        _progressColor1, // 孕早期开始 (原色)
        _progressColor1, // 爱心位置前 (原色)
        _progressColor1.withValues(alpha: 0.2), // 爱心位置后 (20%透明度)
        _progressColor1.withValues(alpha: 0.2), // 孕早期结束 (20%透明度)
        _progressColor2.withValues(alpha: 0.2), // 孕中期 (20%透明度)
        _progressColor2.withValues(alpha: 0.2), // 孕中期结束 (20%透明度)
        _progressColor3.withValues(alpha: 0.2), // 孕晚期 (20%透明度)
        _progressColor3.withValues(alpha: 0.2), // 孕晚期结束 (20%透明度)
      ];
      gradientStops = [
        0.0,
        heartPosition,
        heartPosition,
        stage1End,
        stage1End,
        stage2End,
        stage2End,
        1.0,
      ];
    } else if (heartPosition <= stage2End) {
      // 爱心在孕中期
      gradientColors = [
        _progressColor1, // 孕早期 (原色)
        _progressColor1, // 孕早期结束 (原色)
        _progressColor2, // 孕中期开始 (原色)
        _progressColor2, // 爱心位置前 (原色)
        _progressColor2.withValues(alpha: 0.2), // 爱心位置后 (20%透明度)
        _progressColor2.withValues(alpha: 0.2), // 孕中期结束 (20%透明度)
        _progressColor3.withValues(alpha: 0.2), // 孕晚期 (20%透明度)
        _progressColor3.withValues(alpha: 0.2), // 孕晚期结束 (20%透明度)
      ];
      gradientStops = [
        0.0,
        stage1End,
        stage1End,
        heartPosition,
        heartPosition,
        stage2End,
        stage2End,
        1.0,
      ];
    } else {
      // 爱心在孕晚期
      gradientColors = [
        _progressColor1, // 孕早期 (原色)
        _progressColor1, // 孕早期结束 (原色)
        _progressColor2, // 孕中期 (原色)
        _progressColor2, // 孕中期结束 (原色)
        _progressColor3, // 孕晚期开始 (原色)
        _progressColor3, // 爱心位置前 (原色)
        _progressColor3.withValues(alpha: 0.2), // 爱心位置后 (20%透明度)
        _progressColor3.withValues(alpha: 0.2), // 孕晚期结束 (20%透明度)
      ];
      gradientStops = [
        0.0,
        stage1End,
        stage1End,
        stage2End,
        stage2End,
        heartPosition,
        heartPosition,
        1.0,
      ];
    }

    return Stack(
      children: [
        Container(
          height: 8.h,
          margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(4.r)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              gradient: LinearGradient(
                colors: gradientColors,
                stops: gradientStops,
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
            ),
          ),
        ),
        Positioned(
          left:
              20.w +
              (1.sw - 2 * 20.w - 2 * 24.w) * heartPosition -
              10.r, // 考虑进度条边距和爱心大小
          top: 12.h + 4.h - 10.r, // 垂直居中：margin top + (进度条高度/2) - (图标大小/2)
          child: Icon(
            CupertinoIcons.suit_heart_fill,
            size: 20.r,
            color: CupertinoColors.systemRed,
          ),
        ),
      ],
    );
  }

  Widget _buildCountdownNumber(String number) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: const Color(0xFFD9D9D9),
        borderRadius: BorderRadius.circular(3.r),
      ),
      child: Center(
        child: Text(
          number,
          style: TextUtil.base.sp(16).bold.withColor(Color(0xFF4A5660)),
        ),
      ),
    );
  }

  Widget _buildBabyAndMotherInfo() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: ColorUtil.primaryBgColor, width: 1.w),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧：宝宝大小示意
          Column(
            children: [
              Text(
                '宝宝这周有\n这么大了',
                style: TextUtil.base.sp(14).semiBold,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 12.h),
              // 拟人化草莓卡通形象
              Center(
                child: Text('🍓', style: TextStyle(fontSize: 40.sp)),
              ),
            ],
          ),

          SizedBox(width: 20.w),

          // 右侧：本周概括
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: _card2BgColor,
              ),
              child: Column(
                children: [
                  Text('这周概括', style: TextUtil.base.sp(14).semiBold),
                  SizedBox(height: 8.h),
                  Text(
                    '怀孕15周时，胎儿的四肢已能活动，毛发开始生长，孕妇可能会感到腹部轻微隆起，孕吐反应也可能逐渐减轻。',
                    style: TextUtil.base.sp(12),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String text) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: ColorUtil.primaryBgColor, width: 1.w),
      ),
      child: Center(child: Text(text, style: TextUtil.base.sp(14).semiBold)),
    );
  }

  Widget _buildTodoCard() {
    return _buildCard(
      color: _card2BgColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 38.r,
                height: 38.r,
                decoration: BoxDecoration(
                  color: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(999.r),
                ),
                child: ImageIcon(
                  AssetImage('images/pages/calendar/card2.png'),
                  size: 20.sp,
                  color: _card2Color,
                ),
              ),
              SizedBox(width: 8.w),
              Text('要做的事', style: TextUtil.base.sp(18).semiBold),
            ],
          ),
          SizedBox(height: 16.h),
          _buildTodoItem('看医生', '北京第三医院', '09:00', '10:30'),
          SizedBox(height: 12.h),
          _buildTodoItem('去领礼物', '别忘了去这里。。。', '18:00', '18:30'),
          SizedBox(height: 12.h),
          _buildTodoItem('吃药', '2颗药 + 3mg', '21:00', ''),
        ],
      ),
    );
  }

  Widget _buildTodoItem(
    String title,
    String subtitle,
    String startTime,
    String endTime,
  ) {
    return Row(
      children: [
        Container(width: 4.w, height: 40.h, color: const Color(0xFFFF7D7D)),
        SizedBox(width: 12.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: TextUtil.base.sp(16).medium),
            SizedBox(height: 2.h),
            Text(subtitle, style: TextUtil.base.sp(12).grey),
          ],
        ),
        const Spacer(),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(startTime, style: TextUtil.base.sp(12).black),
            SizedBox(height: 2.h),
            if (endTime.isNotEmpty)
              Text(endTime, style: TextUtil.base.sp(12).grey),
          ],
        ),
      ],
    );
  }

  Widget _buildSymptomCard() {
    return _buildCard(
      color: _card3BgColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 38.r,
                height: 38.r,
                decoration: BoxDecoration(
                  color: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(999.r),
                ),
                child: ImageIcon(
                  AssetImage('images/pages/calendar/card3.png'),
                  size: 20.sp,
                  color: _card3Color,
                ),
              ),
              SizedBox(width: 8.w),
              Text('症状', style: TextUtil.base.sp(18).semiBold),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  children: [
                    _buildSymptomItem('心情', '开心\n冷静', CupertinoIcons.smiley),
                    SizedBox(height: 12.h),
                    _buildSymptomItem('睡眠质量', '8小时', CupertinoIcons.moon),
                    SizedBox(height: 12.h),
                    _buildSymptomItem(
                      '饮食',
                      '300g 水果\n500g 蔬菜\n100g 粗品',
                      CupertinoIcons.tuningfork,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  children: [
                    _buildSymptomItem(
                      '运动',
                      '1000m 游泳\n45分钟 散步',
                      CupertinoIcons.sportscourt,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSymptomItem(String title, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(width: 4.w, height: 24.h, color: _card3Color),
            SizedBox(width: 8.w),
            Icon(icon, size: 20.sp, color: _card3Color),
          ],
        ),
        SizedBox(width: 8.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: TextUtil.base.sp(16).medium),
            SizedBox(height: 4.h),
            Text(value, style: TextUtil.base.sp(12).grey),
          ],
        ),
      ],
    );
  }

  Widget _buildDiaryCard() {
    return _buildCard(
      color: _card4BgColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 38.r,
                height: 38.r,
                decoration: BoxDecoration(
                  color: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(999.r),
                ),
                child: ImageIcon(
                  AssetImage('images/pages/calendar/card4.png'),
                  size: 20.sp,
                  color: _card4Color,
                ),
              ),
              SizedBox(width: 8.w),
              Text('日记', style: TextUtil.base.sp(18).semiBold),
            ],
          ),
          SizedBox(height: 16.h),
          _buildDiaryItem('小小想法😌', '一些今天的感受和想法记录下来...', '8:28 AM'),
          SizedBox(height: 12.h),
          _buildDiaryItem('今日总结😌', '每天可以记录多个笔记', '5:13 PM'),
        ],
      ),
    );
  }

  Widget _buildDiaryItem(String title, String subtitle, String time) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: Image.asset(
            'images/placeholder.png',
            width: 52.r,
            height: 52.r,
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: TextUtil.base.sp(16).medium),
              SizedBox(height: 2.h),
              Text(subtitle, style: TextUtil.base.sp(12).grey),
            ],
          ),
        ),
        SizedBox(width: 12.w),
        Text(time, style: TextUtil.base.sp(12).grey),
      ],
    );
  }

  Widget _buildCard({required Widget child, Color? color}) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color ?? CupertinoColors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: child,
    );
  }

  Widget _buildPortalOverlay() {
    return AnimatedBuilder(
      animation: _menuAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            // 全屏灰色遮罩
            Positioned.fill(
              child: GestureDetector(
                onTap: _toggleMenu,
                child: Container(
                  color: CupertinoColors.black.withValues(
                    alpha: 0.5 * _menuAnimation.value,
                  ),
                ),
              ),
            ),
            // 菜单选项
            _buildFloatingMenuPositioned(),
            // 遮罩层上的+号按钮，位于最顶层
            _buildOverlayAddButton(),
          ],
        );
      },
    );
  }

  Widget _buildFloatingMenuPositioned() {
    // 使用GlobalKey获取原始+号按钮的精确位置
    final RenderBox? renderBox =
        _addButtonKey.currentContext?.findRenderObject() as RenderBox?;

    if (renderBox == null) {
      // 如果无法获取位置，返回空容器
      return const SizedBox.shrink();
    }

    // 获取按钮在屏幕中的全局位置
    final Offset globalPosition = renderBox.localToGlobal(Offset.zero);
    final Size buttonSize = renderBox.size;

    return Positioned(
      top: globalPosition.dy + buttonSize.height + 8.h, // 按钮下方8像素
      right: 1.sw - globalPosition.dx - buttonSize.width, // 右对齐按钮
      child: _buildFloatingMenu(),
    );
  }

  Widget _buildFloatingMenu() {
    final List<Map<String, dynamic>> menuItems = [
      {
        'title': '添加待办事项',
        'iconPath': 'images/pages/calendar/card2.png',
        'color': _card2Color,
      },
      {
        'title': '跟进症状',
        'iconPath': 'images/pages/calendar/card3.png',
        'color': _card3Color,
      },
      {
        'title': '写个笔记',
        'iconPath': 'images/pages/calendar/card4.png',
        'color': _card4Color,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: menuItems.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;

        return AnimatedBuilder(
          animation: _menuAnimation,
          builder: (context, child) {
            // 延迟动画，让选项依次出现
            final delay = index * 0.1;
            final animationValue = (_menuAnimation.value - delay).clamp(
              0.0,
              1.0,
            );

            return Transform.translate(
              offset: Offset(0, (1 - animationValue) * 50.h),
              child: Opacity(
                opacity: animationValue,
                child: Container(
                  margin: EdgeInsets.only(bottom: 16.h),
                  child: _buildMenuItem(
                    item['title'],
                    item['iconPath'],
                    item['color'],
                  ),
                ),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  Widget _buildMenuItem(String title, String iconPath, Color iconColor) {
    return GestureDetector(
      onTap: () {
        _toggleMenu();
        // 根据标题执行不同的功能
        if (title == '跟进症状') {
          _showSymptomModal();
        } else if (title == '添加待办事项') {
          _showAddTodoModal();
        } else if (title == '写个笔记') {
          _showNoteModal();
        }
        // 其他功能可以在这里添加
      },
      child: Container(
        // 添加透明背景确保整个区域可点击
        color: CupertinoColors.transparent,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: CupertinoColors.white,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: CupertinoColors.black.withValues(alpha: 0.1),
                    blurRadius: 8.r,
                    offset: Offset(0, 2.h),
                  ),
                ],
              ),
              child: Text(title, style: TextUtil.base.sp(14).medium),
            ),
            SizedBox(width: 12.w),
            Container(
              width: 48.r,
              height: 48.r,
              decoration: BoxDecoration(
                color: CupertinoColors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: CupertinoColors.black.withValues(alpha: 0.1),
                    blurRadius: 8.r,
                    offset: Offset(0, 2.h),
                  ),
                ],
              ),
              child: ImageIcon(
                AssetImage(iconPath),
                size: 24.sp,
                color: iconColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverlayAddButton() {
    // 使用GlobalKey获取原始+号按钮的精确位置
    final RenderBox? renderBox =
        _addButtonKey.currentContext?.findRenderObject() as RenderBox?;

    if (renderBox == null) {
      // 如果无法获取位置，返回空容器
      return const SizedBox.shrink();
    }

    // 获取按钮在屏幕中的全局位置
    final Offset globalPosition = renderBox.localToGlobal(Offset.zero);
    final Size buttonSize = renderBox.size;

    return Positioned(
      left: globalPosition.dx,
      top: globalPosition.dy,
      child: AnimatedBuilder(
        animation: _menuAnimation,
        builder: (context, child) {
          return GestureDetector(
            onTap: _toggleMenu,
            child: SizedBox(
              width: buttonSize.width,
              height: buttonSize.height,
              child: Center(
                child: Transform.rotate(
                  angle: _rotationAnimation.value * 2 * 3.14159,
                  child: Icon(
                    CupertinoIcons.add,
                    size: 24.sp,
                    color: Color.lerp(
                      CupertinoColors.black,
                      CupertinoColors.white,
                      _menuAnimation.value,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showSymptomModal() {
    showCupertinoModalBottomSheet(
      context: context,
      backgroundColor: _card3BgColor,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: _card3BgColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: SymptomPage(cardColor: _card3Color, cardBgColor: _card3BgColor),
      ),
    );
  }

  void _showAddTodoModal() {
    showCupertinoModalBottomSheet(
      context: context,
      backgroundColor: _card2BgColor,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: _card2BgColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: AddTodoPage(cardColor: _card2Color, cardBgColor: _card2BgColor),
      ),
    );
  }

  void _showNoteModal() {
    showCupertinoModalBottomSheet(
      context: context,
      backgroundColor: _card4BgColor,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: _card4BgColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: NotePage(cardColor: _card4Color, cardBgColor: _card4BgColor),
      ),
    );
  }
}
