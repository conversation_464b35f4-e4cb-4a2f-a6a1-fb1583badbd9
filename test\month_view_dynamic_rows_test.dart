import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/swipeable_calendar.dart';

void main() {
  group('Month View Dynamic Rows Tests', () {
    testWidgets('Should handle months with different row counts', (WidgetTester tester) async {
      // 测试不同月份的行数计算
      // 2024年2月：4行（28天，从周四开始）
      // 2024年5月：5行（31天，从周三开始）
      // 2024年9月：6行（30天，从周日开始）
      
      final testDates = [
        DateTime(2024, 2, 15), // 2月，4行
        DateTime(2024, 5, 15), // 5月，5行
        DateTime(2024, 9, 15), // 9月，6行
      ];
      
      for (final date in testDates) {
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: const Size(393, 852),
            builder: (context, child) {
              return CupertinoApp(
                home: CupertinoPageScaffold(
                  child: Safe<PERSON>rea(
                    child: SwipeableCalendar(
                      selectedDay: date,
                      initialFormat: CustomCalendarFormat.month,
                      monthViewHeight: 350.0.h,
                      onDaySelected: (day) {},
                      onMonthChanged: (month) {},
                      onViewChanged: (format) {},
                    ),
                  ),
                ),
              );
            },
          ),
        );

        await tester.pumpAndSettle();

        // 验证月视图正确渲染
        expect(find.byType(SwipeableCalendar), findsOneWidget);
        
        // 验证没有渲染异常
        expect(tester.takeException(), isNull, reason: 'Should not overflow for ${date.month}月');
        
        // 验证星期标题存在且固定
        expect(find.text('一'), findsOneWidget);
        expect(find.text('日'), findsOneWidget);
      }
    });

    testWidgets('Should have fixed weekday headers outside PageView', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    monthViewHeight: 300.0.h,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证星期标题存在
      expect(find.text('一'), findsOneWidget);
      expect(find.text('二'), findsOneWidget);
      expect(find.text('三'), findsOneWidget);
      expect(find.text('四'), findsOneWidget);
      expect(find.text('五'), findsOneWidget);
      expect(find.text('六'), findsOneWidget);
      expect(find.text('日'), findsOneWidget);
      
      // 验证没有渲染异常
      expect(tester.takeException(), isNull);
    });

    testWidgets('Should calculate correct cell height for different months', (WidgetTester tester) async {
      // 测试不同高度配置下的单元格高度计算
      final testConfigs = [
        {'height': 250.0, 'month': 2}, // 较小高度，2月（4行）
        {'height': 350.0, 'month': 5}, // 中等高度，5月（5行）
        {'height': 450.0, 'month': 9}, // 较大高度，9月（6行）
      ];
      
      for (final config in testConfigs) {
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: const Size(393, 852),
            builder: (context, child) {
              return CupertinoApp(
                home: CupertinoPageScaffold(
                  child: SafeArea(
                    child: SwipeableCalendar(
                      selectedDay: DateTime(2024, config['month'] as int, 15),
                      initialFormat: CustomCalendarFormat.month,
                      monthViewHeight: (config['height'] as double).h,
                      onDaySelected: (day) {},
                      onMonthChanged: (month) {},
                      onViewChanged: (format) {},
                    ),
                  ),
                ),
              );
            },
          ),
        );

        await tester.pumpAndSettle();

        // 验证没有渲染异常
        expect(tester.takeException(), isNull, 
            reason: 'Should not overflow for ${config['month']}月 with height ${config['height']}');
        
        // 验证基本结构存在
        expect(find.byType(SwipeableCalendar), findsOneWidget);
      }
    });

    testWidgets('Should maintain weekday headers when switching months', (WidgetTester tester) async {
      String? currentMonthTitle;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    monthViewHeight: 350.0.h,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(find.text('一'), findsOneWidget);
      expect(find.text('日'), findsOneWidget);
      
      // 模拟月份切换（通过拖拽）
      final calendarFinder = find.byType(PageView);
      if (calendarFinder.evaluate().isNotEmpty) {
        await tester.drag(calendarFinder, const Offset(-100, 0), warnIfMissed: false);
        await tester.pumpAndSettle();
        
        // 验证星期标题仍然存在
        expect(find.text('一'), findsOneWidget);
        expect(find.text('日'), findsOneWidget);
      }
      
      // 验证没有渲染异常
      expect(tester.takeException(), isNull);
    });
  });
}
