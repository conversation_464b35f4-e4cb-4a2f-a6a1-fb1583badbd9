import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_models.dart';
import '../models/user_models.dart';

/// SharedPreferences工具类，统一管理本地数据持久化存储
class SpUtil {
  SpUtil._();

  // SharedPreferences实例
  static SharedPreferences? _prefs;

  // 存储键名常量
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyUserInfo = 'user_info';
  static const String _keyAuthToken = 'auth_token';
  static const String _keyUserPreferences = 'user_preferences';
  static const String _keyFirstLaunch = 'first_launch';
  static const String _keyLanguage = 'language';
  static const String _keyThemeMode = 'theme_mode';
  static const String _keyLastLoginTime = 'last_login_time';
  static const String _keyAutoLogin = 'auto_login';
  static const String _keyRememberPassword = 'remember_password';
  static const String _keyLastUsername = 'last_username';

  /// 初始化SharedPreferences
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 确保SharedPreferences已初始化
  static Future<SharedPreferences> _ensureInit() async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }

  // ==================== 基础存储方法 ====================

  /// 存储字符串
  static Future<bool> setString(String key, String value) async {
    final prefs = await _ensureInit();
    return await prefs.setString(key, value);
  }

  /// 获取字符串
  static Future<String?> getString(String key, [String? defaultValue]) async {
    final prefs = await _ensureInit();
    return prefs.getString(key) ?? defaultValue;
  }

  /// 存储布尔值
  static Future<bool> setBool(String key, bool value) async {
    final prefs = await _ensureInit();
    return await prefs.setBool(key, value);
  }

  /// 获取布尔值
  static Future<bool> getBool(String key, [bool defaultValue = false]) async {
    final prefs = await _ensureInit();
    return prefs.getBool(key) ?? defaultValue;
  }

  /// 存储整数
  static Future<bool> setInt(String key, int value) async {
    final prefs = await _ensureInit();
    return await prefs.setInt(key, value);
  }

  /// 获取整数
  static Future<int> getInt(String key, [int defaultValue = 0]) async {
    final prefs = await _ensureInit();
    return prefs.getInt(key) ?? defaultValue;
  }

  /// 存储双精度浮点数
  static Future<bool> setDouble(String key, double value) async {
    final prefs = await _ensureInit();
    return await prefs.setDouble(key, value);
  }

  /// 获取双精度浮点数
  static Future<double> getDouble(String key, [double defaultValue = 0.0]) async {
    final prefs = await _ensureInit();
    return prefs.getDouble(key) ?? defaultValue;
  }

  /// 存储字符串列表
  static Future<bool> setStringList(String key, List<String> value) async {
    final prefs = await _ensureInit();
    return await prefs.setStringList(key, value);
  }

  /// 获取字符串列表
  static Future<List<String>> getStringList(String key, [List<String>? defaultValue]) async {
    final prefs = await _ensureInit();
    return prefs.getStringList(key) ?? defaultValue ?? [];
  }

  /// 存储JSON对象
  static Future<bool> setJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }

  /// 获取JSON对象
  static Future<Map<String, dynamic>?> getJson(String key) async {
    final jsonString = await getString(key);
    if (jsonString == null || jsonString.isEmpty) {
      return null;
    }
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// 删除指定键的数据
  static Future<bool> remove(String key) async {
    final prefs = await _ensureInit();
    return await prefs.remove(key);
  }

  /// 检查是否包含指定键
  static Future<bool> containsKey(String key) async {
    final prefs = await _ensureInit();
    return prefs.containsKey(key);
  }

  /// 获取所有键
  static Future<Set<String>> getKeys() async {
    final prefs = await _ensureInit();
    return prefs.getKeys();
  }

  /// 清除所有数据
  static Future<bool> clear() async {
    final prefs = await _ensureInit();
    return await prefs.clear();
  }

  // ==================== 登录状态管理 ====================

  /// 设置登录状态
  static Future<bool> setLoginStatus(bool isLoggedIn) async {
    return await setBool(_keyIsLoggedIn, isLoggedIn);
  }

  /// 获取登录状态
  static Future<bool> getLoginStatus() async {
    return await getBool(_keyIsLoggedIn, false);
  }

  /// 设置自动登录
  static Future<bool> setAutoLogin(bool autoLogin) async {
    return await setBool(_keyAutoLogin, autoLogin);
  }

  /// 获取自动登录设置
  static Future<bool> getAutoLogin() async {
    return await getBool(_keyAutoLogin, false);
  }

  /// 设置记住密码
  static Future<bool> setRememberPassword(bool remember) async {
    return await setBool(_keyRememberPassword, remember);
  }

  /// 获取记住密码设置
  static Future<bool> getRememberPassword() async {
    return await getBool(_keyRememberPassword, false);
  }

  /// 设置最后登录用户名
  static Future<bool> setLastUsername(String username) async {
    return await setString(_keyLastUsername, username);
  }

  /// 获取最后登录用户名
  static Future<String?> getLastUsername() async {
    return await getString(_keyLastUsername);
  }

  /// 设置最后登录时间
  static Future<bool> setLastLoginTime(DateTime time) async {
    return await setString(_keyLastLoginTime, time.toIso8601String());
  }

  /// 获取最后登录时间
  static Future<DateTime?> getLastLoginTime() async {
    final timeString = await getString(_keyLastLoginTime);
    if (timeString == null) return null;
    try {
      return DateTime.parse(timeString);
    } catch (e) {
      return null;
    }
  }

  // ==================== 用户信息管理 ====================

  /// 保存用户信息
  static Future<bool> saveUserInfo(UserModel user) async {
    final userJson = user.toJson();
    return await setJson(_keyUserInfo, userJson);
  }

  /// 获取用户信息
  static Future<UserModel?> getUserInfo() async {
    final userJson = await getJson(_keyUserInfo);
    if (userJson == null) return null;
    try {
      return UserModel.fromJson(userJson);
    } catch (e) {
      return null;
    }
  }

  /// 清除用户信息
  static Future<bool> clearUserInfo() async {
    return await remove(_keyUserInfo);
  }

  /// 更新用户信息
  static Future<bool> updateUserInfo(UserModel user) async {
    return await saveUserInfo(user);
  }

  // ==================== 认证令牌管理 ====================

  /// 保存认证令牌
  static Future<bool> saveAuthToken(AuthTokenModel token) async {
    final tokenJson = token.toJson();
    return await setJson(_keyAuthToken, tokenJson);
  }

  /// 获取认证令牌
  static Future<AuthTokenModel?> getAuthToken() async {
    final tokenJson = await getJson(_keyAuthToken);
    if (tokenJson == null) return null;
    try {
      return AuthTokenModel.fromJson(tokenJson);
    } catch (e) {
      return null;
    }
  }

  /// 清除认证令牌
  static Future<bool> clearAuthToken() async {
    return await remove(_keyAuthToken);
  }

  /// 检查令牌是否有效
  static Future<bool> isTokenValid() async {
    final token = await getAuthToken();
    return token?.isValid ?? false;
  }

  // ==================== 用户偏好设置管理 ====================

  /// 保存用户偏好设置
  static Future<bool> saveUserPreferences(UserPreferencesModel preferences) async {
    final preferencesJson = preferences.toJson();
    return await setJson(_keyUserPreferences, preferencesJson);
  }

  /// 获取用户偏好设置
  static Future<UserPreferencesModel> getUserPreferences() async {
    final preferencesJson = await getJson(_keyUserPreferences);
    if (preferencesJson == null) {
      return UserPreferencesModel.defaultSettings();
    }
    try {
      return UserPreferencesModel.fromJson(preferencesJson);
    } catch (e) {
      return UserPreferencesModel.defaultSettings();
    }
  }

  /// 清除用户偏好设置
  static Future<bool> clearUserPreferences() async {
    return await remove(_keyUserPreferences);
  }

  // ==================== 应用设置管理 ====================

  /// 设置是否首次启动
  static Future<bool> setFirstLaunch(bool isFirst) async {
    return await setBool(_keyFirstLaunch, isFirst);
  }

  /// 获取是否首次启动
  static Future<bool> isFirstLaunch() async {
    return await getBool(_keyFirstLaunch, true);
  }

  /// 设置语言
  static Future<bool> setLanguage(String language) async {
    return await setString(_keyLanguage, language);
  }

  /// 获取语言设置
  static Future<String> getLanguage() async {
    return await getString(_keyLanguage, 'zh_CN') ?? 'zh_CN';
  }

  /// 设置主题模式
  static Future<bool> setThemeMode(int mode) async {
    return await setInt(_keyThemeMode, mode);
  }

  /// 获取主题模式
  static Future<int> getThemeMode() async {
    return await getInt(_keyThemeMode, 0);
  }

  // ==================== 便捷方法 ====================

  /// 完整登录（保存所有登录相关信息）
  static Future<bool> completeLogin({
    required UserModel user,
    AuthTokenModel? token,
    bool autoLogin = false,
    bool rememberPassword = false,
  }) async {
    try {
      // 保存登录状态
      await setLoginStatus(true);

      // 保存用户信息
      await saveUserInfo(user);

      // 保存认证令牌（如果有）
      if (token != null) {
        await saveAuthToken(token);
      }

      // 保存登录设置
      await setAutoLogin(autoLogin);
      await setRememberPassword(rememberPassword);

      // 保存最后登录用户名和时间
      if (user.username != null) {
        await setLastUsername(user.username!);
      }
      await setLastLoginTime(DateTime.now());

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 完整登出（清除所有登录相关信息）
  static Future<bool> completeLogout({bool clearPreferences = false}) async {
    try {
      // 清除登录状态
      await setLoginStatus(false);

      // 清除用户信息
      await clearUserInfo();

      // 清除认证令牌
      await clearAuthToken();

      // 清除自动登录设置
      await setAutoLogin(false);

      // 可选：清除用户偏好设置
      if (clearPreferences) {
        await clearUserPreferences();
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取存储大小（估算）
  static Future<int> getStorageSize() async {
    final prefs = await _ensureInit();
    final keys = prefs.getKeys();
    int totalSize = 0;

    for (String key in keys) {
      final value = prefs.get(key);
      if (value != null) {
        totalSize += key.length + value.toString().length;
      }
    }

    return totalSize;
  }

  /// 导出所有数据
  static Future<Map<String, dynamic>> exportAllData() async {
    final prefs = await _ensureInit();
    final keys = prefs.getKeys();
    final Map<String, dynamic> allData = {};

    for (String key in keys) {
      allData[key] = prefs.get(key);
    }

    return allData;
  }

  /// 导入数据
  static Future<bool> importData(Map<String, dynamic> data) async {
    try {
      final prefs = await _ensureInit();

      for (String key in data.keys) {
        final value = data[key];
        if (value is String) {
          await prefs.setString(key, value);
        } else if (value is bool) {
          await prefs.setBool(key, value);
        } else if (value is int) {
          await prefs.setInt(key, value);
        } else if (value is double) {
          await prefs.setDouble(key, value);
        } else if (value is List<String>) {
          await prefs.setStringList(key, value);
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }
}
