import 'package:align/utils/color_util.dart';
import 'package:align/utils/toast_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/pages/main_page.dart';
import 'package:align/pages/due_date_setup_page.dart';
import 'package:align/services/auth_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  int _selectedTab = 0; // 0: 验证码登录, 1: 密码登录
  late PageController _pageController;
  bool _obscureText = true;
  final double _kInputHeight = 52.h;
  bool _isAgreementChecked = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _selectedTab);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
       onTap: () => FocusScope.of(context).unfocus(),
      child: CupertinoPageScaffold(
        backgroundColor: ColorUtil.primaryBgColor,
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Align',
                  style: TextUtil.base.sp(34).semiBold.withColor(CupertinoColors.black),
                ),
                SizedBox(height: 40.h),
                Container(
                  width: 343.w,
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.w, vertical: 32.h),
                  decoration: BoxDecoration(
                    color: CupertinoColors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                        color: CupertinoColors.black.withValues(alpha: 0.05),
                        spreadRadius: 5,
                        blurRadius: 15,
                      ),
                    ],
                  ),
                  child: _buildLoginFormContainer(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginFormContainer() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTabs(),
        SizedBox(
          height: 320.h, // Reduced height to make room for bottom buttons
          child: PageView(
            physics: const NeverScrollableScrollPhysics(),
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _selectedTab = index;
              });
            },
            children: [
              _buildVerificationForm(),
              _buildPasswordForm(),
            ],
          ),
        ),
        _buildOtherLoginMethods(),
      ],
    );
  }

  Widget _buildVerificationForm() {
    return Column(
       crossAxisAlignment: CrossAxisAlignment.start,
       children: [
          SizedBox(height: 24.h),
        // Text(
        //   '您所在地区仅支持\n手机号 / 邮箱 / 微信 / Apple账号 登录',
        //   style: TextStyle(
        //     fontSize: 14.sp,
        //     color: CupertinoColors.inactiveGray,
        //     decoration: TextDecoration.none,
        //   ),
        // ),
        // SizedBox(height: 24.h),
        _buildTextField(
          prefixText: '+86',
          hintText: '手机号码',
          keyboardType: TextInputType.phone,
          maxLength: 11,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                prefixText: '#',
                hintText: '验证码',
                keyboardType: TextInputType.number,
                maxLength: 6,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
            ),
            SizedBox(width: 16.w),
            Container(
              decoration: BoxDecoration(
                color: ColorUtil.primaryBgColor,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: SizedBox(
                height: _kInputHeight,
                child: CupertinoButton(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  color: CupertinoColors.transparent,
                  child: Center(
                    child: Text(
                      '发送验证码',
                      style: TextUtil.base.sp(14).withColor(CupertinoColors.black.withValues(alpha: 0.7)),
                    ),
                  ),
                  onPressed: () {},
                ),
              ),
            )
          ],
        ),
        SizedBox(height: 24.h),
        _buildAgreement(true),
        SizedBox(height: 24.h),
        _buildLoginButton(),
       ],
    );
  }

  Widget _buildPasswordForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 24.h),
        // Text(
        //   '您所在地区仅支持\n手机号 / 邮箱 / 微信 / Apple账号 登录',
        //   style: TextStyle(
        //     fontSize: 14.sp,
        //     color: CupertinoColors.inactiveGray,
        //     decoration: TextDecoration.none,
        //   ),
        // ),
        // SizedBox(height: 24.h),
        _buildTextField(
          icon: CupertinoIcons.mail,
          hintText: '请输入手机号/邮箱地址',
          keyboardType: TextInputType.emailAddress,
        ),
        SizedBox(height: 16.h),
        _buildPasswordTextField(),
        SizedBox(height: 24.h),
        _buildAgreement(false),
        SizedBox(height: 24.h),
        _buildLoginButton(),
        SizedBox(height: 6.h),
        _buildBottomLinks(),
      ],
    );
  }

  Widget _buildLoginButton() {
    return CupertinoButton(
      minimumSize: Size(double.infinity, 50.h),
      color: CupertinoColors.black,
      borderRadius: BorderRadius.circular(12.r),
      onPressed: _handleLogin,
      child: Center(child: Text('登录', style: TextUtil.base.sp(16).withColor(CupertinoColors.white))),
    );
  }

  Future<void> _handleLogin() async {
    if (!_isAgreementChecked) {
      ToastUtil.showValidationError('请先同意用户协议和隐私政策');
      return;
    }

    try {

      // 直接执行登录，不验证输入内容
      bool loginSuccess = await AuthService.instance.login(
        email: '<EMAIL>',
        password: 'demo_password',
      );


      if (loginSuccess) {
        // 登录成功，跳转到预产期设置页面
        if (mounted) {
          Navigator.of(context).pushReplacement(
            CupertinoPageRoute(
              builder: (context) => const DueDateSetupPage(),
            ),
          );
        }
      } else {
        // 登录失败，显示错误信息
        ToastUtil.showLoginFailed();
      }
    } catch (e) {
      // 处理异常
      ToastUtil.showError('登录过程中发生错误: $e');
    }
  }

  Widget _buildTextField({
    String? prefixText,
    IconData? icon,
    required String hintText,
    TextInputType? keyboardType,
    int? maxLength,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return SizedBox(
      height: _kInputHeight,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: ColorUtil.primaryBgColor,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            if (prefixText != null)
              Text(
                prefixText,
                style: TextUtil.base.sp(16).withColor(CupertinoColors.black),
              ),
            if (icon != null)
              Icon(icon, color: CupertinoColors.inactiveGray, size: 20.sp),
            SizedBox(width: 12.w),
            Expanded(
              child: CupertinoTextField(
                placeholder: hintText,
                decoration: const BoxDecoration(),
                style: TextUtil.base.sp(16),
                keyboardType: keyboardType,
                maxLength: maxLength,
                inputFormatters: inputFormatters,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordTextField() {
    return SizedBox(
      height: _kInputHeight,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: ColorUtil.primaryBgColor,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Icon(CupertinoIcons.lock,
                color: CupertinoColors.inactiveGray, size: 20.sp),
            SizedBox(width: 12.w),
            Expanded(
              child: CupertinoTextField(
                placeholder: '请输入密码',
                decoration: const BoxDecoration(),
                style: TextUtil.base.sp(16),
                obscureText: _obscureText,
              ),
            ),
            CupertinoButton(
              padding: EdgeInsets.zero,
              child: Icon(
                _obscureText ? CupertinoIcons.eye_slash : CupertinoIcons.eye,
                color: CupertinoColors.inactiveGray,
              ),
              onPressed: () {
                setState(() {
                  _obscureText = !_obscureText;
                });
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _buildAgreement(bool showPhone) {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _isAgreementChecked = !_isAgreementChecked;
            });
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: 20.r,
            width: 20.r,
            decoration: BoxDecoration(
              color: _isAgreementChecked
                  ? ColorUtil.primaryBgColor
                  : CupertinoColors.white,
              border: Border.all(
                color: _isAgreementChecked
                    ? ColorUtil.primaryBgColor
                    : CupertinoColors.inactiveGray,
                width: 1.5,
              ),
              shape: BoxShape.circle,
            ),
            child: _isAgreementChecked
                ? Icon(
                    CupertinoIcons.check_mark,
                    color: CupertinoColors.white,
                    size: 12.r,
                  )
                : null,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextUtil.base.sp(12).withColor(CupertinoColors.inactiveGray),
              children: [
                const TextSpan(text: '已阅读并同意我们的 '),
                TextSpan(
                  text: '用户协议',
                  style: TextUtil.base.withColor(CupertinoColors.systemBlue),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      // TODO: Navigate to User Agreement
                    },
                ),
                const TextSpan(text: ' 与 '),
                TextSpan(
                  text: '隐私政策',
                  style: TextUtil.base.withColor(CupertinoColors.systemBlue),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      // TODO: Navigate to Privacy Policy
                    },
                ),
                if (showPhone)
                  const TextSpan(text: '\n未注册的手机号将自动注册'),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget _buildBottomLinks() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CupertinoButton(
          padding: EdgeInsets.zero,
          child: Text(
            '忘记密码',
            style: TextUtil.base.sp(14).withColor(CupertinoColors.inactiveGray),
          ),
          onPressed: () {},
        ),
        CupertinoButton(
          padding: EdgeInsets.zero,
          child: Text(
            '立刻注册',
            style: TextUtil.base.sp(14).withColor(CupertinoColors.inactiveGray),
          ),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildOtherLoginMethods() {
    return Column(
      children: [
        SizedBox(height: 4.h),
        Row(
          children: [
            Expanded(child: Container(height: 1, color: ColorUtil.primaryBgColor)),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Text(
                '或',
                style: TextUtil.base.sp(14).withColor(ColorUtil.primaryBgColor),
              ),
            ),
            Expanded(child: Container(height: 1, color: ColorUtil.primaryBgColor)),
          ],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(child: _buildWeChatLoginButton()),
            SizedBox(width: 12.w),
            Expanded(child: _buildAppleLoginButton()),
          ],
        ),
      ],
    );
  }

  Widget _buildWeChatLoginButton() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      child: Container(
        height: 44.h,
        decoration: BoxDecoration(
          color: CupertinoColors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: ColorUtil.primaryBgColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('images/icon/wechat.png', width: 20.w, height: 20.h),
            SizedBox(width: 8.w),
            Text(
              '微信登录',
              style: TextUtil.base.sp(14).medium.withColor(ColorUtil.primaryBgColor),
            ),
          ],
        ),
      ),
      onPressed: () {
        // TODO: Implement WeChat login
      },
    );
  }

  Widget _buildAppleLoginButton() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      child: Container(
        height: 44.h,
        decoration: BoxDecoration(
          color: CupertinoColors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: ColorUtil.primaryBgColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('images/icon/apple.png', width: 20.w, height: 20.h),
            SizedBox(width: 8.w),
            Text(
              'Apple登录',
              style: TextUtil.base.sp(14).medium.withColor(ColorUtil.primaryBgColor),
            ),
          ],
        ),
      ),
      onPressed: () {
        // TODO: Implement Apple login
      },
    );
  }

  Widget _buildTabs() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildTabItem('验证码登录', 0),
        _buildTabItem('密码登录', 1),
      ],
    );
  }

  Widget _buildTabItem(String title, int index) {
    final bool isSelected = _selectedTab == index;
    return GestureDetector(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      child: Column(
        children: [
          Text(
            title,
            style: isSelected
                ? TextUtil.base.sp(16).semiBold.withColor(CupertinoColors.black)
                : TextUtil.base.sp(16).withColor(CupertinoColors.inactiveGray),
          ),
          SizedBox(height: 8.h),
          if (isSelected)
            Container(
              height: 2.h,
              width: 20.w,
              color: CupertinoColors.black,
            )
          else
             Container(height: 2.h, width: 20.w, color: CupertinoColors.transparent),
        ],
      ),
    );
  }
} 