import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import '../../utils/text_util.dart';
import '../../utils/color_util.dart';
import '../chinese_date_picker.dart';

class WeightAddBottomSheet extends StatefulWidget {
  final Function(String weight, String unit, String time)? onWeightAdded;
  final Color color;
  final Color bgColor;

  const WeightAddBottomSheet({
    super.key,
    this.onWeightAdded,
    required this.color,
    required this.bgColor,
  });

  @override
  State<WeightAddBottomSheet> createState() => _WeightAddBottomSheetState();
}

class _WeightAddBottomSheetState extends State<WeightAddBottomSheet> {
  final TextEditingController _weightController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();
  final List<String> _units = ['kg', 'lb', 'g'];
  int _selectedUnitIndex = 0; // 默认选中"kg"

  @override
  void initState() {
    super.initState();
    // 设置默认时间为当前日期
    final now = DateTime.now();
    _timeController.text = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _weightController.dispose();
    _timeController.dispose();
    super.dispose();
  }

  void _onAddWeight() {
    final weight = _weightController.text.trim();
    final time = _timeController.text.trim();
    
    if (weight.isEmpty) {
      _showErrorDialog('请输入体重');
      return;
    }
    
    if (time.isEmpty) {
      _showErrorDialog('请选择时间');
      return;
    }

    // 验证体重是否为有效数字
    final weightValue = double.tryParse(weight);
    if (weightValue == null || weightValue <= 0) {
      _showErrorDialog('请输入有效的体重数值');
      return;
    }

    final unit = _units[_selectedUnitIndex];
    widget.onWeightAdded?.call(weight, unit, time);
    Navigator.of(context).pop();
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('提示'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            child: Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showUnitPicker() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => Container(
        height: 250.h,
        color: CupertinoColors.white,
        child: Column(
          children: [
            // 顶部操作栏
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: ColorUtil.borderGrey,
                    width: 1.w,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '取消',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(CupertinoColors.systemGrey),
                    ),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '确定',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(widget.color),
                    ),
                  ),
                ],
              ),
            ),
            // 选择器
            Expanded(
              child: CupertinoPicker(
                itemExtent: 40.h,
                scrollController: FixedExtentScrollController(
                  initialItem: _selectedUnitIndex,
                ),
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedUnitIndex = index;
                  });
                },
                children: _units.map((unit) => Center(
                  child: Text(
                    unit,
                    style: TextUtil.base
                        .sp(16)
                        .withColor(widget.color),
                  ),
                )).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDatePicker() {
    DateTime selectedDate = DateTime.now();

    // 如果已有选择的日期，解析并使用
    if (_timeController.text.isNotEmpty) {
      try {
        final parts = _timeController.text.split('-');
        if (parts.length == 3) {
          selectedDate = DateTime(
            int.parse(parts[0]),
            int.parse(parts[1]),
            int.parse(parts[2]),
          );
        }
      } catch (e) {
        selectedDate = DateTime.now();
      }
    }

    showCupertinoModalPopup(
      context: context,
      builder: (context) => Container(
        height: 250.h,
        color: CupertinoColors.white,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: ColorUtil.borderGrey,
                    width: 1.w,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '取消',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(CupertinoColors.systemGrey),
                    ),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '确定',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(widget.color),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: selectedDate,
                minimumDate: DateTime(1900),
                maximumDate: DateTime(2100),
                onDateTimeChanged: (DateTime date) {
                  setState(() {
                    _timeController.text = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
                  });
                },
              ),
            ),
          ],
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        height: 300.h,
        color: widget.bgColor,
        child: Column(
          children: [
            // 标题栏
            _buildHeader(),

            // 可滚动的表单内容
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Column(
                  children: [
                    // 表单内容
                    _buildForm(),

                    // 添加按钮
                    _buildAddButton(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
      child: Row(
        children: [
          // 占位，保持标题居中
          SizedBox(width: 44.w),
          
          // 中间标题
          Expanded(
            child: Center(
              child: Text(
                '添加体重',
                style: TextUtil.base
                    .sp(18)
                    .semiBold
                    .withColor(widget.color),
              ),
            ),
          ),
          
          // 右侧关闭按钮
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                CupertinoIcons.xmark,
                size: 20.sp,
                color: widget.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          // 三个输入框在同一行：体重输入框、单位选择框、时间输入框
          Row(
            children: [
              // 体重输入框 - 更短
              Expanded(
                flex: 3,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  decoration: BoxDecoration(
                    color: CupertinoColors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: ColorUtil.borderGrey,
                      width: 1.w,
                    ),
                  ),
                  child: CupertinoTextField(
                    controller: _weightController,
                    placeholder: '体重',
                    placeholderStyle: TextUtil.base
                        .sp(16)
                        .withColor(CupertinoColors.systemGrey),
                    style: TextUtil.base
                        .sp(16)
                        .withColor(widget.color),
                    decoration: null,
                    padding: EdgeInsets.zero,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                  ),
                ),
              ),

              SizedBox(width: 12.w),

              // 单位选择下拉框
              Expanded(
                flex: 2,
                child: GestureDetector(
                  onTap: _showUnitPicker,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                    decoration: BoxDecoration(
                      color: CupertinoColors.white,
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color: ColorUtil.borderGrey,
                        width: 1.w,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            _units[_selectedUnitIndex],
                            style: TextUtil.base
                                .sp(16)
                                .withColor(widget.color),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Icon(
                          CupertinoIcons.chevron_down,
                          size: 16.sp,
                          color: CupertinoColors.systemGrey,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              SizedBox(width: 12.w),

              // 时间输入框
              Expanded(
                flex: 4,
                child: GestureDetector(
                  onTap: _showDatePicker,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                    decoration: BoxDecoration(
                      color: CupertinoColors.white,
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color: ColorUtil.borderGrey,
                        width: 1.w,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            _timeController.text.isEmpty ? '选择时间' : _timeController.text,
                            style: TextUtil.base
                                .sp(16)
                                .withColor(_timeController.text.isEmpty
                                    ? CupertinoColors.systemGrey
                                    : widget.color),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Icon(
                          CupertinoIcons.calendar,
                          size: 16.sp,
                          color: CupertinoColors.systemGrey,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddButton() {
    return Container(
      padding: EdgeInsets.all(24.w),
      child: SizedBox(
        width: double.infinity,
        height: 48.h,
        child: CupertinoButton(
          color: widget.color,
          borderRadius: BorderRadius.circular(99.r),
          padding: EdgeInsets.zero,
          onPressed: _onAddWeight,
          child: Text(
            '添加',
            style: TextUtil.base
                .sp(16)
                .semiBold
                .withColor(CupertinoColors.white),
          ),
        ),
      ),
    );
  }
}
