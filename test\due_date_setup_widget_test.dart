import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/pages/due_date_setup_page.dart';
import 'package:align/services/auth_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('DueDateSetupPage Widget Tests', () {
    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      await AuthService.instance.initialize();
    });

    testWidgets('DueDateSetupPage should build without errors', (WidgetTester tester) async {
      // 构建 widget
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return const CupertinoApp(
              home: DueDateSetupPage(),
            );
          },
        ),
      );

      // 等待 widget 完全构建
      await tester.pumpAndSettle();

      // 验证页面元素存在
      expect(find.text('Align'), findsOneWidget);
      expect(find.text('预产期'), findsOneWidget);
      expect(find.text('末次月经第一天'), findsOneWidget);
      expect(find.text('确认'), findsOneWidget);
      
      // 验证 CupertinoDatePicker 存在
      expect(find.byType(CupertinoDatePicker), findsOneWidget);
    });

    testWidgets('Date picker should have correct constraints', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return const CupertinoApp(
              home: DueDateSetupPage(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 查找 CupertinoDatePicker
      final datePickerFinder = find.byType(CupertinoDatePicker);
      expect(datePickerFinder, findsOneWidget);

      // 获取 CupertinoDatePicker widget
      final CupertinoDatePicker datePicker = tester.widget(datePickerFinder);
      
      // 验证日期约束
      expect(datePicker.minimumDate, isNotNull);
      expect(datePicker.maximumDate, isNotNull);
      expect(datePicker.initialDateTime, isNotNull);
      
      // 验证初始日期在有效范围内
      expect(
        datePicker.initialDateTime!.isAfter(datePicker.minimumDate!) ||
        datePicker.initialDateTime!.isAtSameMomentAs(datePicker.minimumDate!),
        isTrue,
      );
      expect(
        datePicker.initialDateTime!.isBefore(datePicker.maximumDate!) ||
        datePicker.initialDateTime!.isAtSameMomentAs(datePicker.maximumDate!),
        isTrue,
      );
    });

    testWidgets('Confirm button should be tappable', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return const CupertinoApp(
              home: DueDateSetupPage(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 查找确认按钮
      final confirmButton = find.text('确认');
      expect(confirmButton, findsOneWidget);

      // 验证按钮可以点击（不会抛出异常）
      await tester.tap(confirmButton);
      await tester.pump();

      // 验证按钮状态变化（可能显示加载指示器或保持原状）
      // 在测试环境中，由于没有真实的用户数据，可能不会显示加载状态
      expect(find.text('确认'), findsAny);
    });

    testWidgets('Method selection should work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return const CupertinoApp(
              home: DueDateSetupPage(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证两个方法选项都存在
      expect(find.text('预产期'), findsOneWidget);
      expect(find.text('末次月经第一天'), findsOneWidget);

      // 点击"末次月经第一天"选项
      await tester.tap(find.text('末次月经第一天'));
      await tester.pumpAndSettle();

      // 验证选择状态变化（这里我们主要验证不会抛出异常）
      expect(find.text('末次月经第一天'), findsOneWidget);
    });
  });
}
