// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
  id: json['id'] as String?,
  username: json['username'] as String?,
  nickname: json['nickname'] as String?,
  email: json['email'] as String?,
  phone: json['phone'] as String?,
  avatar: json['avatar'] as String?,
  gender: (json['gender'] as num?)?.toInt(),
  birthday: json['birthday'] as String?,
  dueDate: json['dueDate'] as String?,
  bio: json['bio'] as String?,
  createdAt: json['createdAt'] as String?,
  updatedAt: json['updatedAt'] as String?,
);

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
  'id': instance.id,
  'username': instance.username,
  'nickname': instance.nickname,
  'email': instance.email,
  'phone': instance.phone,
  'avatar': instance.avatar,
  'gender': instance.gender,
  'birthday': instance.birthday,
  'dueDate': instance.dueDate,
  'bio': instance.bio,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};

AuthTokenModel _$AuthTokenModelFromJson(Map<String, dynamic> json) =>
    AuthTokenModel(
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      tokenType: json['tokenType'] as String?,
      expiresIn: (json['expiresIn'] as num?)?.toInt(),
      refreshExpiresIn: (json['refreshExpiresIn'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AuthTokenModelToJson(AuthTokenModel instance) =>
    <String, dynamic>{
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
      'tokenType': instance.tokenType,
      'expiresIn': instance.expiresIn,
      'refreshExpiresIn': instance.refreshExpiresIn,
    };

AuthLoginResponse _$AuthLoginResponseFromJson(Map<String, dynamic> json) =>
    AuthLoginResponse(
      access: json['access'] as String?,
      refresh: json['refresh'] as String?,
      user: json['user'] == null
          ? null
          : AuthUserInfo.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AuthLoginResponseToJson(AuthLoginResponse instance) =>
    <String, dynamic>{
      'access': instance.access,
      'refresh': instance.refresh,
      'user': instance.user,
    };

AuthUserInfo _$AuthUserInfoFromJson(Map<String, dynamic> json) => AuthUserInfo(
  id: (json['id'] as num?)?.toInt(),
  email: json['email'] as String?,
  username: json['username'] as String?,
  nickname: json['nickname'] as String?,
  avatarUrl: json['avatar_url'] as String?,
);

Map<String, dynamic> _$AuthUserInfoToJson(AuthUserInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'username': instance.username,
      'nickname': instance.nickname,
      'avatar_url': instance.avatarUrl,
    };

UserPreferencesModel _$UserPreferencesModelFromJson(
  Map<String, dynamic> json,
) => UserPreferencesModel(
  enableNotifications: json['enableNotifications'] as bool?,
  enableSound: json['enableSound'] as bool?,
  enableVibration: json['enableVibration'] as bool?,
  themeMode: (json['themeMode'] as num?)?.toInt(),
  language: json['language'] as String?,
  fontSize: (json['fontSize'] as num?)?.toInt(),
  enableBiometric: json['enableBiometric'] as bool?,
  autoLockTime: (json['autoLockTime'] as num?)?.toInt(),
);

Map<String, dynamic> _$UserPreferencesModelToJson(
  UserPreferencesModel instance,
) => <String, dynamic>{
  'enableNotifications': instance.enableNotifications,
  'enableSound': instance.enableSound,
  'enableVibration': instance.enableVibration,
  'themeMode': instance.themeMode,
  'language': instance.language,
  'fontSize': instance.fontSize,
  'enableBiometric': instance.enableBiometric,
  'autoLockTime': instance.autoLockTime,
};
