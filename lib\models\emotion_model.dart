import 'package:json_annotation/json_annotation.dart';
import 'package:azlistview/azlistview.dart';
import 'package:lpinyin/lpinyin.dart';

part 'emotion_model.g.dart';

/// 情绪词汇模型
@JsonSerializable()
class EmotionModel extends ISuspensionBean {
  final String name;
  final String tagIndex;

  EmotionModel({
    required this.name,
    required this.tagIndex,
  });

  factory EmotionModel.fromJson(Map<String, dynamic> json) => _$EmotionModelFromJson(json);
  Map<String, dynamic> toJson() => _$EmotionModelToJson(this);

  @override
  String getSuspensionTag() => tagIndex;
}

/// 情绪数据管理类
class EmotionDataManager {
  static Map<String, List<String>> _emotionData = {
    'A': ['哀伤', '爱慕', '安慰', '安心'],
    'B': ['悲伤', '不安', '暴躁', '平静'],
    'C': ['沉思', '愉快', '吃惊', '充实'],
    'O': ['偶然', '乐观', '忧虑', '空虚'],
    'P': ['平和', '平静', '焦虑', '沮丧'],
    'Q': ['情绪化', '奇异', '困惑', '乏力'],
    'X': ['兴奋', '心烦', '洗净', '喜悦'],
    'Y': ['忧伤', '意外', '诱惑', '悠闲'],
    'Z': ['自豪', '自卑', '醉意', '忠诚']
  };

  /// 获取所有情绪词汇列表
  static List<EmotionModel> getAllEmotions() {
    List<EmotionModel> emotions = [];

    // 按字母顺序处理数据，确保分组正确
    final sortedKeys = _emotionData.keys.toList()..sort();

    for (String key in sortedKeys) {
      final values = _emotionData[key]!;
      for (String emotion in values) {
        emotions.add(EmotionModel(
          name: emotion,
          tagIndex: key,
        ));
      }
    }

    return emotions;
  }

  /// 根据搜索关键词过滤情绪词汇
  static List<EmotionModel> searchEmotions(String keyword) {
    if (keyword.isEmpty) {
      return getAllEmotions();
    }

    List<EmotionModel> filteredEmotions = [];

    // 按字母顺序处理数据，确保分组正确
    final sortedKeys = _emotionData.keys.toList()..sort();

    for (String key in sortedKeys) {
      final values = _emotionData[key]!;
      for (String emotion in values) {
        if (emotion.contains(keyword)) {
          filteredEmotions.add(EmotionModel(
            name: emotion,
            tagIndex: key,
          ));
        }
      }
    }

    return filteredEmotions;
  }

  /// 添加新的情绪词汇
  static bool addEmotion(String emotion) {
    if (emotion.trim().isEmpty) return false;

    final trimmedEmotion = emotion.trim();

    // 检查是否已存在
    if (isEmotionExists(trimmedEmotion)) return false;

    // 获取首字母并转换为大写
    final firstChar = _getFirstLetter(trimmedEmotion);

    // 如果分组不存在，创建新分组
    if (!_emotionData.containsKey(firstChar)) {
      _emotionData[firstChar] = [];
    }

    // 添加到对应分组
    _emotionData[firstChar]!.add(trimmedEmotion);

    return true;
  }

  /// 检查情绪词汇是否已存在
  static bool isEmotionExists(String emotion) {
    final trimmedEmotion = emotion.trim();
    for (final values in _emotionData.values) {
      if (values.contains(trimmedEmotion)) {
        return true;
      }
    }
    return false;
  }

  /// 获取字符串的首字母，用于分组
  static String _getFirstLetter(String text) {
    if (text.isEmpty) return 'A';

    final firstChar = text[0];

    // 如果是中文字符，使用lpinyin库获取拼音首字母
    if (_isChineseCharacter(firstChar)) {
      return _getChinesePinyinFirstLetter(firstChar);
    }

    // 如果是英文字母，直接返回大写
    final upperChar = firstChar.toUpperCase();
    if (RegExp(r'[A-Z]').hasMatch(upperChar)) {
      return upperChar;
    }

    // 其他情况默认归类到'A'
    return 'A';
  }

  /// 判断是否为中文字符
  static bool _isChineseCharacter(String char) {
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(char);
  }

  /// 使用lpinyin库获取中文字符的拼音首字母
  static String _getChinesePinyinFirstLetter(String char) {
    try {
      // 使用lpinyin库获取拼音
      final pinyin = PinyinHelper.getPinyinE(char);
      if (pinyin.isNotEmpty) {
        // 获取拼音的首字母并转换为大写
        return pinyin[0].toUpperCase();
      }
    } catch (e) {
      // 如果获取拼音失败，使用备用方案
      print('获取拼音失败: $e');
    }

    // 备用方案：使用简化映射
    const Map<String, String> fallbackMap = {
      '哀': 'A', '爱': 'A', '安': 'A',
      '悲': 'B', '不': 'B', '暴': 'B', '平': 'P',
      '沉': 'C', '愉': 'Y', '吃': 'C', '充': 'C',
      '偶': 'O', '乐': 'L', '忧': 'Y', '空': 'K',
      '焦': 'J', '沮': 'J', '困': 'K', '乏': 'F',
      '情': 'Q', '奇': 'Q',
      '兴': 'X', '心': 'X', '洗': 'X', '喜': 'X',
      '意': 'Y', '诱': 'Y', '悠': 'Y',
      '自': 'Z', '醉': 'Z', '忠': 'Z'
    };

    return fallbackMap[char] ?? 'A';
  }
}
