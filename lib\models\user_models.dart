import 'package:json_annotation/json_annotation.dart';

part 'user_models.g.dart';

/// 用户信息模型
@JsonSerializable()
class UserModel {
  /// 用户ID
  final String? id;
  
  /// 用户名
  final String? username;
  
  /// 昵称
  final String? nickname;
  
  /// 邮箱
  final String? email;
  
  /// 手机号
  final String? phone;
  
  /// 头像URL
  final String? avatar;
  
  /// 性别 (0: 未知, 1: 男, 2: 女)
  final int? gender;
  
  /// 生日
  final String? birthday;
  
  /// 预产期
  final String? dueDate;
  
  /// 个人简介
  final String? bio;
  
  /// 创建时间
  final String? createdAt;
  
  /// 更新时间
  final String? updatedAt;

  const UserModel({
    this.id,
    this.username,
    this.nickname,
    this.email,
    this.phone,
    this.avatar,
    this.gender,
    this.birthday,
    this.dueDate,
    this.bio,
    this.createdAt,
    this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  /// 复制并更新用户信息
  UserModel copyWith({
    String? id,
    String? username,
    String? nickname,
    String? email,
    String? phone,
    String? avatar,
    int? gender,
    String? birthday,
    String? dueDate,
    String? bio,
    String? createdAt,
    String? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      nickname: nickname ?? this.nickname,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      gender: gender ?? this.gender,
      birthday: birthday ?? this.birthday,
      dueDate: dueDate ?? this.dueDate,
      bio: bio ?? this.bio,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 检查用户信息是否为空
  bool get isEmpty => id == null || id!.isEmpty;

  /// 检查用户信息是否不为空
  bool get isNotEmpty => !isEmpty;

  /// 获取显示名称（优先显示昵称，其次用户名）
  String get displayName {
    if (nickname != null && nickname!.isNotEmpty) {
      return nickname!;
    }
    if (username != null && username!.isNotEmpty) {
      return username!;
    }
    return '未知用户';
  }

  /// 获取性别文本
  String get genderText {
    switch (gender) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未知';
    }
  }

  /// 获取预产期DateTime对象
  DateTime? get dueDateAsDateTime {
    if (dueDate == null || dueDate!.isEmpty) return null;
    try {
      return DateTime.parse(dueDate!);
    } catch (e) {
      return null;
    }
  }

  /// 获取距离预产期的天数
  int? get daysToDueDate {
    final due = dueDateAsDateTime;
    if (due == null) return null;
    final now = DateTime.now();
    final difference = due.difference(DateTime(now.year, now.month, now.day));
    return difference.inDays;
  }

  /// 获取当前孕周信息（基于预产期倒推）
  Map<String, int>? get pregnancyWeeks {
    final due = dueDateAsDateTime;
    if (due == null) return null;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // 计算从预产期倒推的天数
    final daysFromDue = due.difference(today).inDays;

    // 孕期总共280天（40周）
    final totalDays = 280;
    final currentDays = totalDays - daysFromDue;

    if (currentDays < 0) {
      // 还没有怀孕
      return {'weeks': 0, 'days': 0};
    }

    final weeks = currentDays ~/ 7;
    final days = currentDays % 7;

    return {'weeks': weeks, 'days': days};
  }

  /// 获取孕周文本显示
  String get pregnancyWeeksText {
    final weeks = pregnancyWeeks;
    if (weeks == null) return '未设置预产期';

    final w = weeks['weeks']!;
    final d = weeks['days']!;

    if (w == 0 && d == 0) {
      return '未怀孕';
    } else if (w > 40) {
      return '已过预产期';
    } else {
      return '第${w}周 第${d}天';
    }
  }

  /// 获取倒计时文本（40周 - 当前孕周）
  String get countdownText {
    final weeks = pregnancyWeeks;
    if (weeks == null) return '未设置预产期';

    final currentWeeks = weeks['weeks']!;
    final currentDays = weeks['days']!;

    if (currentWeeks >= 40) {
      return '已到预产期';
    }

    final remainingWeeks = 40 - currentWeeks;
    final remainingDays = 7 - currentDays;

    if (remainingDays == 7) {
      return '还有${remainingWeeks}周';
    } else {
      return '还有${remainingWeeks - 1}周${remainingDays}天';
    }
  }

  /// 获取预产期状态文本
  String get dueDateStatusText {
    final days = daysToDueDate;
    if (days == null) return '未设置预产期';

    if (days > 0) {
      return '还有${days}天';
    } else if (days == 0) {
      return '今天是预产期';
    } else {
      return '已过预产期${-days}天';
    }
  }
}

/// 登录凭证模型
@JsonSerializable()
class AuthTokenModel {
  /// 访问令牌
  final String? accessToken;
  
  /// 刷新令牌
  final String? refreshToken;
  
  /// 令牌类型
  final String? tokenType;
  
  /// 过期时间（时间戳）
  final int? expiresIn;
  
  /// 刷新令牌过期时间（时间戳）
  final int? refreshExpiresIn;

  const AuthTokenModel({
    this.accessToken,
    this.refreshToken,
    this.tokenType,
    this.expiresIn,
    this.refreshExpiresIn,
  });

  factory AuthTokenModel.fromJson(Map<String, dynamic> json) => _$AuthTokenModelFromJson(json);
  Map<String, dynamic> toJson() => _$AuthTokenModelToJson(this);

  /// 复制并更新令牌信息
  AuthTokenModel copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    int? refreshExpiresIn,
  }) {
    return AuthTokenModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      refreshExpiresIn: refreshExpiresIn ?? this.refreshExpiresIn,
    );
  }

  /// 检查令牌是否为空
  bool get isEmpty => accessToken == null || accessToken!.isEmpty;

  /// 检查令牌是否不为空
  bool get isNotEmpty => !isEmpty;

  /// 检查访问令牌是否过期
  bool get isAccessTokenExpired {
    if (expiresIn == null) return true;
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now >= expiresIn!;
  }

  /// 检查刷新令牌是否过期
  bool get isRefreshTokenExpired {
    if (refreshExpiresIn == null) return true;
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now >= refreshExpiresIn!;
  }

  /// 检查令牌是否有效（未过期且不为空）
  bool get isValid => isNotEmpty && !isAccessTokenExpired;
}

/// 用户偏好设置模型
@JsonSerializable()
class UserPreferencesModel {
  /// 是否启用推送通知
  final bool? enableNotifications;
  
  /// 是否启用声音
  final bool? enableSound;
  
  /// 是否启用震动
  final bool? enableVibration;
  
  /// 主题模式 (0: 跟随系统, 1: 浅色, 2: 深色)
  final int? themeMode;
  
  /// 语言设置
  final String? language;
  
  /// 字体大小 (0: 小, 1: 中, 2: 大)
  final int? fontSize;
  
  /// 是否启用生物识别登录
  final bool? enableBiometric;
  
  /// 自动锁定时间（分钟）
  final int? autoLockTime;

  const UserPreferencesModel({
    this.enableNotifications,
    this.enableSound,
    this.enableVibration,
    this.themeMode,
    this.language,
    this.fontSize,
    this.enableBiometric,
    this.autoLockTime,
  });

  factory UserPreferencesModel.fromJson(Map<String, dynamic> json) => _$UserPreferencesModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserPreferencesModelToJson(this);

  /// 创建默认偏好设置
  factory UserPreferencesModel.defaultSettings() => const UserPreferencesModel(
    enableNotifications: true,
    enableSound: true,
    enableVibration: true,
    themeMode: 0, // 跟随系统
    language: 'zh_CN',
    fontSize: 1, // 中等字体
    enableBiometric: false,
    autoLockTime: 5, // 5分钟
  );

  /// 复制并更新偏好设置
  UserPreferencesModel copyWith({
    bool? enableNotifications,
    bool? enableSound,
    bool? enableVibration,
    int? themeMode,
    String? language,
    int? fontSize,
    bool? enableBiometric,
    int? autoLockTime,
  }) {
    return UserPreferencesModel(
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableSound: enableSound ?? this.enableSound,
      enableVibration: enableVibration ?? this.enableVibration,
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
      fontSize: fontSize ?? this.fontSize,
      enableBiometric: enableBiometric ?? this.enableBiometric,
      autoLockTime: autoLockTime ?? this.autoLockTime,
    );
  }
}
