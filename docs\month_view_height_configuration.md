# 月视图高度配置功能文档

## 概述

本次更新为SwipeableCalendar组件添加了可配置的月视图高度参数，实现了与年视图高度配置一致的处理方式，并优化了月视图的动态布局计算。

## 主要功能

### 1. 添加monthViewHeight参数

**修改位置**: `SwipeableCalendar` 构造函数

```dart
class SwipeableCalendar extends StatefulWidget {
  // ... 其他参数
  final double monthViewHeight; // 月视图高度（已经过ScreenUtil处理的响应式值）
  final double yearViewHeight; // 年视图高度（已经过ScreenUtil处理的响应式值）

  const SwipeableCalendar({
    // ... 其他参数
    this.monthViewHeight = 300.0, // 月视图默认高度（调用者应传入如300.h的值）
    this.yearViewHeight = 500.0, // 年视图默认高度（调用者应传入如500.h的值）
  }) : super(key: key);
}
```

**特性**:
- 参数类型为double
- 提供合理的默认值（300.0）
- 与yearViewHeight处理方式完全一致
- 支持向后兼容

### 2. 统一高度处理方式

**修改位置**: `_getViewHeight` 方法

**改进前**:
```dart
case CustomCalendarFormat.month:
  return 300.h; // 固定高度
case CustomCalendarFormat.year:
  return widget.yearViewHeight.h; // 重复.h处理
```

**改进后**:
```dart
case CustomCalendarFormat.month:
  // 月视图使用组件传入的高度（已经过ScreenUtil处理）
  return widget.monthViewHeight;
case CustomCalendarFormat.year:
  // 年视图使用组件传入的高度（已经过ScreenUtil处理）
  return widget.yearViewHeight;
```

**改进效果**:
- 移除了重复的`.h`转换处理
- 外部调用者传入的值应该已经包含`.h`单位处理
- 确保monthViewHeight和yearViewHeight的处理方式完全一致

### 3. 修改_buildMonthView方法

**修改位置**: `_buildMonthView` 方法

```dart
Widget _buildMonthView() {
  // 获取月视图容器的高度（已经过ScreenUtil处理）
  final containerHeight = widget.monthViewHeight;
  
  return SizedBox(
    height: containerHeight,
    child: PageView.builder(
      // ... 其他配置
      itemBuilder: (context, index) {
        final targetDate = _getDateForPageIndex(index, CustomCalendarFormat.month);
        return _buildMonthCalendar(targetDate, containerHeight: containerHeight);
      },
    ),
  );
}
```

**改进效果**:
- 使用传入的月视图高度替代固定的`300.h`
- 将容器高度传递给月视图日历构建方法
- 确保布局的一致性和可配置性

### 4. 优化_buildMonthCalendar方法

**修改位置**: `_buildMonthCalendar` 方法

```dart
Widget _buildMonthCalendar(DateTime targetDate, {required double containerHeight}) {
  // 动态计算月视图布局
  final weekdayHeaderHeight = 20.h; // 星期标题行高度
  final headerSpacing = 8.h; // 标题下方间距
  final availableGridHeight = containerHeight - weekdayHeaderHeight - headerSpacing;
  
  // 保守计算间距，确保不会溢出
  final rows = 6; // 月视图固定6行
  final maxMainAxisSpacing = 6.h; // 最大行间距
  final totalSpacingHeight = maxMainAxisSpacing * (rows - 1);
  final availableCellHeight = availableGridHeight - totalSpacingHeight;
  final cellHeight = availableCellHeight / rows;
  
  // 根据可用空间动态调整间距
  final mainAxisSpacing = (availableGridHeight > cellHeight * rows * 1.2) 
      ? maxMainAxisSpacing 
      : (availableGridHeight - cellHeight * rows) / (rows - 1).clamp(1.h, maxMainAxisSpacing);
  
  // ... 布局实现
}
```

**改进效果**:
- 基于传入的容器高度动态计算内部布局
- 考虑星期标题行高度和间距的影响
- 智能计算日期网格的行间距，避免内容溢出
- 确保所有日期都能正确显示

### 5. 智能间距计算

**计算逻辑**:
1. **可用高度计算**: 总高度 - 星期标题高度 - 标题间距
2. **保守间距策略**: 先预留最大间距，再计算单元格高度
3. **动态调整**: 根据实际可用空间调整行间距
4. **防溢出机制**: 确保间距不会导致内容溢出

## 使用示例

### 基本使用（使用默认高度）

```dart
SwipeableCalendar(
  selectedDay: DateTime.now(),
  initialFormat: CustomCalendarFormat.month,
  onDaySelected: (day) {},
  onMonthChanged: (month) {},
  onViewChanged: (format) {},
)
```

### 自定义月视图高度

```dart
SwipeableCalendar(
  selectedDay: DateTime.now(),
  initialFormat: CustomCalendarFormat.month,
  monthViewHeight: 350.h, // 自定义月视图高度
  onDaySelected: (day) {},
  onMonthChanged: (month) {},
  onViewChanged: (format) {},
)
```

### 同时配置月视图和年视图高度

```dart
SwipeableCalendar(
  selectedDay: DateTime.now(),
  initialFormat: CustomCalendarFormat.month,
  monthViewHeight: 350.h, // 自定义月视图高度
  yearViewHeight: 600.h, // 自定义年视图高度
  onDaySelected: (day) {},
  onMonthChanged: (month) {},
  onViewChanged: (format) {},
)
```

## 技术要点

### 1. 参数传递约定

- **外部调用者**: 传入已经过ScreenUtil处理的值（如`300.h`）
- **组件内部**: 直接使用传入的值，不再进行`.h`转换
- **一致性**: monthViewHeight和yearViewHeight采用相同的处理方式

### 2. 布局计算策略

- **分层计算**: 先计算容器级别的高度分配，再计算内部组件的间距
- **保守策略**: 优先保证内容完整显示，再优化视觉效果
- **响应式设计**: 所有计算都基于ScreenUtil的响应式单位

### 3. 兼容性保证

- **向后兼容**: 新参数有合理的默认值，不破坏现有代码
- **API一致性**: 与现有的yearViewHeight参数保持一致的使用方式
- **功能完整性**: 不影响其他视图（周视图、年视图）的功能

## 测试验证

创建了全面的测试来验证功能：

1. **基本渲染测试**: 验证月视图能够正常渲染
2. **自定义高度测试**: 测试不同月视图高度下的布局稳定性
3. **组合配置测试**: 验证月视图和年视图高度同时配置的效果
4. **无溢出测试**: 确保在各种高度配置下都不会发生布局溢出

## 优势总结

1. **灵活配置**: 支持根据具体需求调整月视图高度
2. **一致体验**: 与年视图高度配置保持一致的使用方式
3. **智能布局**: 根据可用高度动态调整内部间距
4. **响应式设计**: 完全支持ScreenUtil的响应式布局
5. **稳定可靠**: 通过保守的计算策略确保布局稳定性

这些改进使得SwipeableCalendar组件在月视图方面更加灵活和可配置，能够更好地适应不同的设计需求和屏幕尺寸。
