import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../utils/text_util.dart';
import '../../utils/color_util.dart';

class MedicineAddBottomSheet extends StatefulWidget {
  final Function(String medicineName, String quantity, String unit, String time, List<int> cycle)? onMedicineAdded;
  final Color color;
  final Color bgColor;

  const MedicineAddBottomSheet({
    super.key,
    this.onMedicineAdded,
    required this.color,
    required this.bgColor,
  });

  @override
  State<MedicineAddBottomSheet> createState() => _MedicineAddBottomSheetState();
}

class _MedicineAddBottomSheetState extends State<MedicineAddBottomSheet> {
  final TextEditingController _medicineNameController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();
  
  final List<String> _units = ['mg', 'g', 'ml', '片', '粒', '包', '瓶', '支', '盒'];
  int _selectedUnitIndex = 0; // 默认选中"mg"
  
  // 周期选择状态，1-7分别代表周一到周日
  final Set<int> _selectedCycles = <int>{};
  final List<String> _weekDays = ['一', '二', '三', '四', '五', '六', '日'];

  @override
  void initState() {
    super.initState();
    // 设置默认时间为当前时间
    final now = DateTime.now();
    _timeController.text = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _medicineNameController.dispose();
    _quantityController.dispose();
    _timeController.dispose();
    super.dispose();
  }

  void _onAddMedicine() {
    final medicineName = _medicineNameController.text.trim();
    final quantity = _quantityController.text.trim();
    final time = _timeController.text.trim();
    
    if (medicineName.isEmpty) {
      _showErrorDialog('请输入药品名称');
      return;
    }
    
    if (quantity.isEmpty) {
      _showErrorDialog('请输入数量');
      return;
    }

    // 验证数量是否为有效数字
    final quantityValue = double.tryParse(quantity);
    if (quantityValue == null || quantityValue <= 0) {
      _showErrorDialog('请输入有效的数量');
      return;
    }

    if (time.isEmpty) {
      _showErrorDialog('请选择时间');
      return;
    }

    if (_selectedCycles.isEmpty) {
      _showErrorDialog('请选择至少一个周期');
      return;
    }

    final unit = _units[_selectedUnitIndex];
    final cycleList = _selectedCycles.toList()..sort();
    
    widget.onMedicineAdded?.call(medicineName, quantity, unit, time, cycleList);
    Navigator.of(context).pop();
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('提示'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            child: Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showUnitPicker() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => Container(
        height: 250.h,
        color: CupertinoColors.white,
        child: Column(
          children: [
            // 顶部操作栏
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: ColorUtil.borderGrey,
                    width: 1.w,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '取消',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(CupertinoColors.systemGrey),
                    ),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '确定',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(widget.color),
                    ),
                  ),
                ],
              ),
            ),
            // 选择器
            Expanded(
              child: CupertinoPicker(
                itemExtent: 40.h,
                scrollController: FixedExtentScrollController(
                  initialItem: _selectedUnitIndex,
                ),
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedUnitIndex = index;
                  });
                },
                children: _units.map((unit) => Center(
                  child: Text(
                    unit,
                    style: TextUtil.base
                        .sp(16)
                        .withColor(widget.color),
                  ),
                )).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showTimePicker() {
    DateTime selectedTime = DateTime.now();
    
    // 如果已有选择的时间，解析并使用
    if (_timeController.text.isNotEmpty) {
      try {
        final parts = _timeController.text.split(':');
        if (parts.length == 2) {
          final hour = int.parse(parts[0]);
          final minute = int.parse(parts[1]);
          selectedTime = DateTime(2025, 1, 1, hour, minute);
        }
      } catch (e) {
        selectedTime = DateTime.now();
      }
    }

    showCupertinoModalPopup(
      context: context,
      builder: (context) => Container(
        height: 250.h,
        color: CupertinoColors.white,
        child: Column(
          children: [
            // 顶部操作栏
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: ColorUtil.borderGrey,
                    width: 1.w,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '取消',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(CupertinoColors.systemGrey),
                    ),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      setState(() {
                        _timeController.text = '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
                      });
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      '确定',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(widget.color),
                    ),
                  ),
                ],
              ),
            ),
            // 时间选择器
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.time,
                initialDateTime: selectedTime,
                use24hFormat: true,
                onDateTimeChanged: (DateTime time) {
                  selectedTime = time;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleCycle(int cycle) {
    setState(() {
      if (_selectedCycles.contains(cycle)) {
        _selectedCycles.remove(cycle);
      } else {
        _selectedCycles.add(cycle);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        height: 400.h,
        color: widget.bgColor,
        child: Column(
          children: [
            // 标题栏
            _buildHeader(),

            // 可滚动的表单内容
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Column(
                  children: [
                    // 第一行输入区域
                    _buildFirstRow(),
                    
                    SizedBox(height: 16.h),
                    
                    // 第二行周期选择区域
                    _buildCycleSelection(),

                    // 添加按钮
                    _buildAddButton(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
      child: Row(
        children: [
          // 占位，保持标题居中
          SizedBox(width: 44.w),

          // 中间标题
          Expanded(
            child: Center(
              child: Text(
                '添加药物',
                style: TextUtil.base
                    .sp(18)
                    .semiBold
                    .withColor(widget.color),
              ),
            ),
          ),

          // 右侧关闭按钮
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                CupertinoIcons.xmark,
                size: 20.sp,
                color: widget.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFirstRow() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          // 第一行：药品名称和数量
          Row(
            children: [
              // 药品名称输入框
              Expanded(
                flex: 3,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  decoration: BoxDecoration(
                    color: CupertinoColors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: ColorUtil.borderGrey,
                      width: 1.w,
                    ),
                  ),
                  child: CupertinoTextField(
                    controller: _medicineNameController,
                    placeholder: '请输入药品名称',
                    placeholderStyle: TextUtil.base
                        .sp(16)
                        .withColor(CupertinoColors.systemGrey),
                    style: TextUtil.base
                        .sp(16)
                        .withColor(widget.color),
                    decoration: null,
                    padding: EdgeInsets.zero,
                  ),
                ),
              ),

              SizedBox(width: 8.w),

              // 数量输入框
              Expanded(
                flex: 2,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  decoration: BoxDecoration(
                    color: CupertinoColors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: ColorUtil.borderGrey,
                      width: 1.w,
                    ),
                  ),
                  child: CupertinoTextField(
                    controller: _quantityController,
                    placeholder: '数量',
                    placeholderStyle: TextUtil.base
                        .sp(16)
                        .withColor(CupertinoColors.systemGrey),
                    style: TextUtil.base
                        .sp(16)
                        .withColor(widget.color),
                    decoration: null,
                    padding: EdgeInsets.zero,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // 第二行：单位、时间、设置提示按钮
          Row(
            children: [
              // 单位选择框
              Expanded(
                flex: 2,
                child: GestureDetector(
                  onTap: _showUnitPicker,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                    decoration: BoxDecoration(
                      color: CupertinoColors.white,
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color: ColorUtil.borderGrey,
                        width: 1.w,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            _units[_selectedUnitIndex],
                            style: TextUtil.base
                                .sp(16)
                                .withColor(widget.color),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Icon(
                          CupertinoIcons.chevron_down,
                          size: 16.sp,
                          color: CupertinoColors.systemGrey,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              SizedBox(width: 8.w),

              // 时间选择框
              Expanded(
                flex: 2,
                child: GestureDetector(
                  onTap: _showTimePicker,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                    decoration: BoxDecoration(
                      color: CupertinoColors.white,
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color: ColorUtil.borderGrey,
                        width: 1.w,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            _timeController.text.isEmpty ? '选择时间' : _timeController.text,
                            style: TextUtil.base
                                .sp(16)
                                .withColor(_timeController.text.isEmpty
                                    ? CupertinoColors.systemGrey
                                    : widget.color),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Icon(
                          CupertinoIcons.clock,
                          size: 16.sp,
                          color: CupertinoColors.systemGrey,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              SizedBox(width: 8.w),

              // 设置提示按钮
              Container(
                width: 50.w,
                height: 48.h,
                decoration: BoxDecoration(
                  color: widget.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color: widget.color.withValues(alpha: 0.3),
                    width: 1.w,
                  ),
                ),
                child: CupertinoButton(
                  padding: EdgeInsets.all(4.w),
                  onPressed: () {
                    // TODO: 实现设置提示功能
                  },
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '设置',
                        style: TextUtil.base
                            .sp(9)
                            .withColor(widget.color),
                      ),
                      Text(
                        '提示',
                        style: TextUtil.base
                            .sp(9)
                            .withColor(widget.color),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCycleSelection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 周期性标签
          Text(
            '周期性',
            style: TextUtil.base
                .sp(16)
                .semiBold
                .withColor(widget.color),
          ),

          SizedBox(height: 12.h),

          // 一周七天的圆形按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(7, (index) {
              final cycle = index + 1; // 1-7代表周一到周日
              final isSelected = _selectedCycles.contains(cycle);

              return GestureDetector(
                onTap: () => _toggleCycle(cycle),
                child: Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    color: isSelected ? widget.color : CupertinoColors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    border: isSelected
                        ? null
                        : Border.all(
                            color: widget.color,
                            width: 1.w,
                          ),
                  ),
                  child: Center(
                    child: Text(
                      _weekDays[index],
                      style: TextUtil.base
                          .sp(14)
                          .semiBold
                          .withColor(isSelected ? CupertinoColors.white : widget.color),
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildAddButton() {
    return Container(
      padding: EdgeInsets.all(24.w),
      child: SizedBox(
        width: double.infinity,
        height: 48.h,
        child: CupertinoButton(
          color: widget.color,
          borderRadius: BorderRadius.circular(99.r),
          padding: EdgeInsets.zero,
          onPressed: _onAddMedicine,
          child: Text(
            '添加',
            style: TextUtil.base
                .sp(16)
                .semiBold
                .withColor(CupertinoColors.white),
          ),
        ),
      ),
    );
  }
}
