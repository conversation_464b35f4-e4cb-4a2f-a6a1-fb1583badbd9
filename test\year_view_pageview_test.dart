import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('Year View PageView Tests', () {
    testWidgets('Should support year navigation through PageView swipe', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 15); // 初始在2024年
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthTitleHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      monthTitleHistory.add(month);
                      print('Month changed to: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                    swipeDirection: SwipeDirection.horizontal,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（年视图，显示2024年）
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年'));
      
      // 清空历史记录
      monthTitleHistory.clear();
      
      // 在年视图中向右滑动（切换到下一年）
      final pageViewFinder = find.byType(PageView);
      expect(pageViewFinder, findsOneWidget);

      await tester.fling(pageViewFinder, Offset(-300, 0), 1000); // 快速滑动切换到下一年
      await tester.pumpAndSettle();
      
      // 验证切换到2025年
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2025年'));
      
      print('Successfully navigated to next year: 2025');
    });

    testWidgets('Should support vertical year navigation when configured', (WidgetTester tester) async {
      final testDate = DateTime(2023, 8, 20); // 初始在2023年
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;

      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                    swipeDirection: SwipeDirection.vertical,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态和垂直滑动方向配置
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2023年'));

      // 验证PageView使用垂直滑动方向
      final pageViewFinder = find.byType(PageView);
      expect(pageViewFinder, findsOneWidget);

      final pageView = tester.widget<PageView>(pageViewFinder);
      expect(pageView.scrollDirection, equals(Axis.vertical));

      print('Successfully verified vertical scroll direction for year view');
    });

    testWidgets('Should maintain month click functionality in PageView year view', (WidgetTester tester) async {
      final testDate = DateTime(2024, 12, 10); // 初始在2024年12月
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（年视图只显示年份）
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年'));
      
      // 在年视图中点击"三月"
      final marchFinder = find.text('三月');
      expect(marchFinder, findsOneWidget);
      
      await tester.tap(marchFinder);
      await tester.pumpAndSettle();
      
      // 验证切换到月视图并显示3月
      expect(currentFormat, equals(CustomCalendarFormat.month));
      expect(currentMonthTitle, contains('2024年3月'));
      
      // 验证selectedDay保持不变
      expect(selectedDay, isNull); // 没有点击日期，所以selectedDay应该还是null
      
      print('Month click functionality maintained in PageView year view');
    });

    testWidgets('Should handle multiple year navigation correctly', (WidgetTester tester) async {
      final testDate = DateTime(2022, 5, 15); // 初始在2022年
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> yearHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      if (month.contains('年')) {
                        final year = month.split('年')[0];
                        if (yearHistory.isEmpty || yearHistory.last != year) {
                          yearHistory.add(year);
                        }
                      }
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                    swipeDirection: SwipeDirection.horizontal,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2022年'));
      
      // 清空历史记录
      yearHistory.clear();
      
      final pageViewFinder = find.byType(PageView);
      
      // 向前导航两年
      await tester.fling(pageViewFinder, Offset(-300, 0), 1000); // 2023年
      await tester.pumpAndSettle();

      await tester.fling(pageViewFinder, Offset(-300, 0), 1000); // 2024年
      await tester.pumpAndSettle();
      
      // 验证最终状态
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年'));
      
      print('Multiple year navigation completed successfully');
      print('Year history: $yearHistory');
    });

    testWidgets('Should use correct scroll direction based on swipeDirection setting', (WidgetTester tester) async {
      final testDate = DateTime(2024, 1, 1);
      
      // 测试水平滑动方向
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    initialFormat: CustomCalendarFormat.year,
                    swipeDirection: SwipeDirection.horizontal,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 查找PageView并验证滑动方向
      final pageViewWidget = tester.widget<PageView>(find.byType(PageView));
      expect(pageViewWidget.scrollDirection, equals(Axis.horizontal));
      
      print('Horizontal scroll direction verified for year view');
    });
  });
}
