# 网络请求模块文档

## 概述

本模块提供了完整的网络请求封装，基于 Dio 库构建，集成了项目的认证系统和错误处理机制。

## 文件结构

```
lib/api/
├── dio_client.dart         # Dio客户端封装，包含拦截器配置
├── api_routes.dart         # API路由配置，定义所有接口地址
├── api_service.dart        # 接口调用封装，提供具体的API方法
├── api_example.dart        # 使用示例代码
└── README.md              # 本文档
```

## 核心特性

### 🔧 DioClient 特性
- **单例模式**: 全局统一的网络请求配置
- **自动认证**: 自动添加 Authorization 头部
- **统一错误处理**: 集成 ToastUtil 进行错误提示
- **请求日志**: 调试模式下自动打印请求和响应信息
- **超时配置**: 可配置的连接、发送、接收超时时间

### 🛣️ ApiRoutes 特性
- **语义化命名**: 清晰的路由常量定义
- **动态路由**: 支持参数化路由构建
- **分类管理**: 按功能模块分类组织路由
- **路由验证**: 提供认证检查和描述方法

### 🌐 ApiService 特性
- **类型安全**: 泛型支持，确保类型安全
- **响应封装**: 统一的 ApiResponse 响应格式
- **错误处理**: 完善的异常捕获和处理
- **进度回调**: 文件上传支持进度监听

## 快速开始

### 1. 基础配置

在使用前，确保在 `dio_client.dart` 中配置正确的基础URL：

```dart
// 在 DioClient._setupInterceptors() 方法中
_dio.options = BaseOptions(
  baseUrl: 'https://your-api-domain.com', // 替换为实际的API地址
  // ... 其他配置
);
```

### 2. 基本使用

```dart
import '../api/api_service.dart';

// 获取ApiService实例
final apiService = ApiService.instance;

// 用户登录
final loginResponse = await apiService.login(
  username: 'your_username',
  password: 'your_password',
);

if (loginResponse.success) {
  print('登录成功: ${loginResponse.data?.username}');
} else {
  print('登录失败: ${loginResponse.message}');
}
```

### 3. 集成认证服务

```dart
import '../services/auth_service.dart';

// 使用集成了网络请求的认证服务
final success = await AuthService.instance.login(
  username: 'your_username',
  password: 'your_password',
);

if (success) {
  print('登录成功');
} else {
  print('登录失败');
}
```

## 详细使用指南

### 认证相关接口

```dart
// 用户名密码登录
final loginResponse = await ApiService.instance.login(
  username: 'username',
  password: 'password',
);

// 验证码登录
final codeLoginResponse = await ApiService.instance.loginWithCode(
  phone: '13800138000',
  code: '123456',
);

// 发送验证码
final sendCodeResponse = await ApiService.instance.sendVerificationCode(
  phone: '13800138000',
  type: 'login',
);

// 用户注册
final registerResponse = await ApiService.instance.register(
  username: 'newuser',
  password: 'password123',
  phone: '13800138000',
  code: '123456',
  email: '<EMAIL>',
);

// 用户登出
final logoutResponse = await ApiService.instance.logout();
```

### 用户信息接口

```dart
// 获取用户信息
final userInfoResponse = await ApiService.instance.getUserInfo();

// 更新用户信息
final updateResponse = await ApiService.instance.updateUserInfo(
  nickname: '新昵称',
  email: '<EMAIL>',
);

// 修改密码
final changePasswordResponse = await ApiService.instance.changePassword(
  oldPassword: 'oldpass',
  newPassword: 'newpass',
);
```

### 文件上传接口

```dart
// 上传图片
final uploadImageResponse = await ApiService.instance.uploadImage(
  imagePath: '/path/to/image.jpg',
  fileName: 'avatar.jpg',
  onProgress: (sent, total) {
    final progress = (sent / total * 100).toInt();
    print('上传进度: $progress%');
  },
);

// 上传文件
final uploadFileResponse = await ApiService.instance.uploadFile(
  filePath: '/path/to/file.pdf',
  onProgress: (sent, total) {
    print('上传进度: ${(sent / total * 100).toInt()}%');
  },
);
```

### 通用请求方法

```dart
// GET请求
final getResponse = await ApiService.instance.get<List<dynamic>>(
  '/api/v1/data/list',
  queryParameters: {'page': 1, 'pageSize': 20},
  fromJson: (data) => data as List<dynamic>,
);

// POST请求
final postResponse = await ApiService.instance.post<bool>(
  '/api/v1/data/create',
  data: {'name': 'test', 'value': 123},
  fromJson: (data) => data as bool,
);

// PUT请求
final putResponse = await ApiService.instance.put<Map<String, dynamic>>(
  '/api/v1/data/update',
  data: {'id': 1, 'name': 'updated'},
  fromJson: (data) => data as Map<String, dynamic>,
);

// DELETE请求
final deleteResponse = await ApiService.instance.delete<bool>(
  '/api/v1/data/delete',
  data: {'id': 1},
  fromJson: (data) => data as bool,
);
```

## 错误处理

### 自动错误处理

DioClient 会自动处理常见的网络错误：

- **连接超时**: 显示网络连接超时提示
- **401 未授权**: 自动清除本地认证信息并提示重新登录
- **403 权限不足**: 显示权限不足提示
- **404 资源不存在**: 显示资源不存在提示
- **5xx 服务器错误**: 显示服务器错误提示

### 手动错误处理

```dart
try {
  final response = await ApiService.instance.getUserInfo();
  
  if (response.success && response.data != null) {
    // 处理成功响应
    final user = response.data!;
    print('用户名: ${user.username}');
  } else {
    // 处理业务错误
    print('获取用户信息失败: ${response.message}');
  }
} catch (e) {
  // 处理异常
  print('请求异常: $e');
}
```

### 重试机制

参考 `api_example.dart` 中的重试机制实现：

```dart
final userInfo = await ApiExample.requestWithRetry<UserModel>(
  () => ApiService.instance.getUserInfo(),
  maxRetries: 3,
  delay: Duration(seconds: 2),
);
```

## 配置说明

### 超时配置

```dart
// 设置超时时间
DioClient.instance.setTimeout(
  connectTimeout: Duration(seconds: 30),
  receiveTimeout: Duration(seconds: 30),
  sendTimeout: Duration(seconds: 30),
);
```

### 基础URL配置

```dart
// 更新基础URL
DioClient.instance.updateBaseUrl('https://new-api-domain.com');
```

### 自定义拦截器

```dart
// 添加自定义拦截器
DioClient.instance.addInterceptor(
  InterceptorsWrapper(
    onRequest: (options, handler) {
      // 自定义请求处理
      handler.next(options);
    },
  ),
);
```

## 最佳实践

### 1. 错误处理
- 始终检查 `response.success` 状态
- 为用户提供友好的错误提示
- 记录详细的错误日志用于调试

### 2. 加载状态
- 在请求开始时显示加载提示
- 在请求完成后隐藏加载提示
- 使用 ToastUtil 提供统一的用户反馈

### 3. 数据验证
- 在发送请求前验证输入数据
- 检查响应数据的完整性
- 处理空数据和异常情况

### 4. 性能优化
- 合理使用缓存机制
- 避免重复的网络请求
- 实现请求去重和防抖

## 注意事项

1. **认证令牌**: 确保 AuthTokenModel 正确保存和更新
2. **网络权限**: 确保应用具有网络访问权限
3. **HTTPS**: 生产环境建议使用 HTTPS 协议
4. **错误码**: 根据后端API定义调整错误处理逻辑
5. **数据格式**: 确保前后端数据格式一致

## 相关文档

- [ToastUtil 文档](../utils/toast_util.md)
- [AuthService 文档](../services/auth_service.md)
- [用户模型文档](../models/user_models.md)
- [Dio 官方文档](https://pub.dev/packages/dio)
