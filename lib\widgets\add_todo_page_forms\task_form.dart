import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import '../../utils/color_util.dart';
import '../../utils/text_util.dart';
import '../../utils/toast_util.dart';
import '../symptom_bottom_sheets/medicine_add_bottom_sheet.dart';
import 'task_detail_section.dart';
import 'task_reminder_section.dart';

/// 药物信息类
class MedicineInfo {
  final String name;
  final String quantity;
  final String unit;
  final String time;
  final List<int> cycle;

  MedicineInfo({
    required this.name,
    required this.quantity,
    required this.unit,
    required this.time,
    required this.cycle,
  });
}

/// 任务表单组件
class TaskForm extends StatefulWidget {
  final Color cardColor;
  final Color cardBgColor;

  const TaskForm({
    super.key,
    required this.cardColor,
    required this.cardBgColor,
  });

  @override
  State<TaskForm> createState() => _TaskFormState();
}

class _TaskFormState extends State<TaskForm> {
  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  // 表单控制器
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endTimeController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _travelTimeController = TextEditingController();
  final TextEditingController _reminderAdvanceController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _customTaskNameController =
      TextEditingController();

  // 全天开关状态
  bool _isAllDay = false;

  // 任务状态
  String _selectedTaskState = '自定义';
  final List<String> _taskStates = ['自定义', '吃药', '补充营养'];

  // 药物列表
  final List<MedicineInfo> _medicineList = [
    MedicineInfo(
      name: '药1',
      quantity: '1',
      unit: '颗',
      time: '9:00',
      cycle: [1, 2, 3, 4, 5, 6, 7],
    ),
    MedicineInfo(
      name: '药2',
      quantity: '1',
      unit: '瓶',
      time: '22:00',
      cycle: [1, 2, 3, 4, 5, 6, 7],
    ),
  ];

  // 营养补充列表
  final List<MedicineInfo> _nutritionList = [
    MedicineInfo(
      name: 'DHA',
      quantity: '1',
      unit: '颗',
      time: '9:00',
      cycle: [1, 2, 3, 4, 5, 6, 7],
    ),
    MedicineInfo(
      name: '维生素D',
      quantity: '1',
      unit: '颗',
      time: '9:00',
      cycle: [1, 2, 3, 4, 5, 6, 7],
    ),
  ];

  @override
  void initState() {
    super.initState();
    // 使用 Scaffold 的 resizeToAvoidBottomInset
  }

  @override
  void dispose() {
    // 释放滚动控制器
    _scrollController.dispose();

    // 释放表单控制器
    _startTimeController.dispose();
    _endTimeController.dispose();
    _locationController.dispose();
    _travelTimeController.dispose();
    _reminderAdvanceController.dispose();
    _notesController.dispose();
    _customTaskNameController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: widget.cardBgColor,
      body: GestureDetector(
        onTap: () {
          // 点击空白处收起键盘
          FocusScope.of(context).unfocus();
        },
        child: _buildScrollView(),
      ),
    );
  }

  Widget _buildScrollView() {
    return Container(
      color: widget.cardBgColor,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: EdgeInsets.only(
          left: 24.w,
          right: 24.w,
          bottom: 100.h, // 底部间距，防止软键盘遮挡
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 任务状态选择 section
            _buildTaskStateSection(),
            SizedBox(height: 24.h),

            if (_selectedTaskState == '自定义') ...[
              TaskDetailSection(
                cardColor: widget.cardColor,
                isAllDay: _isAllDay,
                onAllDayChanged: (value) {
                  setState(() {
                    _isAllDay = value;
                  });
                },
                startTimeController: _startTimeController,
                endTimeController: _endTimeController,
                locationController: _locationController,
              ),
              SizedBox(height: 24.h),

              // 任务前提示
              TaskReminderSection(
                cardColor: widget.cardColor,
                travelTimeController: _travelTimeController,
                reminderAdvanceController: _reminderAdvanceController,
              ),
              SizedBox(height: 24.h),

              // 备注
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(color: ColorUtil.borderGrey),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Text(
                        '备注',
                        style: TextUtil.base
                            .sp(16)
                            .semiBold
                            .withColor(widget.cardColor),
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Container(
                      width: double.infinity,
                      height: 120.h,
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        border: Border.all(color: widget.cardColor),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: CupertinoTextField(
                        controller: _notesController,
                        placeholder: '记录一些重要信息',
                        placeholderStyle: TextUtil.base
                            .sp(14)
                            .withColor(CupertinoColors.systemGrey),
                        style: TextUtil.base.sp(14).withColor(widget.cardColor),
                        decoration: null,
                        padding: EdgeInsets.zero,
                        maxLines: null,
                        expands: true,
                        textAlignVertical: TextAlignVertical.top,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            // 药物 UI section（条件显示）
            if (_selectedTaskState == '吃药') ...[
              _buildMedicineSection(),
              SizedBox(height: 24.h),
            ],

            // 营养补充 UI section（条件显示）
            if (_selectedTaskState == '补充营养') ...[
              _buildNutritionSection(),
              SizedBox(height: 24.h),
            ],

            // 保存按钮
            SizedBox(height: 32.h),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  // 构建任务状态选择 section
  Widget _buildTaskStateSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: GestureDetector(
        onTap: _showTaskStateSelector,
        child: Row(
          children: [
            Expanded(
              child: _selectedTaskState == '自定义'
                  ? CupertinoTextField(
                      controller: _customTaskNameController,
                      placeholder: '请输入任务名称',
                      placeholderStyle: TextUtil.base
                          .sp(16)
                          .withColor(CupertinoColors.systemGrey),
                      style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      decoration: null,
                      padding: EdgeInsets.zero,
                    )
                  : Text(
                      _selectedTaskState,
                      style: TextUtil.base.sp(16).withColor(widget.cardColor),
                    ),
            ),
            Icon(
              CupertinoIcons.chevron_down,
              size: 20.sp,
              color: widget.cardColor,
            ),
          ],
        ),
      ),
    );
  }

  // 显示任务状态选择器
  void _showTaskStateSelector() {
    // 临时变量存储用户的选择
    String tempSelectedState = _selectedTaskState;
    // 获取当前状态在列表中的索引
    int initialIndex = _taskStates.indexOf(_selectedTaskState);
    if (initialIndex == -1) initialIndex = 0; // 如果找不到，默认选择第一个

    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          color: CupertinoColors.white,
          child: Column(
            children: [
              Container(
                height: 50.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        '取消',
                        style: TextUtil.base
                            .sp(16)
                            .withColor(CupertinoColors.systemGrey),
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        // 只有点击确定时才应用选择的状态
                        setState(() {
                          _selectedTaskState = tempSelectedState;
                        });
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoPicker(
                  scrollController: FixedExtentScrollController(initialItem: initialIndex),
                  itemExtent: 32.h,
                  onSelectedItemChanged: (int index) {
                    // 只更新临时变量，不立即修改状态
                    tempSelectedState = _taskStates[index];
                  },
                  children: _taskStates.map((state) {
                    return Center(
                      child: Text(state, style: TextUtil.base.sp(16)),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建药物 section
  Widget _buildMedicineSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              '药物',
              style: TextUtil.base.sp(16).semiBold.withColor(widget.cardColor),
            ),
          ),
          SizedBox(height: 16.h),

          // 药物标签列表
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              // 显示已添加的药物
              ..._medicineList.map((medicine) => _buildMedicineTag(
                medicine.name,
                '${medicine.quantity} ${medicine.unit}',
                medicine.time,
              )),
              // 添加药物按钮
              _buildAddMedicineButton(),
            ],
          ),
        ],
      ),
    );
  }

  // 构建药物标签
  Widget _buildMedicineTag(String name, String dosage, String time) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        border: Border.all(color: widget.cardColor),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Text(
        '$name $dosage $time',
        style: TextUtil.base.sp(12).withColor(widget.cardColor),
      ),
    );
  }

  // 构建添加药物按钮
  Widget _buildAddMedicineButton() {
    return GestureDetector(
      onTap: _showMedicineAddBottomSheet,
      child: Container(
        width: 32.w,
        height: 32.w,
        decoration: BoxDecoration(
          border: Border.all(color: widget.cardColor),
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Icon(CupertinoIcons.add, size: 16.sp, color: widget.cardColor),
      ),
    );
  }

  // 显示药物添加BottomSheet
  void _showMedicineAddBottomSheet() {
    showCupertinoModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return MedicineAddBottomSheet(
          onMedicineAdded: (String medicineName, String quantity, String unit, String time, List<int> cycle) {
            _addMedicine(medicineName, quantity, unit, time, cycle);
          },
          color: widget.cardColor,
          bgColor: widget.cardBgColor,
        );
      },
    );
  }

  // 添加药物到列表
  void _addMedicine(String name, String quantity, String unit, String time, List<int> cycle) {
    setState(() {
      _medicineList.add(MedicineInfo(
        name: name,
        quantity: quantity,
        unit: unit,
        time: time,
        cycle: cycle,
      ));
    });
  }

  // 构建营养补充 section
  Widget _buildNutritionSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              '营养补充',
              style: TextUtil.base.sp(16).semiBold.withColor(widget.cardColor),
            ),
          ),
          SizedBox(height: 16.h),

          // 营养补充标签列表
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              // 显示已添加的营养补充
              ..._nutritionList.map((nutrition) => _buildNutritionTag(
                nutrition.name,
                '${nutrition.quantity} ${nutrition.unit}',
                nutrition.time,
              )),
              // 添加营养补充按钮
              _buildAddNutritionButton(),
            ],
          ),
        ],
      ),
    );
  }

  // 构建营养补充标签
  Widget _buildNutritionTag(String name, String dosage, String time) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        border: Border.all(color: widget.cardColor),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Text(
        '$name $dosage $time',
        style: TextUtil.base.sp(12).withColor(widget.cardColor),
      ),
    );
  }

  // 构建添加营养补充按钮
  Widget _buildAddNutritionButton() {
    return GestureDetector(
      onTap: _showNutritionAddBottomSheet,
      child: Container(
        width: 32.w,
        height: 32.w,
        decoration: BoxDecoration(
          border: Border.all(color: widget.cardColor),
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Icon(
          CupertinoIcons.add,
          size: 16.sp,
          color: widget.cardColor,
        ),
      ),
    );
  }

  // 显示营养补充添加BottomSheet
  void _showNutritionAddBottomSheet() {
    showCupertinoModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return MedicineAddBottomSheet(
          onMedicineAdded: (String nutritionName, String quantity, String unit, String time, List<int> cycle) {
            _addNutrition(nutritionName, quantity, unit, time, cycle);
          },
          color: widget.cardColor,
          bgColor: widget.cardBgColor,
        );
      },
    );
  }

  // 添加营养补充到列表
  void _addNutrition(String name, String quantity, String unit, String time, List<int> cycle) {
    setState(() {
      _nutritionList.add(MedicineInfo(
        name: name,
        quantity: quantity,
        unit: unit,
        time: time,
        cycle: cycle,
      ));
    });
  }

  // 构建保存按钮
  Widget _buildSaveButton() {
    return Container(
      width: double.infinity,
      height: 48.h,
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        color: widget.cardColor,
        borderRadius: BorderRadius.circular(24.r),
        onPressed: _handleSave,
        child: Text(
          '保存',
          style: TextUtil.base
              .sp(16)
              .semiBold
              .withColor(CupertinoColors.white),
        ),
      ),
    );
  }

  // 处理保存逻辑
  void _handleSave() {
    // TODO: 实现保存逻辑
    // 这里可以添加表单验证和数据保存逻辑

    // 显示保存成功提示
    ToastUtil.showSaveSuccess();
    print('任务表单保存');
  }
}
