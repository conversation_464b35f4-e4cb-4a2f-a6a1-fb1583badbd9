import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_quill/flutter_quill.dart';
import '../utils/text_util.dart';
import '../utils/color_util.dart';
import '../utils/toast_util.dart';

class NoteEditorPage extends StatefulWidget {
  final Color cardColor;
  final Color cardBgColor;
  final String title;
  final String? initialContent;
  final Function(String title, String content)? onSave;

  const NoteEditorPage({
    super.key,
    required this.cardColor,
    required this.cardBgColor,
    required this.title,
    this.initialContent,
    this.onSave,
  });

  @override
  State<NoteEditorPage> createState() => _NoteEditorPageState();
}

class _NoteEditorPageState extends State<NoteEditorPage> {
  late QuillController _controller;
  late FocusNode _focusNode;
  late TextEditingController _titleController;
  late FocusNode _titleFocusNode;
  bool _showFormatToolbar = false; // 控制是否显示格式化子工具栏

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _titleController = TextEditingController(text: widget.title);
    _titleFocusNode = FocusNode();
    // 初始化编辑器内容
    Document document;
    if (widget.initialContent != null && widget.initialContent!.isNotEmpty) {
      // 如果有初始内容，解析为Delta格式
      try {
        final jsonData = widget.initialContent!.startsWith('[')
            ? widget.initialContent!
            : '[]';
        final List<dynamic> deltaList = jsonDecode(jsonData);
        document = Document.fromJson(deltaList);
      } catch (e) {
        // 如果解析失败，创建纯文本文档
        document = Document()..insert(0, widget.initialContent!);
      }
    } else {
      // 创建空文档
      document = Document();
    }

    _controller = QuillController(
      document: document,
      selection: const TextSelection.collapsed(offset: 0),
    );

    // 监听选择变化以更新按钮状态
    _controller.addListener(_onSelectionChanged);
  }

  void _onSelectionChanged() {
    // 当选择发生变化时，更新UI以反映当前位置的格式状态
    if (mounted) {
      setState(() {});
    }
  }

  /// 隐藏软键盘
  void _hideKeyboard() {
    SystemChannels.textInput.invokeMethod('TextInput.hide');
  }

  /// 显示软键盘并聚焦到编辑器
  void _showKeyboardAndFocus() {
    // 确保标题输入框失去焦点
    _titleFocusNode.unfocus();
    _focusNode.requestFocus();
  }

  @override
  void dispose() {
    _controller.removeListener(_onSelectionChanged);
    _controller.dispose();
    _focusNode.dispose();
    _titleController.dispose();
    _titleFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.white,
      child: SafeArea(
        child: Stack(
          children: [
            Column(
              children: [
                // 自定义导航栏
                _buildCustomNavigationBar(),

                // 编辑器区域
                Expanded(
                  child: Container(
                    padding: EdgeInsets.all(16.w),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16.w),
                        child: QuillEditor.basic(
                          controller: _controller,
                          focusNode: _focusNode,
                          config: QuillEditorConfig(
                            placeholder: '写一下',
                            customStyles: DefaultStyles(
                              placeHolder: DefaultTextBlockStyle(
                                TextUtil.base.sp(14).withColor(CupertinoColors.systemGrey),
                                HorizontalSpacing.zero,
                                VerticalSpacing.zero,
                                VerticalSpacing.zero,
                                null
                              ),
                            ),
                            onTapDown: (details, getPosition) {
                              // 如果格式化工具栏正在显示，阻止键盘弹出
                              if (_showFormatToolbar) {
                                _hideKeyboard();
                                return true; // 阻止默认行为
                              }
                              // 如果标题输入框有焦点，让标题失去焦点
                              if (_titleFocusNode.hasFocus) {
                                _titleFocusNode.unfocus();
                              }
                              return false; // 允许默认行为
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // 悬浮工具栏
            _buildFloatingToolbar(),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomNavigationBar() {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        border: Border(
          bottom: BorderSide(color: ColorUtil.borderGrey, width: 0.5.w),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 取消按钮
          GestureDetector(
            onTap: _onCancel,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Text(
                '取消',
                style: TextUtil.base.sp(16).medium.withColor(widget.cardColor),
              ),
            ),
          ),

          // 中间可编辑标题
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              child: CupertinoTextField(
                controller: _titleController,
                focusNode: _titleFocusNode,
                textAlign: TextAlign.center,
                style: TextUtil.base.sp(18).semiBold.withColor(widget.cardColor),
                decoration: const BoxDecoration(),
                placeholder: '输入标题',
                placeholderStyle: TextUtil.base.sp(18).semiBold.withColor(
                  widget.cardColor.withOpacity(0.5),
                ),
                maxLines: 1,
                textInputAction: TextInputAction.done,
                onSubmitted: (_) {
                  // 按回车键时保存并失去焦点
                  _titleFocusNode.unfocus();
                  _onSave();
                },
                onTap: () {
                  // 点击标题输入框时，隐藏格式化工具栏
                  if (_showFormatToolbar) {
                    setState(() {
                      _showFormatToolbar = false;
                    });
                  }
                },
              ),
            ),
          ),

          // 保存按钮
          GestureDetector(
            onTap: _onSave,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Text(
                '保存',
                style: TextUtil.base.sp(16).medium.withColor(widget.cardColor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingToolbar() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: widget.cardBgColor,
          border: Border(
            top: BorderSide(color: ColorUtil.borderGrey, width: 1.w),
          ),
          boxShadow: [
            BoxShadow(
              color: CupertinoColors.black.withValues(alpha: 0.1),
              blurRadius: 4.r,
              offset: Offset(0, -2.h),
            ),
          ],
        ),
        child: SafeArea(
          top: false,
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: _showFormatToolbar
                ? _buildFormatToolbar()
                : _buildMainToolbar(),
          ),
        ),
      ),
    );
  }

  Widget _buildMainToolbar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        children: [
          _buildToolbarButton(
            icon: CupertinoIcons.textformat,
            onTap: () {
              // 隐藏键盘并显示格式化工具栏
              _hideKeyboard();
              // 确保标题输入框失去焦点
              _titleFocusNode.unfocus();
              setState(() {
                _showFormatToolbar = true;
              });
            },
          ),
          SizedBox(width: 8.w),
          _buildToolbarButton(
            icon: CupertinoIcons.list_bullet,
            onTap: () {
              // TODO: 实现TodoList功能
            },
          ),
          SizedBox(width: 8.w),
          _buildToolbarButton(
            icon: CupertinoIcons.photo,
            onTap: () {
              // TODO: 实现图片功能
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: CupertinoColors.systemGrey6,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(icon, size: 20.sp, color: widget.cardColor),
      ),
    );
  }

  Widget _buildFormatToolbar() {
    return Container(
      decoration: BoxDecoration(
        color: widget.cardBgColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 第1行：关闭按钮和标题
          _buildFormatHeader(),
          SizedBox(height: 16.h),

          // 第2行：标题样式
          _buildHeadingRow(),
          SizedBox(height: 12.h),

          // 第3行：文本格式
          _buildTextFormatRow(),
          SizedBox(height: 12.h),

          // 第4行：列表和缩进
          _buildListAndIndentRow(),
        ],
      ),
    );
  }

  Widget _buildFormatHeader() {
    return Row(
      children: [
        Expanded(
          child: Text(
            '格式',
            style: TextUtil.base
                .sp(18)
                .semiBold
                .withColor(widget.cardColor),
          ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              _showFormatToolbar = false;
            });
            // 延迟一帧后重新聚焦编辑器，确保工具栏关闭动画完成
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showKeyboardAndFocus();
            });
          },
          child: Container(
            width: 32.w,
            height: 32.w,
            decoration: BoxDecoration(
              color: widget.cardColor.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                CupertinoIcons.xmark,
                size: 16.sp,
                color:  widget.cardColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeadingRow() {
    return Row(
      children: [
        Expanded(child: _buildHeadingButton('标题', 1)),
        SizedBox(width: 8.w),
        Expanded(child: _buildHeadingButton('小标题', 2)),
        SizedBox(width: 8.w),
        Expanded(child: _buildHeadingButton('副标题', 3)),
        SizedBox(width: 8.w),
        Expanded(child: _buildHeadingButton('正文', 0)),
      ],
    );
  }

  Widget _buildHeadingButton(String label, int level) {
    // 检查当前是否应用了该标题级别
    final style = _controller.getSelectionStyle();
    final isActive = _isHeadingActive(style, level);
    final textStyle0 = TextUtil.base
        .sp(14)
        .withColor(
          isActive ? CupertinoColors.white : CupertinoColors.black,
        );
    final textStyle1 = TextUtil.base
        .sp(18)
        .semiBold
        .withColor(
          isActive ? CupertinoColors.white : CupertinoColors.black,
        );
    final textStyle2 = TextUtil.base
        .sp(16)
        .medium
        .withColor(
          isActive ? CupertinoColors.white : CupertinoColors.black,
        );
    final textStyle3 = TextUtil.base
        .sp(14)
        .medium
        .withColor(
          isActive ? CupertinoColors.white : CupertinoColors.black,
        );

    return GestureDetector(
      onTap: () => _applyHeading(level),
      child: Container(
        height: 44.w,
        decoration: BoxDecoration(
          color: isActive ? widget.cardColor : CupertinoColors.transparent,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(
          child: Text(
            label,
            style: level == 0 ? textStyle0 : level == 1 ? textStyle1 : level == 2 ? textStyle2 : textStyle3,
          ),
        ),
      ),
    );
  }

  bool _isHeadingActive(Style style, int level) {
    final headerAttribute = style.attributes[Attribute.header.key];
    if (level == 0) {
      // 正文：检查是否没有任何标题格式
      // 根据 flutter_quill API 文档，标题格式存储在 "header" 键中
      return headerAttribute == null;
    } else {
      // 检查是否有对应级别的标题
      // 根据 API 文档：h1 对应 {"header": 1}, h2 对应 {"header": 2}, h3 对应 {"header": 3}
      return headerAttribute?.value == level;
    }
  }

  void _applyHeading(int level) {
    final selection = _controller.selection;
    if (selection.isValid) {
      if (level == 0) {
        // 移除标题格式 - 使用 Attribute.clone 传入 null 来移除格式
        _controller.formatSelection(Attribute.clone(Attribute.header, null));
      } else {
        // 应用标题格式
        final headerAttribute = level == 1
            ? Attribute.h1
            : level == 2
            ? Attribute.h2
            : Attribute.h3;
        _controller.formatSelection(headerAttribute);
      }

      // 触发UI更新以反映按钮状态变化
      setState(() {});
    }
  }

  Widget _buildTextFormatRow() {
    return Row(
      children: [
        Expanded(
          child: _buildFormatButton(
            icon: CupertinoIcons.bold,
            attribute: Attribute.bold,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildFormatButton(
            icon: CupertinoIcons.italic,
            attribute: Attribute.italic,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildFormatButton(
            icon: CupertinoIcons.underline,
            attribute: Attribute.underline,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildFormatButton(
            icon: CupertinoIcons.strikethrough,
            attribute: Attribute.strikeThrough,
          ),
        ),
      ],
    );
  }

  Widget _buildFormatButton({
    required IconData icon,
    required Attribute attribute,
  }) {
    // 检查当前选中文本是否已应用该格式
    final style = _controller.getSelectionStyle();
    final isActive = style.attributes.containsKey(attribute.key);

    return GestureDetector(
      onTap: () => _toggleFormat(attribute),
      child: Container(
        width: 44.w,
        height: 44.w,
        decoration: BoxDecoration(
          color: isActive ? widget.cardColor : CupertinoColors.transparent,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(child: _buildFormatIcon(icon, attribute, isActive)),
      ),
    );
  }

  Widget _buildFormatIcon(IconData icon, Attribute attribute, bool isActive) {
    // 根据不同的格式类型显示不同的图标样式
    if (attribute == Attribute.bold) {
      return Text(
        'B',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
          color: isActive ? CupertinoColors.white : CupertinoColors.black,
        ),
      );
    } else if (attribute == Attribute.italic) {
      return Text(
        'I',
        style: TextStyle(
          fontSize: 18.sp,
          fontStyle: FontStyle.italic,
          fontWeight: FontWeight.w500,
          color: isActive ? CupertinoColors.white : CupertinoColors.black,
        ),
      );
    } else if (attribute == Attribute.underline) {
      return Text(
        'U',
        style: TextStyle(
          fontSize: 18.sp,
          decoration: TextDecoration.underline,
          fontWeight: FontWeight.w500,
          color: isActive ? CupertinoColors.white : CupertinoColors.black,
        ),
      );
    } else if (attribute == Attribute.strikeThrough) {
      return Text(
        'S',
        style: TextStyle(
          fontSize: 18.sp,
          decoration: TextDecoration.lineThrough,
          fontWeight: FontWeight.w500,
          color: isActive ? CupertinoColors.white : CupertinoColors.black,
        ),
      );
    }

    return Icon(
      icon,
      size: 18.sp,
      color: isActive ? CupertinoColors.white : CupertinoColors.black,
    );
  }

  void _toggleFormat(Attribute attribute) {
    final style = _controller.getSelectionStyle();
    final isActive = style.attributes.containsKey(attribute.key);

    if (isActive) {
      // 如果已激活，则移除格式
      _controller.formatSelection(Attribute.clone(attribute, null));
    } else {
      // 如果未激活，则应用格式
      _controller.formatSelection(attribute);
    }

    // 触发UI更新以反映按钮状态变化
    setState(() {});
  }

  Widget _buildListAndIndentRow() {
    return Row(
      children: [
        Expanded(
          child: _buildListButtonWithIcon(
            iconBuilder: (isActive) => Icon(
              CupertinoIcons.list_bullet,
              color: isActive ? CupertinoColors.white : CupertinoColors.black,
              size: 20.sp,
            ),
            attribute: Attribute.ul,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildListButtonWithIcon(
            iconBuilder: (isActive) => Icon(
              CupertinoIcons.list_number,
              color: isActive ? CupertinoColors.white : CupertinoColors.black,
              size: 20.sp,
            ),
            attribute: Attribute.ol,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildIndentButton(
            icon: CupertinoIcons.decrease_indent,
            isIncrease: false,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildIndentButton(
            icon: CupertinoIcons.increase_indent,
            isIncrease: true,
          ),
        ),
      ],
    );
  }

  Widget _buildDashListIcon(bool isActive) {
    final iconColor = isActive ? CupertinoColors.white : CupertinoColors.black;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(width: 6.w, height: 1.5.h, color: iconColor),
            SizedBox(width: 4.w),
            Container(width: 12.w, height: 1.5.h, color: iconColor),
          ],
        ),
        SizedBox(height: 2.h),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(width: 6.w, height: 1.5.h, color: iconColor),
            SizedBox(width: 4.w),
            Container(width: 12.w, height: 1.5.h, color: iconColor),
          ],
        ),
      ],
    );
  }

  Widget _buildListButtonWithIcon({
    required Widget Function(bool isActive) iconBuilder,
    required Attribute attribute,
  }) {
    final style = _controller.getSelectionStyle();
    final isActive = style.attributes.containsKey(attribute.key);

    return GestureDetector(
      onTap: () => _toggleList(attribute),
      child: Container(
        width: 44.w,
        height: 44.w,
        decoration: BoxDecoration(
          color: isActive ? widget.cardColor : CupertinoColors.transparent,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(child: iconBuilder(isActive)),
      ),
    );
  }

  Widget _buildIndentButton({
    required IconData icon,
    required bool isIncrease,
  }) {
    return GestureDetector(
      onTap: () => _applyIndent(isIncrease),
      child: Container(
        width: 44.w,
        height: 44.w,
        decoration: BoxDecoration(
          color: CupertinoColors.transparent,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(
          child: Icon(icon, size: 18.sp, color: CupertinoColors.black),
        ),
      ),
    );
  }

  void _toggleList(Attribute attribute) {
    final style = _controller.getSelectionStyle();
    final isActive = style.attributes.containsKey(attribute.key);

    if (isActive) {
      // 如果已激活，则移除列表格式
      _controller.formatSelection(Attribute.clone(attribute, null));
    } else {
      // 如果未激活，则应用列表格式
      _controller.formatSelection(attribute);
    }

    // 触发UI更新以反映按钮状态变化
    setState(() {});
  }

  void _applyIndent(bool isIncrease) {
    if (isIncrease) {
      _controller.formatSelection(Attribute.indentL1);
    } else {
      _controller.formatSelection(Attribute.indent);
    }

    // 触发UI更新以反映按钮状态变化
    setState(() {});
  }

  void _onCancel() {
    // 检查是否有未保存的更改
    if (_controller.document.length > 1) {
      _showDiscardDialog();
    } else {
      Navigator.of(context).pop();
    }
  }

  void _onSave() {
    try {
      // 获取文档内容
      final document = _controller.document;
      final jsonContent = jsonEncode(document.toDelta().toJson());

      // 获取纯文本内容用于预览
      final plainText = document.toPlainText().trim();

      if (plainText.isEmpty) {
        ToastUtil.showError('请输入笔记内容');
        return;
      }

      // 获取编辑后的标题
      final editedTitle = _titleController.text.trim();
      if (editedTitle.isEmpty) {
        ToastUtil.showError('请输入标题');
        return;
      }

      // 调用保存回调
      if (widget.onSave != null) {
        widget.onSave!(editedTitle, jsonContent);
      }

      ToastUtil.showSuccess('笔记保存成功');
      Navigator.of(context).pop();
    } catch (e) {
      ToastUtil.showError('保存失败：${e.toString()}');
    }
  }

  void _showDiscardDialog() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('放弃更改'),
        content: Text('您有未保存的更改，确定要放弃吗？'),
        actions: [
          CupertinoDialogAction(
            child: Text('取消'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: Text('放弃'),
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              Navigator.of(context).pop(); // 关闭编辑器
            },
          ),
        ],
      ),
    );
  }
}
