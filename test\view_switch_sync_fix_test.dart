import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('SwipeableCalendar View Switch Sync Fix Tests', () {
    testWidgets('Should maintain selectedDay when switching views', (WidgetTester tester) async {
      final testDate = DateTime(2024, 1, 20); // 2024年1月20日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;

      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（月视图）
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(currentMonthTitle, contains('2024年1月'));
      expect(currentFormat, equals(CustomCalendarFormat.month));

      // 在月视图中点击选择1月20日
      final dayFinder20 = find.text('20');
      if (tester.any(dayFinder20)) {
        await tester.tap(dayFinder20);
        await tester.pumpAndSettle();

        // 验证选中的日期正确
        expect(selectedDay, isNotNull);
        expect(selectedDay!.day, equals(20));
        expect(selectedDay!.month, equals(1));
        expect(selectedDay!.year, equals(2024));

        print('Selected date in month view: ${selectedDay}');
      }

      // 验证selectedDay在视图切换后保持不变
      // 这个测试主要验证状态管理的正确性
      expect(selectedDay!.day, equals(20));
      expect(selectedDay!.month, equals(1));
      expect(selectedDay!.year, equals(2024));

      print('SelectedDay correctly maintained');
    });

    testWidgets('Should handle date calculation correctly for different views', (WidgetTester tester) async {
      final testDate = DateTime(2024, 3, 15); // 2024年3月15日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;

      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.week,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（周视图）
      expect(currentFormat, equals(CustomCalendarFormat.week));
      expect(currentMonthTitle, contains('2024年3月'));

      // 在周视图中点击选择3月17日
      final dayFinder17 = find.text('17');
      if (tester.any(dayFinder17)) {
        await tester.tap(dayFinder17);
        await tester.pumpAndSettle();

        expect(selectedDay!.day, equals(17));
        expect(selectedDay!.month, equals(3));
      }

      // 验证日期计算的正确性
      expect(selectedDay!.day, equals(17));
      expect(selectedDay!.month, equals(3));
      expect(selectedDay!.year, equals(2024));

      print('Date calculation works correctly');
    });

    testWidgets('Should maintain correct month display', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 25); // 2024年6月25日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;

      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 在月视图中选择日期
      final dayFinder28 = find.text('28');
      if (tester.any(dayFinder28)) {
        await tester.tap(dayFinder28);
        await tester.pumpAndSettle();

        expect(selectedDay!.day, equals(28));
        expect(selectedDay!.month, equals(6));
      }

      // 验证月份显示正确
      expect(currentMonthTitle, contains('2024年6月'));
      expect(selectedDay!.day, equals(28));

      print('Month display is correct');
    });
  });
}
