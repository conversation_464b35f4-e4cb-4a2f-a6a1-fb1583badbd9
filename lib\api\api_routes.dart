/// API路由配置类，定义所有接口地址常量
class ApiRoutes {
  ApiRoutes._();

  // ==================== 基础配置 ====================
  
  /// API版本
  static const String apiVersion = 'v1';
  
  /// API基础路径
  static const String basePath = '/api/$apiVersion';

  // ==================== 认证相关 ====================

  /// 邮箱密码登录
  static const String login = '/api/auth/login/';

  /// 邮箱验证码登录
  static const String loginWithEmailCode = '/api/auth/login/email-code/';

  /// 用户登出
  static const String logout = '/api/auth/logout/';

  /// 刷新token
  static const String refreshToken = '/api/auth/refresh/';

  /// 发送邮箱验证码
  static const String sendVerificationCode = '/api/auth/send-code/';

  /// 验证Token有效性
  static const String verifyToken = '/api/auth/verify/';

  // ==================== 用户相关 ====================
  
  /// 获取用户信息
  static const String getUserInfo = '$basePath/user/profile';
  
  /// 更新用户信息
  static const String updateUserInfo = '$basePath/user/profile';
  
  /// 上传头像
  static const String uploadAvatar = '$basePath/user/avatar';
  
  /// 修改密码
  static const String changePassword = '$basePath/user/change-password';
  
  /// 绑定手机号
  static const String bindPhone = '$basePath/user/bind-phone';
  
  /// 绑定邮箱
  static const String bindEmail = '$basePath/user/bind-email';

  // ==================== 文件上传 ====================
  
  /// 通用文件上传
  static const String uploadFile = '$basePath/upload/file';
  
  /// 图片上传
  static const String uploadImage = '$basePath/upload/image';
  
  /// 批量文件上传
  static const String uploadMultipleFiles = '$basePath/upload/multiple';

  // ==================== 数据同步 ====================
  
  /// 同步用户数据
  static const String syncUserData = '$basePath/sync/user-data';
  
  /// 获取同步状态
  static const String getSyncStatus = '$basePath/sync/status';
  
  /// 强制同步
  static const String forceSync = '$basePath/sync/force';

  // ==================== 系统配置 ====================
  
  /// 获取应用配置
  static const String getAppConfig = '$basePath/config/app';
  
  /// 检查更新
  static const String checkUpdate = '$basePath/config/check-update';
  
  /// 获取服务条款
  static const String getTermsOfService = '$basePath/config/terms';
  
  /// 获取隐私政策
  static const String getPrivacyPolicy = '$basePath/config/privacy';

  // ==================== 反馈相关 ====================
  
  /// 提交反馈
  static const String submitFeedback = '$basePath/feedback/submit';
  
  /// 获取反馈列表
  static const String getFeedbackList = '$basePath/feedback/list';
  
  /// 获取反馈详情
  static const String getFeedbackDetail = '$basePath/feedback/detail';

  // ==================== 通知相关 ====================
  
  /// 获取通知列表
  static const String getNotifications = '$basePath/notifications';
  
  /// 标记通知已读
  static const String markNotificationRead = '$basePath/notifications/read';
  
  /// 删除通知
  static const String deleteNotification = '$basePath/notifications/delete';
  
  /// 获取未读通知数量
  static const String getUnreadCount = '$basePath/notifications/unread-count';

  // ==================== 动态路由构建方法 ====================
  
  /// 构建用户详情路由
  static String getUserDetail(String userId) => '$basePath/user/$userId';
  
  /// 构建反馈详情路由
  static String getFeedbackDetailById(String feedbackId) => '$basePath/feedback/$feedbackId';
  
  /// 构建通知详情路由
  static String getNotificationDetail(String notificationId) => '$basePath/notifications/$notificationId';
  
  /// 构建文件下载路由
  static String getFileDownload(String fileId) => '$basePath/files/$fileId/download';
  
  /// 构建分页查询路由
  static String buildPaginatedRoute(String baseRoute, {
    int page = 1,
    int pageSize = 20,
    Map<String, dynamic>? filters,
  }) {
    final uri = Uri.parse(baseRoute);
    final queryParams = <String, String>{
      'page': page.toString(),
      'pageSize': pageSize.toString(),
      ...uri.queryParameters,
    };
    
    // 添加过滤条件
    if (filters != null) {
      filters.forEach((key, value) {
        if (value != null) {
          queryParams[key] = value.toString();
        }
      });
    }
    
    return uri.replace(queryParameters: queryParams).toString();
  }

  // ==================== 路由验证方法 ====================
  
  /// 验证路由是否需要认证
  static bool requiresAuth(String route) {
    final publicRoutes = [
      login,
      loginWithEmailCode,
      sendVerificationCode,
      getAppConfig,
      checkUpdate,
      getTermsOfService,
      getPrivacyPolicy,
    ];

    return !publicRoutes.contains(route);
  }
  
  /// 获取路由描述（用于日志记录）
  static String getRouteDescription(String route) {
    final routeDescriptions = <String, String>{
      '/api/auth/login/': '邮箱密码登录',
      '/api/auth/login/email-code/': '邮箱验证码登录',
      '/api/auth/refresh/': '刷新令牌',
      '/api/auth/logout/': '用户登出',
      '/api/auth/send-code/': '发送邮箱验证码',
      '/api/auth/verify/': '验证Token有效性',
    };

    return routeDescriptions[route] ?? '未知接口';
  }
}
