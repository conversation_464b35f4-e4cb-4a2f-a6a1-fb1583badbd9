import 'package:dio/dio.dart';
import 'dio_client.dart';
import 'api_routes.dart';
import '../models/user_models.dart';
import '../utils/sp_util.dart';

/// API服务类，封装具体的网络请求方法
class ApiService {
  static final ApiService _instance = ApiService._internal();
  static ApiService get instance => _instance;

  late final Dio _dio;

  ApiService._internal() {
    _dio = DioClient.instance.dio;
  }

  // ==================== 认证相关接口 ====================

  /// 邮箱密码登录
  Future<ApiResponse<AuthLoginResponse>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _dio.post(
        ApiRoutes.login,
        data: {
          'email': email,
          'password': password,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => AuthLoginResponse.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      return ApiResponse.error('登录失败: ${_getErrorMessage(e)}');
    }
  }

  /// 邮箱验证码登录
  Future<ApiResponse<AuthLoginResponse>> loginWithEmailCode({
    required String email,
    required String code,
  }) async {
    try {
      final response = await _dio.post(
        ApiRoutes.loginWithEmailCode,
        data: {
          'email': email,
          'code': code,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => AuthLoginResponse.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      return ApiResponse.error('验证码登录失败: ${_getErrorMessage(e)}');
    }
  }

  /// 发送邮箱验证码
  Future<ApiResponse<bool>> sendVerificationCode({
    required String email,
    String codeType = 'login',
  }) async {
    try {
      final response = await _dio.post(
        ApiRoutes.sendVerificationCode,
        data: {
          'email': email,
          'code_type': codeType,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => true, // API返回成功消息，我们返回true
      );
    } catch (e) {
      return ApiResponse.error('发送验证码失败: ${_getErrorMessage(e)}');
    }
  }



  /// 用户登出
  Future<ApiResponse<bool>> logout() async {
    try {
      final response = await _dio.post(
        ApiRoutes.logout,
        data: {
          'refresh': await SpUtil.getAuthToken().then((token) => token?.refreshToken ?? ''),
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => true, // API返回成功消息，我们返回true
      );
    } catch (e) {
      return ApiResponse.error('登出失败: ${_getErrorMessage(e)}');
    }
  }

  /// 刷新token
  Future<ApiResponse<AuthTokenModel>> refreshToken(String refreshToken) async {
    try {
      final response = await _dio.post(
        ApiRoutes.refreshToken,
        data: {
          'refresh': refreshToken,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => AuthTokenModel(
          accessToken: data['access'] as String?,
          refreshToken: refreshToken, // 保持原有的refresh token
          tokenType: 'Bearer',
          expiresIn: DateTime.now().millisecondsSinceEpoch ~/ 1000 + 3600,
          refreshExpiresIn: DateTime.now().millisecondsSinceEpoch ~/ 1000 + 7200,
        ),
      );
    } catch (e) {
      return ApiResponse.error('刷新token失败: ${_getErrorMessage(e)}');
    }
  }

  /// 验证Token有效性
  Future<ApiResponse<bool>> verifyToken() async {
    try {
      final response = await _dio.get(ApiRoutes.verifyToken);

      return ApiResponse.fromJson(
        response.data,
        (data) => data['valid'] as bool? ?? false,
      );
    } catch (e) {
      return ApiResponse.error('验证Token失败: ${_getErrorMessage(e)}');
    }
  }

  // ==================== 私有方法 ====================

  /// 获取错误信息
  String _getErrorMessage(dynamic error) {
    if (error is DioException) {
      return error.message ?? '网络请求异常';
    }
    return error.toString();
  }
}

/// API响应封装类
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? code;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.code,
  });

  /// 成功响应
  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
    );
  }

  /// 错误响应
  factory ApiResponse.error(String message, {int? code}) {
    return ApiResponse(
      success: false,
      message: message,
      code: code,
    );
  }

  /// 从JSON创建响应
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    try {
      final success = json['success'] as bool? ?? false;
      final message = json['message'] as String?;
      final code = json['code'] as int?;

      if (success && json['data'] != null) {
        final data = fromJsonT(json['data']);
        return ApiResponse.success(data, message: message);
      } else {
        return ApiResponse.error(
          message ?? '请求失败',
          code: code,
        );
      }
    } catch (e) {
      return ApiResponse.error('数据解析失败: $e');
    }
  }
}
