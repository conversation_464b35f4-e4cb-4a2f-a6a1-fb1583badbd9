import 'package:dio/dio.dart';
import 'dio_client.dart';
import 'api_routes.dart';
import '../utils/toast_util.dart';
import '../models/user_models.dart';

/// API服务类，封装具体的网络请求方法
class ApiService {
  static final ApiService _instance = ApiService._internal();
  static ApiService get instance => _instance;

  late final Dio _dio;

  ApiService._internal() {
    _dio = DioClient.instance.dio;
  }

  // ==================== 认证相关接口 ====================

  /// 用户登录
  Future<ApiResponse<UserModel>> login({
    required String username,
    required String password,
  }) async {
    try {
      final response = await _dio.post(
        ApiRoutes.login,
        data: {
          'username': username,
          'password': password,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => UserModel.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      return ApiResponse.error('登录失败: ${_getErrorMessage(e)}');
    }
  }

  /// 验证码登录
  Future<ApiResponse<UserModel>> loginWithCode({
    required String phone,
    required String code,
  }) async {
    try {
      final response = await _dio.post(
        ApiRoutes.loginWithCode,
        data: {
          'phone': phone,
          'code': code,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => UserModel.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      return ApiResponse.error('验证码登录失败: ${_getErrorMessage(e)}');
    }
  }

  /// 发送验证码
  Future<ApiResponse<bool>> sendVerificationCode({
    required String phone,
    String type = 'login',
  }) async {
    try {
      final response = await _dio.post(
        ApiRoutes.sendVerificationCode,
        data: {
          'phone': phone,
          'type': type,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => data as bool,
      );
    } catch (e) {
      return ApiResponse.error('发送验证码失败: ${_getErrorMessage(e)}');
    }
  }

  /// 用户注册
  Future<ApiResponse<UserModel>> register({
    required String username,
    required String password,
    required String phone,
    required String code,
    String? email,
  }) async {
    try {
      final response = await _dio.post(
        ApiRoutes.register,
        data: {
          'username': username,
          'password': password,
          'phone': phone,
          'code': code,
          if (email != null) 'email': email,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => UserModel.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      return ApiResponse.error('注册失败: ${_getErrorMessage(e)}');
    }
  }

  /// 用户登出
  Future<ApiResponse<bool>> logout() async {
    try {
      final response = await _dio.post(ApiRoutes.logout);

      return ApiResponse.fromJson(
        response.data,
        (data) => data as bool,
      );
    } catch (e) {
      return ApiResponse.error('登出失败: ${_getErrorMessage(e)}');
    }
  }

  /// 刷新token
  Future<ApiResponse<AuthTokenModel>> refreshToken(String refreshToken) async {
    try {
      final response = await _dio.post(
        ApiRoutes.refreshToken,
        data: {
          'refreshToken': refreshToken,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => AuthTokenModel.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      return ApiResponse.error('刷新token失败: ${_getErrorMessage(e)}');
    }
  }

  // ==================== 用户相关接口 ====================

  /// 获取用户信息
  Future<ApiResponse<UserModel>> getUserInfo() async {
    try {
      final response = await _dio.get(ApiRoutes.getUserInfo);

      return ApiResponse.fromJson(
        response.data,
        (data) => UserModel.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      return ApiResponse.error('获取用户信息失败: ${_getErrorMessage(e)}');
    }
  }

  /// 更新用户信息
  Future<ApiResponse<UserModel>> updateUserInfo({
    String? nickname,
    String? email,
    String? avatar,
    Map<String, dynamic>? extraData,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (nickname != null) data['nickname'] = nickname;
      if (email != null) data['email'] = email;
      if (avatar != null) data['avatar'] = avatar;
      if (extraData != null) data.addAll(extraData);

      final response = await _dio.put(
        ApiRoutes.updateUserInfo,
        data: data,
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => UserModel.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      return ApiResponse.error('更新用户信息失败: ${_getErrorMessage(e)}');
    }
  }

  /// 修改密码
  Future<ApiResponse<bool>> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _dio.post(
        ApiRoutes.changePassword,
        data: {
          'oldPassword': oldPassword,
          'newPassword': newPassword,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => data as bool,
      );
    } catch (e) {
      return ApiResponse.error('修改密码失败: ${_getErrorMessage(e)}');
    }
  }

  // ==================== 文件上传接口 ====================

  /// 上传单个文件
  Future<ApiResponse<String>> uploadFile({
    required String filePath,
    String? fileName,
    ProgressCallback? onProgress,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          filePath,
          filename: fileName,
        ),
      });

      final response = await _dio.post(
        ApiRoutes.uploadFile,
        data: formData,
        onSendProgress: onProgress,
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => data['url'] as String,
      );
    } catch (e) {
      return ApiResponse.error('文件上传失败: ${_getErrorMessage(e)}');
    }
  }

  /// 上传图片
  Future<ApiResponse<String>> uploadImage({
    required String imagePath,
    String? fileName,
    ProgressCallback? onProgress,
  }) async {
    try {
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          imagePath,
          filename: fileName,
        ),
      });

      final response = await _dio.post(
        ApiRoutes.uploadImage,
        data: formData,
        onSendProgress: onProgress,
      );

      return ApiResponse.fromJson(
        response.data,
        (data) => data['url'] as String,
      );
    } catch (e) {
      return ApiResponse.error('图片上传失败: ${_getErrorMessage(e)}');
    }
  }

  // ==================== 通用请求方法 ====================

  /// GET请求
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
      );

      return ApiResponse.fromJson(
        response.data,
        fromJson ?? (data) => data as T,
      );
    } catch (e) {
      return ApiResponse.error('请求失败: ${_getErrorMessage(e)}');
    }
  }

  /// POST请求
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );

      return ApiResponse.fromJson(
        response.data,
        fromJson ?? (data) => data as T,
      );
    } catch (e) {
      return ApiResponse.error('请求失败: ${_getErrorMessage(e)}');
    }
  }

  /// PUT请求
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
      );

      return ApiResponse.fromJson(
        response.data,
        fromJson ?? (data) => data as T,
      );
    } catch (e) {
      return ApiResponse.error('请求失败: ${_getErrorMessage(e)}');
    }
  }

  /// DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
      );

      return ApiResponse.fromJson(
        response.data,
        fromJson ?? (data) => data as T,
      );
    } catch (e) {
      return ApiResponse.error('请求失败: ${_getErrorMessage(e)}');
    }
  }

  // ==================== 私有方法 ====================

  /// 获取错误信息
  String _getErrorMessage(dynamic error) {
    if (error is DioException) {
      return error.message ?? '网络请求异常';
    }
    return error.toString();
  }
}

/// API响应封装类
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? code;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.code,
  });

  /// 成功响应
  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
    );
  }

  /// 错误响应
  factory ApiResponse.error(String message, {int? code}) {
    return ApiResponse(
      success: false,
      message: message,
      code: code,
    );
  }

  /// 从JSON创建响应
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    try {
      final success = json['success'] as bool? ?? false;
      final message = json['message'] as String?;
      final code = json['code'] as int?;

      if (success && json['data'] != null) {
        final data = fromJsonT(json['data']);
        return ApiResponse.success(data, message: message);
      } else {
        return ApiResponse.error(
          message ?? '请求失败',
          code: code,
        );
      }
    } catch (e) {
      return ApiResponse.error('数据解析失败: $e');
    }
  }
}
