# SpUtil 工具类使用指南

SpUtil 是项目中的本地数据持久化存储工具类，基于 SharedPreferences 封装，提供了完整的数据存储和管理功能。

## 功能特性

- 🔐 **用户信息管理** - 完整的用户数据存储和获取
- 🎫 **认证令牌管理** - 访问令牌和刷新令牌的安全存储
- ⚙️ **用户偏好设置** - 应用配置和个人偏好存储
- 📱 **应用设置管理** - 首次启动、语言、主题等设置
- 🔄 **登录状态管理** - 自动登录、记住密码等功能
- 📊 **数据管理工具** - 导入导出、清理等实用功能

## 初始化

在使用 SpUtil 之前，需要先初始化：

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SpUtil.init(); // 或者在 AuthService.initialize() 中调用
  runApp(MyApp());
}
```

## 基础存储功能

### 字符串存储
```dart
// 存储字符串
await SpUtil.setString('user_name', '张三');

// 获取字符串
final userName = await SpUtil.getString('user_name');
print('用户名: $userName');

// 获取字符串（带默认值）
final nickName = await SpUtil.getString('nick_name', '默认昵称');
```

### 布尔值存储
```dart
// 存储布尔值
await SpUtil.setBool('is_first_launch', false);

// 获取布尔值
final isFirstLaunch = await SpUtil.getBool('is_first_launch', true);
print('是否首次启动: $isFirstLaunch');
```

### 数值存储
```dart
// 存储整数
await SpUtil.setInt('user_age', 25);
final age = await SpUtil.getInt('user_age');

// 存储双精度浮点数
await SpUtil.setDouble('user_height', 175.5);
final height = await SpUtil.getDouble('user_height');
```

### JSON 对象存储
```dart
final userData = {
  'name': '张三',
  'age': 25,
  'active': true,
};

// 存储 JSON 对象
await SpUtil.setJson('user_data', userData);

// 获取 JSON 对象
final savedData = await SpUtil.getJson('user_data');
if (savedData != null) {
  print('姓名: ${savedData['name']}');
  print('年龄: ${savedData['age']}');
}
```

## 用户信息管理

### 保存用户信息
```dart
final user = UserModel(
  id: 'user_123',
  username: 'zhangsan',
  nickname: '张三',
  email: '<EMAIL>',
  phone: '13800138000',
  dueDate: '2024-12-25T00:00:00.000Z', // 预产期
  gender: 1, // 1: 男, 2: 女, 0: 未知
  birthday: '1990-01-01',
  bio: '这是一个示例用户',
  createdAt: DateTime.now().toIso8601String(),
);

// 保存用户信息
await SpUtil.saveUserInfo(user);
print('用户信息已保存');
```

### 获取用户信息
```dart
// 获取用户信息
final savedUser = await SpUtil.getUserInfo();
if (savedUser != null) {
  print('用户ID: ${savedUser.id}');
  print('用户名: ${savedUser.username}');
  print('昵称: ${savedUser.nickname}');
  print('显示名称: ${savedUser.displayName}');
  print('性别: ${savedUser.genderText}');
  print('预产期状态: ${savedUser.dueDateStatusText}');
}
```

### 更新用户信息
```dart
// 更新用户信息
final updatedUser = user.copyWith(
  nickname: '张三丰',
  bio: '更新后的用户简介',
  dueDate: '2024-12-31T00:00:00.000Z',
);
await SpUtil.updateUserInfo(updatedUser);
print('用户信息已更新');
```

## 认证令牌管理

### 保存认证令牌
```dart
final token = AuthTokenModel(
  accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  refreshToken: 'refresh_token_example',
  tokenType: 'Bearer',
  expiresIn: DateTime.now().millisecondsSinceEpoch ~/ 1000 + 3600, // 1小时后过期
  refreshExpiresIn: DateTime.now().millisecondsSinceEpoch ~/ 1000 + 7200, // 2小时后过期
);

// 保存认证令牌
await SpUtil.saveAuthToken(token);
print('认证令牌已保存');
```

### 获取和验证令牌
```dart
// 获取认证令牌
final savedToken = await SpUtil.getAuthToken();
if (savedToken != null) {
  print('访问令牌: ${savedToken.accessToken}');
  print('令牌类型: ${savedToken.tokenType}');
  print('是否有效: ${savedToken.isValid}');
}

// 检查令牌是否有效
final isValid = await SpUtil.isTokenValid();
print('令牌有效性: $isValid');
```

## 用户偏好设置

### 保存偏好设置
```dart
final preferences = UserPreferencesModel(
  enableNotifications: true,
  enableSound: false,
  enableVibration: true,
  themeMode: 1, // 0: 跟随系统, 1: 浅色, 2: 深色
  language: 'zh_CN',
  fontSize: 1, // 0: 小, 1: 中, 2: 大
  enableBiometric: false,
  autoLockTime: 5, // 5分钟
);

// 保存用户偏好设置
await SpUtil.saveUserPreferences(preferences);
print('用户偏好设置已保存');
```

### 获取偏好设置
```dart
// 获取用户偏好设置（如果不存在则返回默认设置）
final savedPreferences = await SpUtil.getUserPreferences();
print('推送通知: ${savedPreferences.enableNotifications}');
print('声音: ${savedPreferences.enableSound}');
print('震动: ${savedPreferences.enableVibration}');
print('主题模式: ${savedPreferences.themeMode}');
print('语言: ${savedPreferences.language}');
print('字体大小: ${savedPreferences.fontSize}');
```

## 登录状态管理

### 基础登录状态
```dart
// 设置登录状态
await SpUtil.setLoginStatus(true);
await SpUtil.setAutoLogin(true);
await SpUtil.setRememberPassword(true);
await SpUtil.setLastUsername('zhangsan');
await SpUtil.setLastLoginTime(DateTime.now());

// 获取登录状态
final isLoggedIn = await SpUtil.getLoginStatus();
final autoLogin = await SpUtil.getAutoLogin();
final rememberPassword = await SpUtil.getRememberPassword();
final lastUsername = await SpUtil.getLastUsername();
final lastLoginTime = await SpUtil.getLastLoginTime();

print('登录状态: $isLoggedIn');
print('自动登录: $autoLogin');
print('记住密码: $rememberPassword');
print('最后登录用户名: $lastUsername');
print('最后登录时间: $lastLoginTime');
```

### 完整登录流程
```dart
// 完整登录（保存所有相关信息）
final loginSuccess = await SpUtil.completeLogin(
  user: user,
  token: token,
  autoLogin: true,
  rememberPassword: true,
);

if (loginSuccess) {
  print('登录成功！');
  
  // 验证登录状态
  final isLoggedIn = await SpUtil.getLoginStatus();
  final savedUser = await SpUtil.getUserInfo();
  final savedToken = await SpUtil.getAuthToken();
  
  print('登录状态: $isLoggedIn');
  print('用户信息: ${savedUser?.displayName}');
  print('令牌有效: ${savedToken?.isValid}');
}
```

### 完整登出流程
```dart
// 完整登出（清除所有相关信息）
final logoutSuccess = await SpUtil.completeLogout(clearPreferences: false);

if (logoutSuccess) {
  print('登出成功！');
  
  // 验证登出状态
  final isLoggedIn = await SpUtil.getLoginStatus();
  final user = await SpUtil.getUserInfo();
  final token = await SpUtil.getAuthToken();
  
  print('登录状态: $isLoggedIn');
  print('用户信息: $user');
  print('认证令牌: $token');
}
```

## 应用设置管理

```dart
// 设置应用相关配置
await SpUtil.setFirstLaunch(false);
await SpUtil.setLanguage('zh_CN');
await SpUtil.setThemeMode(1);

// 获取应用配置
final isFirstLaunch = await SpUtil.isFirstLaunch();
final language = await SpUtil.getLanguage();
final themeMode = await SpUtil.getThemeMode();

print('是否首次启动: $isFirstLaunch');
print('语言设置: $language');
print('主题模式: $themeMode');
```

## 数据管理工具

### 数据检查和清理
```dart
// 检查键是否存在
final hasKey = await SpUtil.containsKey('user_name');
print('是否包含user_name键: $hasKey');

// 获取所有键
final allKeys = await SpUtil.getKeys();
print('所有存储的键: $allKeys');

// 获取存储大小（估算）
final storageSize = await SpUtil.getStorageSize();
print('存储大小: $storageSize 字符');

// 删除特定键
await SpUtil.remove('user_name');
print('已删除user_name键');

// 清除所有数据
await SpUtil.clear();
print('已清除所有数据');
```

### 数据导入导出
```dart
// 导出所有数据
final allData = await SpUtil.exportAllData();
print('所有数据: $allData');

// 导入数据
final importData = {
  'user_name': '李四',
  'user_age': 30,
  'is_vip': true,
};
final importSuccess = await SpUtil.importData(importData);
print('数据导入结果: $importSuccess');
```

## 最佳实践

### 1. 错误处理
```dart
try {
  final user = await SpUtil.getUserInfo();
  if (user != null) {
    // 处理用户信息
  }
} catch (e) {
  print('获取用户信息失败: $e');
  // 处理错误情况
}
```

### 2. 空值检查
```dart
// 总是检查返回值是否为空
final username = await SpUtil.getString('username');
if (username != null && username.isNotEmpty) {
  // 使用用户名
}
```

### 3. 默认值使用
```dart
// 为可能不存在的数据提供默认值
final theme = await SpUtil.getInt('theme_mode', 0); // 默认跟随系统
final language = await SpUtil.getString('language', 'zh_CN'); // 默认中文
```

### 4. 批量操作
```dart
// 批量设置相关数据
Future<void> setupUserSession(UserModel user, AuthTokenModel token) async {
  await SpUtil.completeLogin(
    user: user,
    token: token,
    autoLogin: true,
    rememberPassword: true,
  );
}
```

## 注意事项

1. **初始化**: 使用前必须调用 `SpUtil.init()`
2. **异步操作**: 所有方法都是异步的，需要使用 `await`
3. **错误处理**: 建议使用 try-catch 包装调用
4. **数据类型**: JSON 存储时注意数据类型的一致性
5. **性能考虑**: 避免频繁的大量数据存储操作
6. **安全性**: 敏感信息建议使用加密存储

## 相关文档

- [用户模型文档](../models/user_models.md)
- [认证服务文档](../services/auth_service.md)
- [工具类总览](./README.md)
