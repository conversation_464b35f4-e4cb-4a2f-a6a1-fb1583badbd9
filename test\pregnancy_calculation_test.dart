import 'package:flutter_test/flutter_test.dart';
import 'package:align/models/user_models.dart';

void main() {
  group('Pregnancy Calculation Tests', () {
    test('Pregnancy weeks calculation should work correctly', () {
      // 测试场景：预产期是今天之后100天
      final now = DateTime.now();
      final dueDate = now.add(const Duration(days: 100));
      
      final user = UserModel(
        id: 'test_user',
        username: 'testuser',
        dueDate: dueDate.toIso8601String(),
      );

      // 验证孕周计算
      final weeks = user.pregnancyWeeks;
      expect(weeks, isNotNull);
      
      // 280天 - 100天 = 180天 = 25周5天
      final expectedWeeks = 180 ~/ 7; // 25周
      final expectedDays = 180 % 7;   // 5天
      
      expect(weeks!['weeks'], expectedWeeks);
      expect(weeks['days'], expectedDays);
      
      // 验证文本显示
      expect(user.pregnancyWeeksText, '第${expectedWeeks}周 第${expectedDays}天');
      
      print('✅ 孕周计算测试通过');
      print('   预产期: ${dueDate.toString().substring(0, 10)}');
      print('   当前孕周: ${user.pregnancyWeeksText}');
      print('   倒计时: ${user.countdownText}');
    });

    test('Due date calculation from LMP should work correctly', () {
      // 测试末次月经第一天计算预产期
      final lmpDate = DateTime(2024, 1, 1); // 2024年1月1日
      final expectedDueDate = lmpDate.add(const Duration(days: 280)); // 280天后
      
      // 验证计算结果
      expect(expectedDueDate.year, 2024);
      expect(expectedDueDate.month, 10); // 10月
      expect(expectedDueDate.day, 7);    // 7日
      
      print('✅ 末次月经计算预产期测试通过');
      print('   末次月经第一天: ${lmpDate.toString().substring(0, 10)}');
      print('   计算的预产期: ${expectedDueDate.toString().substring(0, 10)}');
    });

    test('Countdown calculation should work correctly', () {
      // 测试倒计时计算
      final now = DateTime.now();
      final dueDate = now.add(const Duration(days: 70)); // 70天后到预产期
      
      final user = UserModel(
        id: 'test_user',
        username: 'testuser',
        dueDate: dueDate.toIso8601String(),
      );

      // 280天 - 70天 = 210天 = 30周
      // 倒计时 = 40周 - 30周 = 10周
      final weeks = user.pregnancyWeeks;
      expect(weeks, isNotNull);
      
      final currentWeeks = weeks!['weeks']!;
      final expectedCountdownWeeks = 40 - currentWeeks;
      
      expect(user.countdownText, contains('还有'));
      expect(user.countdownText, contains('周'));
      
      print('✅ 倒计时计算测试通过');
      print('   当前孕周: ${user.pregnancyWeeksText}');
      print('   倒计时: ${user.countdownText}');
    });

    test('Edge cases should be handled correctly', () {
      final now = DateTime.now();
      
      // 测试1: 预产期是今天
      final dueTodayUser = UserModel(
        id: 'test_user',
        username: 'testuser',
        dueDate: now.toIso8601String(),
      );
      
      expect(dueTodayUser.daysToDueDate, 0);
      expect(dueTodayUser.dueDateStatusText, '今天是预产期');
      
      // 测试2: 已过预产期
      final overDueDate = DateTime(now.year, now.month, now.day).subtract(const Duration(days: 5));
      final overDueUser = UserModel(
        id: 'test_user',
        username: 'testuser',
        dueDate: overDueDate.toIso8601String(),
      );

      expect(overDueUser.daysToDueDate, -5);
      expect(overDueUser.dueDateStatusText, '已过预产期5天');
      
      // 测试3: 未设置预产期
      final noDueDateUser = UserModel(
        id: 'test_user',
        username: 'testuser',
      );
      
      expect(noDueDateUser.pregnancyWeeks, isNull);
      expect(noDueDateUser.pregnancyWeeksText, '未设置预产期');
      expect(noDueDateUser.countdownText, '未设置预产期');
      
      print('✅ 边界情况测试通过');
    });

    test('Pregnancy weeks text formatting should be correct', () {
      final now = DateTime.now();
      
      // 测试不同孕周的文本格式
      final testCases = [
        {'days': 280, 'expected': '第0周 第0天'}, // 刚怀孕
        {'days': 210, 'expected': '第10周 第0天'}, // 10周整
        {'days': 203, 'expected': '第11周 第0天'}, // 11周整
        {'days': 200, 'expected': '第11周 第3天'}, // 11周3天
        {'days': 70, 'expected': '第30周 第0天'},  // 30周整
        {'days': 0, 'expected': '第40周 第0天'},   // 预产期当天
      ];
      
      for (final testCase in testCases) {
        final dueDate = now.add(Duration(days: testCase['days'] as int));
        final user = UserModel(
          id: 'test_user',
          username: 'testuser',
          dueDate: dueDate.toIso8601String(),
        );
        
        print('距离预产期${testCase['days']}天: ${user.pregnancyWeeksText}');
      }
      
      print('✅ 孕周文本格式测试通过');
    });
  });
}
