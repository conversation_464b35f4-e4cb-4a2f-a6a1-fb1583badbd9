import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_quill/flutter_quill.dart';
import '../utils/text_util.dart';
import '../utils/sp_util.dart';
import '../models/models.dart';
import 'note_editor_page.dart';

// 笔记数据模型
class NoteItem {
  final String id;
  final String title;
  final String content; // JSON格式的富文本内容
  final DateTime createdAt;
  final DateTime updatedAt;

  NoteItem({
    required this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
  });

  NoteItem copyWith({
    String? id,
    String? title,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NoteItem(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // 获取纯文本预览
  String get plainTextPreview {
    if (content.isEmpty) return '';
    try {
      // 尝试解析JSON格式的内容
      final jsonData = content.startsWith('[') ? content : '[]';
      final List<dynamic> deltaList = jsonDecode(jsonData);
      final document = Document.fromJson(deltaList);
      final plainText = document.toPlainText().trim();
      return plainText.length > 50 ? '${plainText.substring(0, 50)}...' : plainText;
    } catch (e) {
      // 如果解析失败，直接返回文本内容
      return content.length > 50 ? '${content.substring(0, 50)}...' : content;
    }
  }
}

class NotePage extends StatefulWidget {
  final Color cardColor;
  final Color cardBgColor;

  const NotePage({
    super.key,
    required this.cardColor,
    required this.cardBgColor,
  });

  @override
  State<NotePage> createState() => _NotePageState();
}

class _NotePageState extends State<NotePage> {
  // 笔记数据列表
  List<NoteItem> _notes = [];

  // 日期相关状态
  DateTime _selectedDate = DateTime.now();
  UserModel? _userInfo;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
    _initializeNotes();
  }

  // 加载用户信息
  Future<void> _loadUserInfo() async {
    final userInfo = await SpUtil.getUserInfo();
    if (mounted) {
      setState(() {
        _userInfo = userInfo;
      });
    }
  }

  // 切换到上一天
  void _goToPreviousDay() {
    setState(() {
      _selectedDate = _selectedDate.subtract(const Duration(days: 1));
    });
  }

  // 切换到下一天
  void _goToNextDay() {
    setState(() {
      _selectedDate = _selectedDate.add(const Duration(days: 1));
    });
  }

  // 格式化日期显示
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return '今天';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return '昨天';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return '明天';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  // 格式化完整日期
  String _formatFullDate(DateTime date) {
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[date.weekday - 1];
    return '${date.year}年${date.month}月${date.day}日（$weekday）';
  }

  // 计算孕周信息
  String _getPregnancyWeekInfo() {
    if (_userInfo?.pregnancyWeeks == null) {
      return '15周 3天'; // 默认显示
    }

    final weeks = _userInfo!.pregnancyWeeks!;
    return '${weeks['weeks']}周 ${weeks['days']}天';
  }

  void _initializeNotes() {
    // 初始化示例数据
    _notes = [
      NoteItem(
        id: '1',
        title: '小小想法😌',
        content: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      NoteItem(
        id: '2',
        title: '今日总结😌',
        content: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 顶部拖拽指示器
        Container(
          padding: EdgeInsets.only(top: 16.h),
          child: Center(
            child: Container(
              width: 36.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: widget.cardColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
        ),

        // Header区域 - 参考symptom_page的设计
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 左箭头
              GestureDetector(
                onTap: _goToPreviousDay,
                child: Icon(
                  CupertinoIcons.chevron_left,
                  size: 20.sp,
                  color: widget.cardColor,
                ),
              ),

              // 中间标题内容
              Expanded(
                child: Column(
                  children: [
                    Text(
                      _formatDate(_selectedDate),
                      style: TextUtil.base
                          .sp(24)
                          .semiBold
                          .withColor(widget.cardColor),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      _formatFullDate(_selectedDate),
                      style: TextUtil.base.sp(14).withColor(widget.cardColor),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      _getPregnancyWeekInfo(),
                      style: TextUtil.base
                          .sp(12)
                          .withColor(CupertinoColors.systemGrey),
                    ),
                  ],
                ),
              ),

              // 右箭头
              GestureDetector(
                onTap: _goToNextDay,
                child: Icon(
                  CupertinoIcons.chevron_right,
                  size: 20.sp,
                  color: widget.cardColor,
                ),
              ),
            ],
          ),
        ),

        // 主要内容区域
        Expanded(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 24.w),
            padding: EdgeInsets.symmetric(vertical: 20.h),
            child: Column(
              children: [
                // 笔记条目列表
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: _notes.asMap().entries.map((entry) {
                        final index = entry.key;
                        final note = entry.value;
                        return Column(
                          children: [
                            if (index > 0) SizedBox(height: 16.h),
                            _buildNoteEntry(note),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                ),

                SizedBox(height: 20.h),

                // 底部添加按钮
                _buildAddButton(),

                SizedBox(height: 20.h),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNoteEntry(NoteItem note) {
    return GestureDetector(
      onTap: () => _editNote(note),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: CupertinoColors.white,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 左侧标题
                Text(
                  note.title,
                  style: TextUtil.base.sp(16).medium.black,
                ),

                const Spacer(),

                // 右侧眼睛图标
                Icon(
                  CupertinoIcons.eye,
                  size: 20.sp,
                  color: CupertinoColors.systemGrey,
                ),
              ],
            ),

            // 如果有内容，显示预览
            if (note.plainTextPreview.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Text(
                note.plainTextPreview,
                style: TextUtil.base.sp(12).grey,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAddButton() {
    return GestureDetector(
      onTap: _addNewNote,
      child: Container(
        width: double.infinity,
        height: 48.h,
        decoration: BoxDecoration(
          color: widget.cardColor,
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Center(
          child: Text(
            '添加',
            style: TextUtil.base.sp(16).semiBold.white,
          ),
        ),
      ),
    );
  }

  void _editNote(NoteItem note) {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => NoteEditorPage(
          cardColor: widget.cardColor,
          cardBgColor: widget.cardBgColor,
          title: note.title,
          initialContent: note.content,
          onSave: (title, content) => _saveNote(note.id, title, content),
        ),
      ),
    );
  }

  void _addNewNote() {
    // 创建新笔记
    final newNote = NoteItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: '新笔记',
      content: '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => NoteEditorPage(
          cardColor: widget.cardColor,
          cardBgColor: widget.cardBgColor,
          title: newNote.title,
          initialContent: newNote.content,
          onSave: (title, content) {
            // 添加新笔记到列表
            setState(() {
              _notes.add(newNote.copyWith(
                title: title,
                content: content,
                updatedAt: DateTime.now(),
              ));
            });
          },
        ),
      ),
    );
  }

  void _saveNote(String noteId, String title, String content) {
    setState(() {
      final index = _notes.indexWhere((note) => note.id == noteId);
      if (index != -1) {
        _notes[index] = _notes[index].copyWith(
          title: title,
          content: content,
          updatedAt: DateTime.now(),
        );
      }
    });
  }
}
