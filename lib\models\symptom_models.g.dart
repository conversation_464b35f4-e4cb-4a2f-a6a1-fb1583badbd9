// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'symptom_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SymptomData _$SymptomDataFromJson(Map<String, dynamic> json) => SymptomData(
  symptom: (json['symptom'] as List<dynamic>)
      .map((e) => SymptomCategory.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$SymptomDataToJson(SymptomData instance) =>
    <String, dynamic>{'symptom': instance.symptom};

SymptomCategory _$SymptomCategoryFromJson(Map<String, dynamic> json) =>
    SymptomCategory(
      title: json['title'] as String,
      type: (json['type'] as num).toInt(),
      content: (json['content'] as List<dynamic>)
          .map((e) => SymptomContent.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SymptomCategoryToJson(SymptomCategory instance) =>
    <String, dynamic>{
      'title': instance.title,
      'type': instance.type,
      'content': instance.content,
    };

SymptomContent _$SymptomContentFromJson(Map<String, dynamic> json) =>
    SymptomContent(
      description: json['description'] as String?,
      name: json['name'] as String?,
      weight: json['weight'] as String?,
      unit: json['unit'] as String?,
      time: json['time'] as String?,
      medicineName: json['medicine_name'] as String?,
      quantity: json['quantity'] as String?,
      cycle: (json['cycle'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$SymptomContentToJson(SymptomContent instance) =>
    <String, dynamic>{
      'description': instance.description,
      'name': instance.name,
      'weight': instance.weight,
      'unit': instance.unit,
      'time': instance.time,
      'medicine_name': instance.medicineName,
      'quantity': instance.quantity,
      'cycle': instance.cycle,
    };
