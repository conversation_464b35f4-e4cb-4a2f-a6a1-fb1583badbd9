import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:align/utils/sp_util.dart';
import 'package:align/models/user_models.dart';

void main() {
  group('SpUtil Tests', () {
    setUp(() async {
      // 在每个测试前清除SharedPreferences
      SharedPreferences.setMockInitialValues({});
      await SpUtil.init();
    });

    test('基础存储功能测试', () async {
      // 测试字符串存储
      await SpUtil.setString('test_string', 'hello world');
      final stringValue = await SpUtil.getString('test_string');
      expect(stringValue, 'hello world');

      // 测试布尔值存储
      await SpUtil.setBool('test_bool', true);
      final boolValue = await SpUtil.getBool('test_bool');
      expect(boolValue, true);

      // 测试整数存储
      await SpUtil.setInt('test_int', 42);
      final intValue = await SpUtil.getInt('test_int');
      expect(intValue, 42);

      // 测试双精度浮点数存储
      await SpUtil.setDouble('test_double', 3.14);
      final doubleValue = await SpUtil.getDouble('test_double');
      expect(doubleValue, 3.14);
    });

    test('JSON存储功能测试', () async {
      final testData = {
        'name': '测试用户',
        'age': 25,
        'active': true,
      };

      await SpUtil.setJson('test_json', testData);
      final retrievedData = await SpUtil.getJson('test_json');
      
      expect(retrievedData, isNotNull);
      expect(retrievedData!['name'], '测试用户');
      expect(retrievedData['age'], 25);
      expect(retrievedData['active'], true);
    });

    test('登录状态管理测试', () async {
      // 初始状态应该是未登录
      final initialStatus = await SpUtil.getLoginStatus();
      expect(initialStatus, false);

      // 设置登录状态
      await SpUtil.setLoginStatus(true);
      final loginStatus = await SpUtil.getLoginStatus();
      expect(loginStatus, true);

      // 设置自动登录
      await SpUtil.setAutoLogin(true);
      final autoLogin = await SpUtil.getAutoLogin();
      expect(autoLogin, true);

      // 设置记住密码
      await SpUtil.setRememberPassword(true);
      final rememberPassword = await SpUtil.getRememberPassword();
      expect(rememberPassword, true);

      // 设置最后登录用户名
      await SpUtil.setLastUsername('test_user');
      final lastUsername = await SpUtil.getLastUsername();
      expect(lastUsername, 'test_user');
    });

    test('用户信息管理测试', () async {
      final user = UserModel(
        id: 'test_id',
        username: 'test_user',
        nickname: '测试用户',
        email: '<EMAIL>',
        phone: '13800138000',
        gender: 1,
        bio: '这是一个测试用户',
      );

      // 保存用户信息
      final saveResult = await SpUtil.saveUserInfo(user);
      expect(saveResult, true);

      // 获取用户信息
      final retrievedUser = await SpUtil.getUserInfo();
      expect(retrievedUser, isNotNull);
      expect(retrievedUser!.id, 'test_id');
      expect(retrievedUser.username, 'test_user');
      expect(retrievedUser.nickname, '测试用户');
      expect(retrievedUser.email, '<EMAIL>');
      expect(retrievedUser.phone, '13800138000');
      expect(retrievedUser.gender, 1);
      expect(retrievedUser.bio, '这是一个测试用户');

      // 更新用户信息
      final updatedUser = user.copyWith(nickname: '更新的用户');
      final updateResult = await SpUtil.updateUserInfo(updatedUser);
      expect(updateResult, true);

      final finalUser = await SpUtil.getUserInfo();
      expect(finalUser!.nickname, '更新的用户');
    });

    test('认证令牌管理测试', () async {
      final token = AuthTokenModel(
        accessToken: 'access_token_123',
        refreshToken: 'refresh_token_456',
        tokenType: 'Bearer',
        expiresIn: DateTime.now().millisecondsSinceEpoch ~/ 1000 + 3600, // 1小时后过期
        refreshExpiresIn: DateTime.now().millisecondsSinceEpoch ~/ 1000 + 7200, // 2小时后过期
      );

      // 保存认证令牌
      final saveResult = await SpUtil.saveAuthToken(token);
      expect(saveResult, true);

      // 获取认证令牌
      final retrievedToken = await SpUtil.getAuthToken();
      expect(retrievedToken, isNotNull);
      expect(retrievedToken!.accessToken, 'access_token_123');
      expect(retrievedToken.refreshToken, 'refresh_token_456');
      expect(retrievedToken.tokenType, 'Bearer');

      // 检查令牌是否有效
      final isValid = await SpUtil.isTokenValid();
      expect(isValid, true);
    });

    test('用户偏好设置管理测试', () async {
      final preferences = UserPreferencesModel(
        enableNotifications: true,
        enableSound: false,
        enableVibration: true,
        themeMode: 1,
        language: 'zh_CN',
        fontSize: 2,
        enableBiometric: false,
        autoLockTime: 10,
      );

      // 保存用户偏好设置
      final saveResult = await SpUtil.saveUserPreferences(preferences);
      expect(saveResult, true);

      // 获取用户偏好设置
      final retrievedPreferences = await SpUtil.getUserPreferences();
      expect(retrievedPreferences.enableNotifications, true);
      expect(retrievedPreferences.enableSound, false);
      expect(retrievedPreferences.enableVibration, true);
      expect(retrievedPreferences.themeMode, 1);
      expect(retrievedPreferences.language, 'zh_CN');
      expect(retrievedPreferences.fontSize, 2);
      expect(retrievedPreferences.enableBiometric, false);
      expect(retrievedPreferences.autoLockTime, 10);
    });

    test('完整登录和登出测试', () async {
      final user = UserModel(
        id: 'complete_test_id',
        username: 'complete_test_user',
        nickname: '完整测试用户',
        email: '<EMAIL>',
      );

      final token = AuthTokenModel(
        accessToken: 'complete_access_token',
        refreshToken: 'complete_refresh_token',
        tokenType: 'Bearer',
        expiresIn: DateTime.now().millisecondsSinceEpoch ~/ 1000 + 3600,
      );

      // 完整登录
      final loginResult = await SpUtil.completeLogin(
        user: user,
        token: token,
        autoLogin: true,
        rememberPassword: true,
      );
      expect(loginResult, true);

      // 验证登录状态
      final loginStatus = await SpUtil.getLoginStatus();
      expect(loginStatus, true);

      // 验证用户信息
      final savedUser = await SpUtil.getUserInfo();
      expect(savedUser!.id, 'complete_test_id');

      // 验证认证令牌
      final savedToken = await SpUtil.getAuthToken();
      expect(savedToken!.accessToken, 'complete_access_token');

      // 验证登录设置
      final autoLogin = await SpUtil.getAutoLogin();
      expect(autoLogin, true);

      final rememberPassword = await SpUtil.getRememberPassword();
      expect(rememberPassword, true);

      // 完整登出
      final logoutResult = await SpUtil.completeLogout();
      expect(logoutResult, true);

      // 验证登出后状态
      final logoutStatus = await SpUtil.getLoginStatus();
      expect(logoutStatus, false);

      final clearedUser = await SpUtil.getUserInfo();
      expect(clearedUser, isNull);

      final clearedToken = await SpUtil.getAuthToken();
      expect(clearedToken, isNull);

      final clearedAutoLogin = await SpUtil.getAutoLogin();
      expect(clearedAutoLogin, false);
    });

    test('应用设置管理测试', () async {
      // 测试首次启动标记
      final isFirstLaunch = await SpUtil.isFirstLaunch();
      expect(isFirstLaunch, true); // 默认为首次启动

      await SpUtil.setFirstLaunch(false);
      final isNotFirstLaunch = await SpUtil.isFirstLaunch();
      expect(isNotFirstLaunch, false);

      // 测试语言设置
      await SpUtil.setLanguage('en_US');
      final language = await SpUtil.getLanguage();
      expect(language, 'en_US');

      // 测试主题模式
      await SpUtil.setThemeMode(2);
      final themeMode = await SpUtil.getThemeMode();
      expect(themeMode, 2);
    });

    test('数据清除功能测试', () async {
      // 先保存一些数据
      await SpUtil.setString('test_key', 'test_value');
      await SpUtil.setBool('test_bool', true);
      await SpUtil.setLoginStatus(true);

      // 验证数据存在
      expect(await SpUtil.getString('test_key'), 'test_value');
      expect(await SpUtil.getBool('test_bool'), true);
      expect(await SpUtil.getLoginStatus(), true);

      // 清除所有数据
      await SpUtil.clear();

      // 验证数据已清除
      expect(await SpUtil.getString('test_key'), isNull);
      expect(await SpUtil.getBool('test_bool'), false);
      expect(await SpUtil.getLoginStatus(), false);
    });
  });
}
