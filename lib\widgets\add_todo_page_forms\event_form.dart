import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/text_util.dart';
import '../../utils/toast_util.dart';
import 'event_detail_section.dart';
import 'event_reminder_section.dart';

/// 事项表单组件
class EventForm extends StatefulWidget {
  final Color cardColor;
  final Color cardBgColor;

  const EventForm({
    super.key,
    required this.cardColor,
    required this.cardBgColor,
  });

  @override
  State<EventForm> createState() => _EventFormState();
}

class _EventFormState extends State<EventForm> {
  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  // 表单控制器
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endTimeController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _travelTimeController = TextEditingController();
  final TextEditingController _reminderAdvanceController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // 全天开关状态
  bool _isAllDay = false;

  @override
  void initState() {
    super.initState();
    // 使用 Scaffold 的 resizeToAvoidBottomInset
  }

  @override
  void dispose() {
    // 释放滚动控制器
    _scrollController.dispose();

    // 释放表单控制器
    _startTimeController.dispose();
    _endTimeController.dispose();
    _locationController.dispose();
    _travelTimeController.dispose();
    _reminderAdvanceController.dispose();
    _notesController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: GestureDetector(
        onTap: () {
          // 点击空白处收起键盘
          FocusScope.of(context).unfocus();
        },
        child: _buildScrollView(),
      ),
    );
  }

  Widget _buildScrollView() {
    return Container(
      color: widget.cardBgColor,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: EdgeInsets.only(
          left: 24.w,
          right: 24.w,
          bottom: 100.h, // 底部间距，防止软键盘遮挡
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CupertinoTextField(
              placeholder: '输入标题',
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
              ),
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.black,
              ),
              placeholderStyle: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 24.h),
            EventDetailSection(
              cardColor: widget.cardColor,
              isAllDay: _isAllDay,
              onAllDayChanged: (value) {
                setState(() {
                  _isAllDay = value;
                });
              },
              startTimeController: _startTimeController,
              endTimeController: _endTimeController,
              locationController: _locationController,
            ),
            SizedBox(height: 24.h),

            // 事项前提示
            EventReminderSection(
              cardColor: widget.cardColor,
              travelTimeController: _travelTimeController,
              reminderAdvanceController: _reminderAdvanceController,
              notesController: _notesController,
            ),
            SizedBox(height: 32.h),

            // 保存按钮
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  // 构建保存按钮
  Widget _buildSaveButton() {
    return Container(
      width: double.infinity,
      height: 48.h,
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        color: widget.cardColor,
        borderRadius: BorderRadius.circular(24.r),
        onPressed: _handleSave,
        child: Text(
          '保存',
          style: TextUtil.base
              .sp(16)
              .semiBold
              .withColor(CupertinoColors.white),
        ),
      ),
    );
  }

  // 处理保存逻辑
  void _handleSave() {
    // TODO: 实现保存逻辑
    // 这里可以添加表单验证和数据保存逻辑

    // 显示保存成功提示
    ToastUtil.showSaveSuccess();
    print('事项表单保存');
  }
}
