import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/text_util.dart';

/// 中文日期选择器组件
/// 支持年月日选择，自动处理每月天数，包含防抖机制
class ChineseDatePicker extends StatefulWidget {
  final DateTime initialDate;
  final Function(DateTime) onDateChanged;
  final Color textColor;
  final DateTime? minimumDate;
  final DateTime? maximumDate;

  const ChineseDatePicker({
    super.key,
    required this.initialDate,
    required this.onDateChanged,
    required this.textColor,
    this.minimumDate,
    this.maximumDate,
  });

  @override
  State<ChineseDatePicker> createState() => _ChineseDatePickerState();
}

class _ChineseDatePickerState extends State<ChineseDatePicker> {
  late int selectedYear;
  late int selectedMonth;
  late int selectedDay;
  
  // 防抖定时器
  Timer? _debounceTimer;
  
  // 选择器控制器
  late FixedExtentScrollController _yearController;
  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;
  
  // 缓存当前月份的天数，避免重复计算
  int _cachedDaysInMonth = 31;
  int _cachedYear = 0;
  int _cachedMonth = 0;

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialDate.year;
    selectedMonth = widget.initialDate.month;
    selectedDay = widget.initialDate.day;
    
    // 初始化控制器
    final minYear = widget.minimumDate?.year ?? 1900;
    _yearController = FixedExtentScrollController(
      initialItem: selectedYear - minYear,
    );
    _monthController = FixedExtentScrollController(
      initialItem: selectedMonth - 1,
    );
    _dayController = FixedExtentScrollController(
      initialItem: selectedDay - 1,
    );
    
    // 初始化缓存
    _updateDaysCache();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    super.dispose();
  }

  /// 获取指定年月的天数
  int _getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }

  /// 更新天数缓存
  void _updateDaysCache() {
    if (_cachedYear != selectedYear || _cachedMonth != selectedMonth) {
      _cachedYear = selectedYear;
      _cachedMonth = selectedMonth;
      _cachedDaysInMonth = _getDaysInMonth(selectedYear, selectedMonth);
    }
  }

  /// 智能调整日期，确保在有效范围内
  void _adjustDay() {
    _updateDaysCache();
    if (selectedDay > _cachedDaysInMonth) {
      selectedDay = _cachedDaysInMonth;
      // 更新日期选择器的位置
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_dayController.hasClients) {
          _dayController.animateToItem(
            selectedDay - 1,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  /// 防抖处理日期变化
  void _debouncedDateChange() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 150), () {
      final newDate = DateTime(selectedYear, selectedMonth, selectedDay);
      
      // 检查日期是否在允许范围内
      if (widget.minimumDate != null && newDate.isBefore(widget.minimumDate!)) {
        return;
      }
      if (widget.maximumDate != null && newDate.isAfter(widget.maximumDate!)) {
        return;
      }
      
      widget.onDateChanged(newDate);
    });
  }

  /// 处理年份变化
  void _onYearChanged(int index) {
    final minYear = widget.minimumDate?.year ?? 1900;
    final newYear = minYear + index;
    
    if (newYear != selectedYear) {
      setState(() {
        selectedYear = newYear;
        _adjustDay();
      });
      _debouncedDateChange();
    }
  }

  /// 处理月份变化
  void _onMonthChanged(int index) {
    final newMonth = index + 1;
    
    if (newMonth != selectedMonth) {
      setState(() {
        selectedMonth = newMonth;
        _adjustDay();
      });
      _debouncedDateChange();
    }
  }

  /// 处理日期变化
  void _onDayChanged(int index) {
    final newDay = index + 1;
    
    if (newDay != selectedDay && newDay <= _cachedDaysInMonth) {
      setState(() {
        selectedDay = newDay;
      });
      _debouncedDateChange();
    }
  }

  @override
  Widget build(BuildContext context) {
    final minYear = widget.minimumDate?.year ?? 1900;
    final maxYear = widget.maximumDate?.year ?? 2100;
    final yearRange = maxYear - minYear + 1;

    return Row(
      children: [
        // 年份选择器
        Expanded(
          child: CupertinoPicker(
            itemExtent: 40.h,
            scrollController: _yearController,
            onSelectedItemChanged: _onYearChanged,
            children: List.generate(yearRange, (index) {
              final year = minYear + index;
              return Center(
                child: Text(
                  '${year}年',
                  style: TextUtil.base.sp(16).withColor(widget.textColor),
                ),
              );
            }),
          ),
        ),

        // 月份选择器
        Expanded(
          child: CupertinoPicker(
            itemExtent: 40.h,
            scrollController: _monthController,
            onSelectedItemChanged: _onMonthChanged,
            children: List.generate(12, (index) {
              final month = index + 1;
              return Center(
                child: Text(
                  '${month}月',
                  style: TextUtil.base.sp(16).withColor(widget.textColor),
                ),
              );
            }),
          ),
        ),

        // 日期选择器 - 根据年月动态生成天数
        Expanded(
          child: CupertinoPicker(
            itemExtent: 40.h,
            scrollController: _dayController,
            onSelectedItemChanged: _onDayChanged,
            children: List.generate(_cachedDaysInMonth, (index) {
              final day = index + 1;
              return Center(
                child: Text(
                  '${day}日',
                  style: TextUtil.base.sp(16).withColor(widget.textColor),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }
}

/// 日期选择器弹窗组件
class DatePickerBottomSheet extends StatefulWidget {
  final DateTime initialDate;
  final Function(DateTime) onDateSelected;
  final Color color;
  final Color bgColor;
  final String title;
  final DateTime? minimumDate;
  final DateTime? maximumDate;

  const DatePickerBottomSheet({
    super.key,
    required this.initialDate,
    required this.onDateSelected,
    required this.color,
    required this.bgColor,
    this.title = '选择日期',
    this.minimumDate,
    this.maximumDate,
  });

  @override
  State<DatePickerBottomSheet> createState() => _DatePickerBottomSheetState();
}

class _DatePickerBottomSheetState extends State<DatePickerBottomSheet> {
  late DateTime selectedDate;

  @override
  void initState() {
    super.initState();
    selectedDate = widget.initialDate;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300.h,
      color: widget.bgColor,
      child: Column(
        children: [
          // 顶部操作栏
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: widget.color.withOpacity(0.1),
                  width: 1.w,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    '取消',
                    style: TextUtil.base
                        .sp(16)
                        .withColor(CupertinoColors.systemGrey),
                  ),
                ),
                Text(
                  widget.title,
                  style: TextUtil.base
                      .sp(18)
                      .semiBold
                      .withColor(widget.color),
                ),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    widget.onDateSelected(selectedDate);
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    '确定',
                    style: TextUtil.base
                        .sp(16)
                        .withColor(widget.color),
                  ),
                ),
              ],
            ),
          ),
          
          // 日期选择器
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: ChineseDatePicker(
                initialDate: selectedDate,
                textColor: widget.color,
                minimumDate: widget.minimumDate,
                maximumDate: widget.maximumDate,
                onDateChanged: (date) {
                  selectedDate = date;
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
