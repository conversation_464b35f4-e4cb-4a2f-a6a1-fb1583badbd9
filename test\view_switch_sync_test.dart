import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('SwipeableCalendar View Switch Sync Tests', () {
    testWidgets('Should maintain correct date when switching between views', (WidgetTester tester) async {
      final testDate = DateTime(2024, 1, 15); // 2024年1月15日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.week,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（周视图）
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(currentMonthTitle, contains('2024年1月'));
      expect(currentFormat, equals(CustomCalendarFormat.week));
      
      // 在周视图中点击日期17
      final dayFinder17 = find.text('17');
      if (tester.any(dayFinder17)) {
        await tester.tap(dayFinder17);
        await tester.pumpAndSettle();

        // 验证选中的日期正确
        expect(selectedDay, isNotNull);
        expect(selectedDay!.day, equals(17));
        expect(selectedDay!.month, equals(1));
        expect(selectedDay!.year, equals(2024));
        expect(currentMonthTitle, contains('2024年1月'));
      }

      // 现在测试视图切换后的日期同步
      // 重置选中日期变量
      selectedDay = null;
      
      // 等待一段时间确保状态稳定
      await tester.pump(Duration(milliseconds: 100));
      
      // 在当前状态下点击另一个日期，验证没有跳转到错误的年份
      final dayFinder20 = find.text('20');
      if (tester.any(dayFinder20)) {
        await tester.tap(dayFinder20);
        await tester.pumpAndSettle();

        // 验证选中的日期正确，没有跳到错误的年份
        expect(selectedDay, isNotNull);
        expect(selectedDay!.day, equals(20));
        expect(selectedDay!.month, equals(1));
        expect(selectedDay!.year, equals(2024)); // 关键验证：应该仍然是2024年
        expect(currentMonthTitle, contains('2024年1月'));
      }
    });

    testWidgets('Should maintain date consistency across different view formats', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 15); // 2024年6月15日
      String? currentMonthTitle;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态显示正确的月份
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(currentMonthTitle, contains('2024年6月'));
      
      // 等待确保组件完全初始化
      await tester.pump(Duration(milliseconds: 200));
      
      // 验证月份标题仍然正确
      expect(currentMonthTitle, contains('2024年6月'));
    });

    testWidgets('Should handle year view correctly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 3, 10); // 2024年3月10日
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证年视图正确显示
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年3月'));
      
      // 验证年视图中显示了月份名称
      expect(find.text('三月'), findsOneWidget);
      expect(find.text('四月'), findsOneWidget);
    });
  });
}
