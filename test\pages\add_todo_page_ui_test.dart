import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:align/pages/add_todo_page.dart';

void main() {
  group('AddTodoPage UI Tests', () {
    testWidgets('Should render AddTodoPage UI correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: Scaffold(
                body: AddTodoPage(
                  cardColor: const Color(0xFFFF7D7D),
                  cardBgColor: const Color(0xFFFFC4C4),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('今天'), findsOneWidget);
      expect(find.text('2025年6月28日（周六）'), findsOneWidget);
      expect(find.text('15周 3天'), findsOneWidget);

      // 验证分类选择器
      expect(find.text('产检'), findsOneWidget);
      expect(find.text('事项'), findsOneWidget);
      expect(find.text('任务'), findsOneWidget);

      print('AddTodoPage UI rendered successfully');
    });

    testWidgets('Should switch between categories', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: Scaffold(
                body: AddTodoPage(
                  cardColor: const Color(0xFFFF7D7D),
                  cardBgColor: const Color(0xFFFFC4C4),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 点击"事项"分类
      await tester.tap(find.text('事项'));
      await tester.pumpAndSettle();

      // 验证分类切换成功
      expect(find.text('事项详细'), findsOneWidget);

      // 点击"任务"分类
      await tester.tap(find.text('任务'));
      await tester.pumpAndSettle();

      // 验证分类切换成功
      expect(find.text('任务详细'), findsOneWidget);

      print('Category switching works correctly');
    });

    testWidgets('Should display form fields correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: Scaffold(
                body: AddTodoPage(
                  cardColor: const Color(0xFFFF7D7D),
                  cardBgColor: const Color(0xFFFFC4C4),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 默认显示产检表单，验证产检相关字段
      expect(find.text('产检详细'), findsWidgets);

      // 切换到事项表单
      await tester.tap(find.text('事项'));
      await tester.pumpAndSettle();

      // 验证事项表单字段
      expect(find.text('事项详细'), findsWidgets);
      expect(find.text('全天'), findsWidgets);
      expect(find.text('开始时间'), findsWidgets);
      expect(find.text('结束时间'), findsWidgets);
      expect(find.text('地点'), findsWidgets);
      expect(find.text('事项前提示'), findsWidgets);

      print('Form fields displayed correctly');
    });

    testWidgets('Should handle time picker interaction', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: Scaffold(
                body: AddTodoPage(
                  cardColor: const Color(0xFFFF7D7D),
                  cardBgColor: const Color(0xFFFFC4C4),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 查找时间字段并点击
      final timeField = find.text('00:00').first;
      await tester.tap(timeField);
      await tester.pumpAndSettle();

      // 验证时间选择器出现
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('确定'), findsOneWidget);

      // 点击取消关闭选择器
      await tester.tap(find.text('取消'));
      await tester.pumpAndSettle();

      print('Time picker interaction works correctly');
    });

    testWidgets('Should display save button in all forms', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: Scaffold(
                body: AddTodoPage(
                  cardColor: const Color(0xFFFF7D7D),
                  cardBgColor: const Color(0xFFFFC4C4),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证产检表单的保存按钮
      expect(find.text('保存'), findsOneWidget);

      // 切换到事项表单
      await tester.tap(find.text('事项'));
      await tester.pumpAndSettle();
      expect(find.text('保存'), findsOneWidget);

      // 切换到任务表单
      await tester.tap(find.text('任务'));
      await tester.pumpAndSettle();
      expect(find.text('保存'), findsOneWidget);

      print('Save buttons displayed correctly in all forms');
    });
  });
}
