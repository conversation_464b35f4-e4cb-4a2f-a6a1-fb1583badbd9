import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('Year View Month Click Bounce Fix Tests', () {
    testWidgets('Should not bounce back when clicking different month in year view', (WidgetTester tester) async {
      final testDate = DateTime(2024, 7, 15); // selectedDay在7月15日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthTitleHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: Safe<PERSON><PERSON>(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                      print('Day selected: $day');
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      monthTitleHistory.add(month);
                      print('Month changed to: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                      print('View changed to: $format');
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（年视图，显示selectedDay所在的月份7月）
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年7月'));
      
      // 清空历史记录
      monthTitleHistory.clear();
      
      // 在年视图中点击"二月"（与selectedDay的7月不同）
      final februaryFinder = find.text('二月');
      expect(februaryFinder, findsOneWidget);
      
      await tester.tap(februaryFinder);
      await tester.pumpAndSettle();
      
      // 验证切换到月视图
      expect(currentFormat, equals(CustomCalendarFormat.month));
      
      // 关键验证：应该显示2月，而不是跳回7月
      expect(currentMonthTitle, contains('2024年2月'));
      
      // 验证selectedDay保持不变（没有点击日期，所以selectedDay应该还是初始值）
      expect(selectedDay, isNull);
      
      // 等待较长时间，确保没有自动跳回7月
      await tester.pump(Duration(milliseconds: 1000));
      await tester.pumpAndSettle();
      
      // 再次验证仍然显示2月，没有跳回7月
      expect(currentMonthTitle, contains('2024年2月'));
      expect(currentFormat, equals(CustomCalendarFormat.month));
      
      // 验证月份变化历史，应该只有一次变化到2月
      expect(monthTitleHistory.length, equals(1));
      expect(monthTitleHistory.first, contains('2024年2月'));
      
      print('Successfully stayed in February, did not bounce back to July');
    });

    testWidgets('Should handle extreme month difference without bouncing', (WidgetTester tester) async {
      final testDate = DateTime(2024, 12, 25); // selectedDay在12月
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthTitleHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      monthTitleHistory.add(month);
                      print('Month changed to: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（年视图，显示12月）
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年12月'));
      
      // 清空历史记录
      monthTitleHistory.clear();
      
      // 点击"一月"（与selectedDay的12月相差最远）
      final januaryFinder = find.text('一月');
      if (tester.any(januaryFinder)) {
        await tester.tap(januaryFinder);
        await tester.pumpAndSettle();
        
        // 验证跳转到1月
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(currentMonthTitle, contains('2024年1月'));
        
        // 等待确保稳定在1月，不会跳回12月
        await tester.pump(Duration(milliseconds: 1000));
        await tester.pumpAndSettle();
        
        expect(currentMonthTitle, contains('2024年1月'));
        
        // 验证月份变化历史，应该只有一次变化到1月
        expect(monthTitleHistory.length, equals(1));
        expect(monthTitleHistory.first, contains('2024年1月'));
      }
      
      print('Extreme month difference handled correctly without bouncing');
    });

    testWidgets('Should maintain normal navigation after year view month click', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 10); // selectedDay在6月
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 在年视图中点击"四月"
      final aprilFinder = find.text('四月');
      if (tester.any(aprilFinder)) {
        await tester.tap(aprilFinder);
        await tester.pumpAndSettle();
        
        // 验证跳转到4月
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(currentMonthTitle, contains('2024年4月'));
        
        // 在4月视图中点击一个日期
        final dayFinder15 = find.text('15');
        if (tester.any(dayFinder15)) {
          await tester.tap(dayFinder15);
          await tester.pumpAndSettle();
          
          // 验证日期选择正常工作
          expect(selectedDay, isNotNull);
          expect(selectedDay!.day, equals(15));
          expect(selectedDay!.month, equals(4));
          expect(selectedDay!.year, equals(2024));
          
          // 验证仍然显示4月
          expect(currentMonthTitle, contains('2024年4月'));
        }
      }
      
      print('Normal navigation maintained after year view month click');
    });

    testWidgets('Should handle multiple consecutive month clicks correctly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 9, 20); // selectedDay在9月
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthTitleHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      monthTitleHistory.add(month);
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 清空历史记录
      monthTitleHistory.clear();
      
      // 第一次点击：点击"三月"
      final marchFinder = find.text('三月');
      if (tester.any(marchFinder)) {
        await tester.tap(marchFinder);
        await tester.pumpAndSettle();
        
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(currentMonthTitle, contains('2024年3月'));
        
        // 等待确保稳定
        await tester.pump(Duration(milliseconds: 500));
        expect(currentMonthTitle, contains('2024年3月'));
      }
      
      print('Multiple consecutive month clicks handled correctly');
    });
  });
}
