import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/swipeable_calendar.dart';

void main() {
  group('Month View Structure Tests', () {
    testWidgets('Should have correct structure with fixed weekday headers', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Si<PERSON>(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    monthViewHeight: 400.0.h, // 使用较大的高度避免溢出
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证基本结构
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(find.byType(Column), findsWidgets); // 应该有Column结构
      expect(find.byType(PageView), findsOneWidget); // 应该有PageView
      expect(find.byType(GridView), findsOneWidget); // 应该有GridView
      
      // 验证星期标题
      expect(find.text('一'), findsOneWidget);
      expect(find.text('日'), findsOneWidget);
      
      // 验证没有渲染异常
      expect(tester.takeException(), isNull);
    });

    testWidgets('Should calculate dynamic rows correctly', (WidgetTester tester) async {
      // 测试2024年2月（28天，从周四开始，需要4行）
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 2, 15),
                    initialFormat: CustomCalendarFormat.month,
                    monthViewHeight: 400.0.h,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证基本结构正确
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    testWidgets('Should use mainAxisExtent instead of childAspectRatio', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    monthViewHeight: 400.0.h,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证GridView存在
      expect(find.byType(GridView), findsOneWidget);
      
      // 验证没有渲染异常（说明使用了正确的布局方式）
      expect(tester.takeException(), isNull);
    });
  });
}
