import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('SwipeableCalendar SelectedDay Persistence Tests', () {
    testWidgets('selectedDay should persist when swiping between months', (WidgetTester tester) async {
      final testDate = DateTime(2024, 1, 15); // 2024年1月15日
      DateTime? selectedDay;
      String? currentMonthTitle;
      List<DateTime> selectedDayHistory = []; // 记录selectedDay的变化历史
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: <PERSON><PERSON><PERSON>(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                      selectedDayHistory.add(day);
                      print('onDaySelected called: $day');
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      print('onMonthChanged called: $month');
                    },
                    initialFormat: CustomCalendarFormat.month,
                    swipeDirection: SwipeDirection.vertical,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(currentMonthTitle, contains('2024年1月'));
      
      // 用户点击选择1月17日
      final dayFinder17 = find.text('17');
      if (tester.any(dayFinder17)) {
        await tester.tap(dayFinder17);
        await tester.pumpAndSettle();

        // 验证选中的日期正确
        expect(selectedDay, isNotNull);
        expect(selectedDay!.day, equals(17));
        expect(selectedDay!.month, equals(1));
        expect(selectedDay!.year, equals(2024));
        expect(selectedDayHistory.length, equals(1)); // 只有一次onDaySelected调用
        
        print('User selected: ${selectedDay}');
      }

      // 重置历史记录，准备测试滑动
      selectedDayHistory.clear();
      final originalSelectedDay = selectedDay;
      
      // 模拟向上滑动切换到下个月（2月）
      final pageViewFinder = find.byType(PageView);
      expect(pageViewFinder, findsOneWidget);
      
      // 模拟滑动手势 - 向上滑动
      await tester.drag(pageViewFinder, Offset(0, -300));
      await tester.pumpAndSettle();

      // 关键验证：滑动后selectedDay应该保持不变
      expect(selectedDay, equals(originalSelectedDay)); // selectedDay不应该改变
      expect(selectedDayHistory.length, equals(0)); // 滑动不应该触发onDaySelected
      expect(currentMonthTitle, contains('2024年2月')); // 但显示的月份应该改变
      
      print('After swipe - selectedDay: $selectedDay, monthTitle: $currentMonthTitle');
      
      // 再次模拟向上滑动切换到3月
      await tester.drag(pageViewFinder, Offset(0, -300));
      await tester.pumpAndSettle();

      // 再次验证：selectedDay仍然保持不变
      expect(selectedDay, equals(originalSelectedDay)); // selectedDay仍然不应该改变
      expect(selectedDayHistory.length, equals(0)); // 仍然没有onDaySelected调用
      expect(currentMonthTitle, contains('2024年3月')); // 显示3月
      
      print('After second swipe - selectedDay: $selectedDay, monthTitle: $currentMonthTitle');
    });

    testWidgets('selectedDay should only change when user taps a date', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 10); // 2024年6月10日
      DateTime? selectedDay;
      String? currentMonthTitle;
      int onDaySelectedCallCount = 0;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                      onDaySelectedCallCount++;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentMonthTitle, contains('2024年6月'));
      expect(onDaySelectedCallCount, equals(0)); // 初始化时不应该调用onDaySelected
      
      // 用户点击选择6月15日
      final dayFinder15 = find.text('15');
      if (tester.any(dayFinder15)) {
        await tester.tap(dayFinder15);
        await tester.pumpAndSettle();

        expect(selectedDay!.day, equals(15));
        expect(selectedDay!.month, equals(6));
        expect(onDaySelectedCallCount, equals(1)); // 点击后应该调用一次
      }

      // 模拟多次滑动
      final pageViewFinder = find.byType(PageView);
      
      // 滑动到7月
      await tester.drag(pageViewFinder, Offset(0, -200));
      await tester.pumpAndSettle();
      
      // 滑动到8月
      await tester.drag(pageViewFinder, Offset(0, -200));
      await tester.pumpAndSettle();
      
      // 滑动回6月
      await tester.drag(pageViewFinder, Offset(0, 400));
      await tester.pumpAndSettle();

      // 验证：多次滑动后，onDaySelected仍然只被调用了一次
      expect(onDaySelectedCallCount, equals(1));
      expect(selectedDay!.day, equals(15)); // selectedDay保持不变
      expect(selectedDay!.month, equals(6));
    });

    testWidgets('week view should also maintain selectedDay when swiping', (WidgetTester tester) async {
      final testDate = DateTime(2024, 3, 15); // 2024年3月15日（周五）
      DateTime? selectedDay;
      String? currentMonthTitle;
      int onDaySelectedCallCount = 0;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                      onDaySelectedCallCount++;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    initialFormat: CustomCalendarFormat.week,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 在周视图中点击选择一个日期
      final dayFinder17 = find.text('17');
      if (tester.any(dayFinder17)) {
        await tester.tap(dayFinder17);
        await tester.pumpAndSettle();

        expect(selectedDay!.day, equals(17));
        expect(onDaySelectedCallCount, equals(1));
      }

      // 在周视图中左右滑动切换周
      final pageViewFinder = find.byType(PageView);
      
      // 向右滑动到下一周
      await tester.drag(pageViewFinder, Offset(-200, 0));
      await tester.pumpAndSettle();
      
      // 向左滑动到上一周
      await tester.drag(pageViewFinder, Offset(200, 0));
      await tester.pumpAndSettle();

      // 验证：周视图滑动也不应该改变selectedDay
      expect(onDaySelectedCallCount, equals(1)); // 仍然只有一次点击
      expect(selectedDay!.day, equals(17)); // selectedDay保持不变
    });
  });
}
