# 工具类文档索引

## 概述

本目录包含了项目中所有自定义工具类的详细文档。这些工具类旨在提高代码复用性、保持样式一致性，并简化常见操作。

## 工具类列表

### 🎨 UI相关工具类

#### [ToastUtil](./toast_util.md) - Toast消息工具类
- **功能**: 统一管理应用中的Toast显示
- **特性**: 无图标显示、多种类型、预设场景方法
- **文件**: `lib/utils/toast_util.dart`
- **导入**: `import 'package:align/utils/toast_util.dart';`

**常用方法**:
```dart
ToastUtil.showSuccess('操作成功');
ToastUtil.showError('操作失败');
ToastUtil.showLoginFailed();
ToastUtil.showDataLoading();
```

#### [ColorUtil](./color_util.md) - 颜色工具类
- **功能**: 定义应用中使用的标准颜色常量
- **特性**: 统一颜色管理、语义化命名
- **文件**: `lib/utils/color_util.dart`
- **导入**: `import 'package:align/utils/color_util.dart';`

**常用颜色**:
```dart
ColorUtil.primaryBgColor    // 主背景色
ColorUtil.borderGrey        // 边框灰色
```

#### [TextUtil](./text_util.md) - 文本样式工具类
- **功能**: 提供链式调用的TextStyle构建方式
- **特性**: 链式调用、响应式设计、扩展性强
- **文件**: `lib/utils/text_util.dart`
- **导入**: `import 'package:align/utils/text_util.dart';`

**常用方法**:
```dart
TextUtil.base.sp(16).semiBold.withColor(Colors.blue)
```

### 💾 数据存储工具类

#### [SpUtil](./sp_util.md) - 本地数据持久化存储工具类
- **功能**: 基于SharedPreferences的完整数据存储解决方案
- **特性**: 用户信息管理、认证令牌存储、偏好设置、登录状态管理
- **文件**: `lib/utils/sp_util.dart`
- **导入**: `import 'package:align/utils/sp_util.dart';`

**常用方法**:
```dart
// 用户信息管理
await SpUtil.saveUserInfo(user);
final user = await SpUtil.getUserInfo();

// 登录状态管理
await SpUtil.completeLogin(user: user, autoLogin: true);
await SpUtil.completeLogout();

// 基础存储
await SpUtil.setString('key', 'value');
final value = await SpUtil.getString('key');
```

## 使用规范

### 1. 导入规范

```dart
// 推荐：按字母顺序导入工具类
import 'package:align/utils/color_util.dart';
import 'package:align/utils/text_util.dart';
import 'package:align/utils/toast_util.dart';
```

### 2. 命名规范

- 工具类名称使用 `XxxUtil` 格式
- 方法名称使用驼峰命名法
- 常量使用语义化命名

### 3. 使用优先级

1. **优先使用工具类方法**：避免重复代码
2. **保持一致性**：相同功能使用相同的工具类方法
3. **扩展而非修改**：需要新功能时扩展工具类而不是修改现有方法

## 组合使用示例

### Toast + ColorUtil

```dart
// 在自定义Toast中使用ColorUtil的颜色
ToastUtil.showCustom(
  message: '自定义消息',
  backgroundColor: ColorUtil.primaryColor,
  textColor: Colors.white,
);
```

### TextUtil + ColorUtil

```dart
// 创建带有统一颜色的文本样式
Text(
  '标题文本',
  style: TextUtil.base
    .sp(18)
    .semiBold
    .withColor(ColorUtil.primaryTextColor),
)
```

### 完整示例

```dart
class CustomCard extends StatelessWidget {
  final String title;
  final String content;
  final VoidCallback? onTap;

  const CustomCard({
    Key? key,
    required this.title,
    required this.content,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap?.call();
        ToastUtil.showInfo('卡片被点击');
      },
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: ColorUtil.primaryBgColor,
          border: Border.all(color: ColorUtil.borderGrey),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextUtil.base.sp(18).semiBold,
            ),
            SizedBox(height: 8.h),
            Text(
              content,
              style: TextUtil.base.sp(14).withColor(Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
```

## 扩展指南

### 添加新工具类

1. **创建工具类文件**：`lib/utils/new_util.dart`
2. **编写文档**：`docs/utils/new_util.md`
3. **更新索引**：在本文档中添加新工具类信息
4. **添加测试**：`test/utils/new_util_test.dart`

### 扩展现有工具类

1. **添加新方法**：在对应的工具类中添加
2. **更新文档**：更新对应的markdown文档
3. **添加示例**：在文档中添加使用示例
4. **测试验证**：确保新功能正常工作

## 最佳实践

### 1. 代码组织

```dart
// 好的做法：使用工具类
Container(
  color: ColorUtil.primaryBgColor,
  child: Text(
    'Hello',
    style: TextUtil.base.sp(16).semiBold,
  ),
)

// 避免：硬编码值
Container(
  color: Color(0xFFFFFFFF),
  child: Text(
    'Hello',
    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
  ),
)
```

### 2. 错误处理

```dart
// 好的做法：使用预设的Toast方法
try {
  await someOperation();
  ToastUtil.showOperationSuccess();
} catch (e) {
  ToastUtil.showOperationFailed();
}
```

### 3. 性能优化

```dart
// 好的做法：缓存样式
class MyWidget extends StatelessWidget {
  static final _titleStyle = TextUtil.base.sp(18).semiBold;
  
  @override
  Widget build(BuildContext context) {
    return Text('Title', style: _titleStyle);
  }
}
```

## 注意事项

1. **版本兼容性**：工具类方法的修改要考虑向后兼容
2. **性能影响**：避免在build方法中重复创建相同的对象
3. **测试覆盖**：新增的工具类方法要有对应的测试
4. **文档同步**：代码修改后要及时更新文档

## 贡献指南

1. **遵循现有规范**：保持与现有工具类的一致性
2. **完善文档**：为新功能编写详细的文档和示例
3. **添加测试**：确保代码质量和稳定性
4. **代码审查**：提交前进行代码审查

---

**最后更新**: 2025-01-19
**维护者**: 开发团队
