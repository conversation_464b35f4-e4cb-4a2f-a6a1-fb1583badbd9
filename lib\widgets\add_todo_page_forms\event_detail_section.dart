import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:animated_toggle_switch/animated_toggle_switch.dart';

import '../../utils/color_util.dart';
import '../../utils/text_util.dart';

/// 事项详细 Section 组件
class EventDetailSection extends StatefulWidget {
  final Color cardColor;
  final bool isAllDay;
  final Function(bool) onAllDayChanged;
  final TextEditingController startTimeController;
  final TextEditingController endTimeController;
  final TextEditingController locationController;

  const EventDetailSection({
    super.key,
    required this.cardColor,
    required this.isAllDay,
    required this.onAllDayChanged,
    required this.startTimeController,
    required this.endTimeController,
    required this.locationController,
  });

  @override
  State<EventDetailSection> createState() => _EventDetailSectionState();
}

class _EventDetailSectionState extends State<EventDetailSection> {
  // 获取今天的日期格式化字符串
  String _getTodayDateString() {
    final now = DateTime.now();
    return '${now.year}年${now.month}月${now.day}日';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              '事项详细',
              style: TextUtil.base.sp(18).semiBold.withColor(widget.cardColor),
            ),
          ),
          SizedBox(height: 16.h),

          // 全天开关
          Row(
            children: [
              Text(
                '全天',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              const Spacer(),
              AnimatedToggleSwitch<bool>.dual(
                current: widget.isAllDay,
                first: false,
                second: true,
                spacing: 12.w,
                style: ToggleStyle(
                  borderColor: Colors.transparent,
                  boxShadow: const [],
                  indicatorColor: widget.cardColor,
                  backgroundColor: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                borderWidth: 0,
                height: 30.h,
                onChanged: widget.onAllDayChanged,
                styleBuilder: (value) => ToggleStyle(
                  indicatorColor: CupertinoColors.white,
                  backgroundColor: value ? widget.cardColor : CupertinoColors.systemGrey5,
                  borderColor: Colors.transparent,
                  borderRadius: BorderRadius.circular(16.r),
                  indicatorBorderRadius: BorderRadius.circular(99.r),
                  indicatorBorder: Border.all(
                    color: value ? widget.cardColor : CupertinoColors.systemGrey5,
                    width: 2.w,
                  ),
                  indicatorBoxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.04),
                      blurRadius: 8.r,
                      offset: Offset(0, 4.h),
                    ),
                  ],
                ),
                indicatorSize: Size(30.h, 30.h),
                textBuilder: (value) => Center(
                  child: Text(
                    value ? '是' : '否',
                    style: TextUtil.base.sp(14).withColor(value ? CupertinoColors.white : widget.cardColor),
                  ),
                ),
                animationDuration: const Duration(milliseconds: 200),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 开始时间
          _buildTimeField('开始时间', widget.startTimeController),
          SizedBox(height: 16.h),

          // 结束时间
          _buildTimeField('结束时间', widget.endTimeController),
          SizedBox(height: 16.h),

          // 地点
          _buildLocationField(),
        ],
      ),
    );
  }

  Widget _buildTimeField(String label, TextEditingController controller) {
    return Row(
      children: [
        Text(label, style: TextUtil.base.sp(14).withColor(widget.cardColor)),
        const Spacer(),
        if (widget.isAllDay) ...[
          // 全天模式显示日期选择器
          GestureDetector(
            onTap: () => _showDatePicker(controller),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                border: Border.all(color: widget.cardColor),
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                controller.text.isEmpty ? _getTodayDateString() : controller.text,
                style: TextUtil.base
                    .sp(14)
                    .withColor(
                      controller.text.isEmpty
                          ? CupertinoColors.systemGrey
                          : widget.cardColor,
                    ),
              ),
            ),
          ),
        ] else ...[
          // 非全天模式显示日期和时间选择器
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () => _showDatePicker(controller),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: widget.cardColor),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    controller.text.isEmpty
                        ? _getTodayDateString()
                        : controller.text,
                    style: TextUtil.base
                        .sp(12)
                        .withColor(
                          controller.text.isEmpty
                              ? CupertinoColors.systemGrey
                              : widget.cardColor,
                        ),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              GestureDetector(
                onTap: () => _showTimePicker(controller),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: widget.cardColor),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    '08:00',
                    style: TextUtil.base.sp(12).withColor(widget.cardColor),
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildLocationField() {
    return Row(
      children: [
        Text(
          '地点',
          style: TextUtil.base.sp(14).withColor(widget.cardColor),
        ),
        SizedBox(width: 60.w),
        Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              border: Border.all(color: widget.cardColor),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              children: [
                Icon(
                  CupertinoIcons.search,
                  size: 20.sp,
                  color: widget.cardColor,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: CupertinoTextField(
                    controller: widget.locationController,
                    placeholder: '搜索',
                    placeholderStyle: TextUtil.base.sp(14).withColor(CupertinoColors.systemGrey),
                    style: TextUtil.base.sp(14).withColor(widget.cardColor),
                    decoration: null,
                    padding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 显示日期选择器
  void _showDatePicker(TextEditingController controller) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          color: CupertinoColors.white,
          child: Column(
            children: [
              Container(
                height: 50.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        '取消',
                        style: TextUtil.base.sp(16).withColor(CupertinoColors.systemGrey),
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.date,
                  onDateTimeChanged: (DateTime dateTime) {
                    setState(() {
                      controller.text = '${dateTime.year}年${dateTime.month}月${dateTime.day}日';
                    });
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 显示时间选择器
  void _showTimePicker(TextEditingController controller) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          color: CupertinoColors.white,
          child: Column(
            children: [
              Container(
                height: 50.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        '取消',
                        style: TextUtil.base.sp(16).withColor(CupertinoColors.systemGrey),
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.time,
                  use24hFormat: true,
                  onDateTimeChanged: (DateTime dateTime) {
                    // 这里可以更新时间显示，但由于我们使用的是固定显示，暂时不处理
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
