import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('Year View Month Click Tests', () {
    testWidgets('Should stay in clicked month and not jump back to selectedDay month', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 15); // selectedDay在6月
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthTitleHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      monthTitleHistory.add(month);
                      print('Month title changed to: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                      print('View changed to: $format');
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（年视图，显示selectedDay所在的月份）
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年6月'));
      
      // 清空历史记录
      monthTitleHistory.clear();
      
      // 在年视图中点击"三月"
      final marchFinder = find.text('三月');
      expect(marchFinder, findsOneWidget);
      
      await tester.tap(marchFinder);
      await tester.pumpAndSettle();
      
      // 验证切换到月视图
      expect(currentFormat, equals(CustomCalendarFormat.month));
      
      // 关键验证：应该显示3月，而不是跳回6月
      expect(currentMonthTitle, contains('2024年3月'));
      
      // 验证selectedDay保持不变
      expect(selectedDay, isNull); // 没有点击日期，所以selectedDay应该还是初始值
      
      // 等待一段时间，确保没有自动跳回
      await tester.pump(Duration(milliseconds: 500));
      await tester.pumpAndSettle();
      
      // 再次验证仍然显示3月
      expect(currentMonthTitle, contains('2024年3月'));
      expect(currentFormat, equals(CustomCalendarFormat.month));
      
      print('Successfully stayed in March after clicking, did not jump back to June');
    });

    testWidgets('Should handle multiple month clicks correctly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 12, 25); // selectedDay在12月
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年12月'));
      
      // 点击"一月"
      final januaryFinder = find.text('一月');
      if (tester.any(januaryFinder)) {
        await tester.tap(januaryFinder);
        await tester.pumpAndSettle();
        
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(currentMonthTitle, contains('2024年1月'));
        
        // 等待确保稳定
        await tester.pump(Duration(milliseconds: 300));
        expect(currentMonthTitle, contains('2024年1月'));
      }
      
      print('January click test passed');
    });

    testWidgets('Should maintain selectedDay when clicking different months', (WidgetTester tester) async {
      final testDate = DateTime(2024, 8, 10); // selectedDay在8月10日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年8月'));
      
      // 点击"五月"
      final mayFinder = find.text('五月');
      if (tester.any(mayFinder)) {
        await tester.tap(mayFinder);
        await tester.pumpAndSettle();
        
        // 验证跳转到5月
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(currentMonthTitle, contains('2024年5月'));
        
        // 验证selectedDay没有被意外触发（应该仍然是null，因为我们没有点击日期）
        expect(selectedDay, isNull);
        
        // 等待确保稳定在5月
        await tester.pump(Duration(milliseconds: 500));
        expect(currentMonthTitle, contains('2024年5月'));
      }
      
      print('SelectedDay maintained correctly when clicking different months');
    });

    testWidgets('Should handle year view to month view transition smoothly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 4, 20); // selectedDay在4月
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      int viewChangeCount = 0;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                      viewChangeCount++;
                      print('View changed to: $format (count: $viewChangeCount)');
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(viewChangeCount, equals(1)); // 初始化时的一次调用
      
      // 点击"二月"（更可能在屏幕可见区域）
      final februaryFinder = find.text('二月');
      if (tester.any(februaryFinder)) {
        await tester.tap(februaryFinder, warnIfMissed: false);
        await tester.pumpAndSettle();

        // 验证视图切换
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(viewChangeCount, equals(2)); // 应该只增加一次
        expect(currentMonthTitle, contains('2024年2月'));

        // 等待确保没有额外的视图切换
        await tester.pump(Duration(milliseconds: 500));
        expect(viewChangeCount, equals(2)); // 应该仍然是2，没有额外的切换
        expect(currentMonthTitle, contains('2024年2月'));
      } else {
        // 如果找不到二月，跳过这个测试
        print('February not found, skipping tap test');
      }
      
      print('Year to month transition handled smoothly');
    });
  });
}
