import 'package:json_annotation/json_annotation.dart';

part 'symptom_models.g.dart';

/// 症状数据根模型
@JsonSerializable()
class SymptomData {
  final List<SymptomCategory> symptom;

  SymptomData({required this.symptom});

  factory SymptomData.fromJson(Map<String, dynamic> json) => _$SymptomDataFromJson(json);
  Map<String, dynamic> toJson() => _$SymptomDataToJson(this);
}

/// 症状分类模型
@JsonSerializable()
class SymptomCategory {
  final String title;
  final int type;
  final List<SymptomContent> content;

  SymptomCategory({
    required this.title,
    required this.type,
    required this.content,
  });

  factory SymptomCategory.fromJson(Map<String, dynamic> json) => _$SymptomCategoryFromJson(json);
  Map<String, dynamic> toJson() => _$SymptomCategoryToJson(this);
}

/// 症状内容模型
/// 支持不同类型的症状数据：
/// - type 1: 心情 (description)
/// - type 2: 食欲 (name, weight)
/// - type 3: 体重 (weight, unit, time)
/// - type 4: 药物 (medicineName, quantity, unit, time, cycle)
@JsonSerializable()
class SymptomContent {
  final String? description; // type 1: 心情
  final String? name; // type 2: 食欲
  final String? weight; // type 2: 食欲, type 3: 体重
  final String? unit; // type 3: 体重, type 4: 药物
  final String? time; // type 3: 体重, type 4: 药物
  @JsonKey(name: 'medicine_name')
  final String? medicineName; // type 4: 药物
  final String? quantity; // type 4: 药物
  final List<int>? cycle; // type 4: 药物

  SymptomContent({
    this.description,
    this.name,
    this.weight,
    this.unit,
    this.time,
    this.medicineName,
    this.quantity,
    this.cycle,
  });

  factory SymptomContent.fromJson(Map<String, dynamic> json) => _$SymptomContentFromJson(json);
  Map<String, dynamic> toJson() => _$SymptomContentToJson(this);
}
