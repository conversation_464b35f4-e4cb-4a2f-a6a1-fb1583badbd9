import 'package:json_annotation/json_annotation.dart';

part 'preparation_info.g.dart';

/// 项目准备信息模型
@JsonSerializable()
class PreparationInfo {
  /// 标题
  final String title;
  
  /// 内容（Markdown格式）
  final String content;

  const PreparationInfo({
    required this.title,
    required this.content,
  });

  factory PreparationInfo.fromJson(Map<String, dynamic> json) =>
      _$PreparationInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PreparationInfoToJson(this);
}
