import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('SwipeableCalendar Fixes Tests', () {
    testWidgets('Should handle drag gesture view switching without month jumping', (WidgetTester tester) async {
      final testDate = DateTime(2024, 3, 15); // 2024年3月15日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthTitleHistory = []; // 记录月份标题变化历史
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      monthTitleHistory.add(month);
                      print('Month changed to: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                      print('View changed to: $format');
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentFormat, equals(CustomCalendarFormat.month));
      expect(currentMonthTitle, contains('2024年3月'));
      
      // 清空历史记录
      monthTitleHistory.clear();
      
      // 在月视图中选择一个日期
      final dayFinder17 = find.text('17');
      if (tester.any(dayFinder17)) {
        await tester.tap(dayFinder17);
        await tester.pumpAndSettle();
        
        expect(selectedDay!.day, equals(17));
        expect(selectedDay!.month, equals(3));
      }

      // 模拟拖拽手势切换到年视图（向上拖拽）
      final dragHandle = find.byType(GestureDetector);
      if (tester.any(dragHandle)) {
        await tester.drag(dragHandle.first, Offset(0, -50)); // 向上拖拽
        await tester.pumpAndSettle();
        
        // 验证视图切换成功
        expect(currentFormat, equals(CustomCalendarFormat.year));
        
        // 验证selectedDay保持不变
        expect(selectedDay!.day, equals(17));
        expect(selectedDay!.month, equals(3));
        expect(selectedDay!.year, equals(2024));
        
        // 验证月份标题没有异常跳动（应该仍然显示3月）
        expect(currentMonthTitle, contains('2024年3月'));
        
        print('Drag gesture view switch successful without month jumping');
      }
    });

    testWidgets('Should handle year view layout without overflow', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 15); // 2024年6月15日
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SizedBox(
                    height: 600, // 限制高度来测试溢出问题
                    child: SwipeableCalendar(
                      selectedDay: testDate,
                      onDaySelected: (day) {},
                      onMonthChanged: (month) {
                        currentMonthTitle = month;
                      },
                      onViewChanged: (format) {
                        currentFormat = format;
                      },
                      initialFormat: CustomCalendarFormat.year,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证年视图正确显示
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年6月'));
      
      // 验证年视图包含滚动组件
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      expect(find.byType(GridView), findsAtLeastNWidgets(1)); // 年视图包含多个GridView（主GridView + 每个月的小GridView）
      
      // 验证月份名称显示
      expect(find.text('六月'), findsOneWidget);
      expect(find.text('七月'), findsOneWidget);
      
      // 测试滚动功能
      final scrollView = find.byType(SingleChildScrollView);
      await tester.drag(scrollView, Offset(0, -100)); // 向上滚动
      await tester.pumpAndSettle();
      
      print('Year view layout handles scrolling correctly');
    });

    testWidgets('Should maintain state consistency during multiple view switches', (WidgetTester tester) async {
      final testDate = DateTime(2024, 9, 20); // 2024年9月20日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.week,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（周视图）
      expect(currentFormat, equals(CustomCalendarFormat.week));
      expect(currentMonthTitle, contains('2024年9月'));
      
      // 选择一个日期
      final dayFinder22 = find.text('22');
      if (tester.any(dayFinder22)) {
        await tester.tap(dayFinder22);
        await tester.pumpAndSettle();
        
        expect(selectedDay!.day, equals(22));
        expect(selectedDay!.month, equals(9));
      }

      // 多次视图切换测试
      final dragHandle = find.byType(GestureDetector);
      if (tester.any(dragHandle)) {
        // 切换到月视图
        await tester.drag(dragHandle.first, Offset(0, -30));
        await tester.pumpAndSettle();
        
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(selectedDay!.day, equals(22)); // selectedDay保持不变
        expect(currentMonthTitle, contains('2024年9月'));
        
        // 切换到年视图
        await tester.drag(dragHandle.first, Offset(0, -30));
        await tester.pumpAndSettle();
        
        expect(currentFormat, equals(CustomCalendarFormat.year));
        expect(selectedDay!.day, equals(22)); // selectedDay仍然保持不变
        expect(currentMonthTitle, contains('2024年9月'));
        
        // 切换回月视图
        await tester.drag(dragHandle.first, Offset(0, 30));
        await tester.pumpAndSettle();
        
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(selectedDay!.day, equals(22)); // selectedDay仍然保持不变
        expect(currentMonthTitle, contains('2024年9月'));
        
        print('Multiple view switches maintain state consistency');
      }
    });

    testWidgets('Should handle year view month selection correctly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 12, 10); // 2024年12月10日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证年视图初始状态
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年12月'));
      
      // 在年视图中点击其他月份（如果找到的话）
      final monthFinder = find.text('三月');
      if (tester.any(monthFinder)) {
        await tester.tap(monthFinder);
        await tester.pumpAndSettle();
        
        // 验证切换到月视图并显示正确的月份
        expect(currentFormat, equals(CustomCalendarFormat.month));
        expect(currentMonthTitle, contains('2024年3月'));
        
        print('Year view month selection works correctly');
      }
    });
  });
}
