import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/color_util.dart';
import '../utils/text_util.dart';
import '../widgets/app_button.dart';
import 'add_todo_page.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

class PrenatalCheckupListPage extends StatefulWidget {
  const PrenatalCheckupListPage({super.key});

  @override
  State<PrenatalCheckupListPage> createState() => _PrenatalCheckupListPageState();
}

class _PrenatalCheckupListPageState extends State<PrenatalCheckupListPage> {
  // 模拟产检数据
  final List<PrenatalCheckupItem> _checkupItems = [
    PrenatalCheckupItem(
      date: '2025年05月19日',
      weekInfo: '第5-9周产检',
      hasData: true,
    ),
    PrenatalCheckupItem(
      date: '2025年06月14日',
      weekInfo: '第10-13周产检',
      hasData: true,
    ),
    PrenatalCheckupItem(
      date: '2025年06月28日',
      weekInfo: '第14-19周产检',
      hasData: true,
    ),
    PrenatalCheckupItem(
      date: '2025年07月26日',
      weekInfo: '第20-24周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年08月23日',
      weekInfo: '第25-28周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年09月20日',
      weekInfo: '第29-32周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年10月04日',
      weekInfo: '第33-36周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年10月18日',
      weekInfo: '第37-40周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年11月01日',
      weekInfo: '第41-42周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年11月15日',
      weekInfo: '第43-44周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年11月29日',
      weekInfo: '第45-46周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年12月13日',
      weekInfo: '第47-48周产检',
      hasData: false,
    ),
    PrenatalCheckupItem(
      date: '2025年12月27日',
      weekInfo: '第49-50周产检',
      hasData: false,
    ),
  ];

  final _accentColor = const Color(0xFFFE2C55);

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: const Color(0xFFFFE3E3),
      child: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildCheckupList(),
            ),
            _buildAddButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          AppButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Icon(
              CupertinoIcons.left_chevron,
              size: 24.sp,
              color: CupertinoColors.systemRed,
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                '产检记录',
                style: TextUtil.base.sp(24).semiBold.withColor(_accentColor),
              ),
            ),
          ),
          SizedBox(width: 48.w), // 占位，保持标题居中
        ],
      ),
    );
  }

  Widget _buildCheckupList() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      itemCount: _checkupItems.length,
      itemBuilder: (context, index) {
        final item = _checkupItems[index];
        return _buildCheckupItem(item);
      },
    );
  }

  Widget _buildCheckupItem(PrenatalCheckupItem item) {
    return AppButton(
      onPressed: () {
        if (item.hasData) {
          _navigateToPrenatalDetail(item);
        } else {
          _navigateToAddCheckup();
        }
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h),
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: item.hasData ? CupertinoColors.white : const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: item.hasData ? ColorUtil.borderGrey : const Color(0xFFE8E8E8),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Text(
              item.date,
              style: item.hasData
                  ? TextUtil.base.sp(16).medium.black
                  : TextUtil.base.sp(16).medium.grey,
            ),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 8.w),
              width: 2.w,
              height: 16.h,
              color: item.hasData ? CupertinoColors.black : const Color(0xFFE8E8E8),
            ),
            Text(
              item.weekInfo,
              style: item.hasData
                  ? TextUtil.base.sp(16).medium.black
                  : TextUtil.base.sp(16).medium.grey,
            ),
            Spacer(),
            if (item.hasData)
              Icon(
                CupertinoIcons.eye,
                size: 20.sp,
                color: const Color(0xFF666666),
              )
            else
              Icon(
                CupertinoIcons.add,
                size: 20.sp,
                color: CupertinoColors.systemRed,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddButton() {
    return Container(
      padding: EdgeInsets.all(24.w),
      child: SizedBox(
        width: double.infinity,
        child: CupertinoButton(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          color: CupertinoColors.systemRed,
          borderRadius: BorderRadius.circular(25.r),
          onPressed: _navigateToAddCheckup,
          child: Text(
            '添加',
            style: TextUtil.base.sp(16).semiBold.white,
          ),
        ),
      ),
    );
  }

  void _navigateToAddCheckup() {
    showCupertinoModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFFFFE3E3),
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFE3E3),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: AddTodoPage(
          cardColor: _accentColor,
          cardBgColor: const Color(0xFFFFE3E3),
          isPrenatalDetail: false, // 添加模式
        ),
      ),
    );
  }

  void _navigateToPrenatalDetail(PrenatalCheckupItem item) {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => CupertinoPageScaffold(
          backgroundColor: const Color(0xFFFFE3E3),
          child: SafeArea(
            child: AddTodoPage(
              cardColor: _accentColor,
              cardBgColor: const Color(0xFFFFE3E3),
              isPrenatalDetail: true,
              prenatalTitle: item.weekInfo,
            ),
          ),
        ),
      ),
    );
  }


}

class PrenatalCheckupItem {
  final String date;
  final String weekInfo;
  final bool hasData;

  PrenatalCheckupItem({
    required this.date,
    required this.weekInfo,
    required this.hasData,
  });
}
