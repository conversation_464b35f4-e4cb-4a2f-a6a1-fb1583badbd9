// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'emotion_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmotionModel _$EmotionModelFromJson(Map<String, dynamic> json) => EmotionModel(
  name: json['name'] as String,
  tagIndex: json['tagIndex'] as String,
)..isShowSuspension = json['isShowSuspension'] as bool;

Map<String, dynamic> _$EmotionModelToJson(EmotionModel instance) =>
    <String, dynamic>{
      'isShowSuspension': instance.isShowSuspension,
      'name': instance.name,
      'tagIndex': instance.tagIndex,
    };
