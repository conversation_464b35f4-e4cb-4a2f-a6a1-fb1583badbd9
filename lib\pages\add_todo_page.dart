import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/text_util.dart';
import '../utils/sp_util.dart';
import '../models/models.dart';
import '../widgets/add_todo_page_forms/pregnancy_check_form.dart';
import '../widgets/add_todo_page_forms/event_form.dart';
import '../widgets/add_todo_page_forms/task_form.dart';

/// 添加待办事项页面
class AddTodoPage extends StatefulWidget {
  final Color cardColor;
  final Color cardBgColor;
  final bool isPrenatalDetail;
  final String? prenatalTitle;

  const AddTodoPage({
    super.key,
    required this.cardColor,
    required this.cardBgColor,
    this.isPrenatalDetail = false,
    this.prenatalTitle,
  });

  @override
  State<AddTodoPage> createState() => _AddTodoPageState();
}

class _AddTodoPageState extends State<AddTodoPage> {
  int selectedCategoryIndex = 0;

  // 分类数据
  final List<String> categories = ['产检', '事项', '任务'];

  // 日期相关状态
  DateTime _selectedDate = DateTime.now();
  UserModel? _userInfo;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  // 加载用户信息
  Future<void> _loadUserInfo() async {
    final userInfo = await SpUtil.getUserInfo();
    if (mounted) {
      setState(() {
        _userInfo = userInfo;
      });
    }
  }

  // 切换到上一天
  void _goToPreviousDay() {
    setState(() {
      _selectedDate = _selectedDate.subtract(const Duration(days: 1));
    });
  }

  // 切换到下一天
  void _goToNextDay() {
    setState(() {
      _selectedDate = _selectedDate.add(const Duration(days: 1));
    });
  }

  // 格式化日期显示
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return '今天';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return '昨天';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return '明天';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  // 格式化完整日期
  String _formatFullDate(DateTime date) {
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[date.weekday - 1];
    return '${date.year}年${date.month}月${date.day}日（$weekday）';
  }

  // 计算孕周信息
  String _getPregnancyWeekInfo() {
    if (_userInfo?.pregnancyWeeks == null) {
      return '15周 3天'; // 默认显示
    }

    final weeks = _userInfo!.pregnancyWeeks!;
    return '${weeks['weeks']}周 ${weeks['days']}天';
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isPrenatalDetail) {
      return _buildPrenatalDetailLayout();
    } else {
      return _buildDefaultLayout();
    }
  }

  Widget _buildDefaultLayout() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(top: 16.h),
          child: Center(
            child: Container(
              width: 36.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: widget.cardColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
        ),

        // 标题区域 - 水平布局
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 左箭头
              GestureDetector(
                onTap: _goToPreviousDay,
                child: Icon(
                  CupertinoIcons.chevron_left,
                  size: 20.sp,
                  color: widget.cardColor,
                ),
              ),

              // 中间标题内容
              Expanded(
                child: Column(
                  children: [
                    Text(
                      _formatDate(_selectedDate),
                      style: TextUtil.base
                          .sp(24)
                          .semiBold
                          .withColor(widget.cardColor),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      _formatFullDate(_selectedDate),
                      style: TextUtil.base.sp(14).withColor(widget.cardColor),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      _getPregnancyWeekInfo(),
                      style: TextUtil.base
                          .sp(12)
                          .withColor(CupertinoColors.systemGrey),
                    ),
                  ],
                ),
              ),

              // 右箭头
              GestureDetector(
                onTap: _goToNextDay,
                child: Icon(
                  CupertinoIcons.chevron_right,
                  size: 20.sp,
                  color: widget.cardColor,
                ),
              ),
            ],
          ),
        ),

        // 分类选择器
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Row(
            children: categories.asMap().entries.map((entry) {
              final index = entry.key;
              final category = entry.value;
              final isSelected = index == selectedCategoryIndex;

              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedCategoryIndex = index;
                    });
                  },
                  child: Container(
                    margin: EdgeInsets.only(right: index < categories.length - 1 ? 8.w : 0),
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    decoration: BoxDecoration(
                      color: isSelected ? widget.cardColor : CupertinoColors.white,
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color: widget.cardColor,
                        width: 1.w,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        category,
                        style: TextUtil.base.sp(14).semiBold.withColor(
                          isSelected ? CupertinoColors.white : widget.cardColor,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),

        SizedBox(height: 24.h),

        // 内容区域
        Expanded(
          child: _buildContentArea(),
        ),
      ],
    );
  }

  Widget _buildPrenatalDetailLayout() {
    return Column(
      children: [
        // 产检详情页面的顶部导航栏
        _buildPrenatalDetailHeader(),

        // 内容区域
        Expanded(
          child: PregnancyCheckForm(
            cardColor: widget.cardColor,
            cardBgColor: widget.cardBgColor,
            isDetailMode: true,
            onSave: _handlePrenatalDetailSave,
          ),
        ),
      ],
    );
  }

  Widget _buildPrenatalDetailHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Icon(
              CupertinoIcons.chevron_left,
              size: 24.sp,
              color: widget.cardColor,
            ),
          ),

          // 标题
          Expanded(
            child: Center(
              child: Text(
                widget.prenatalTitle ?? '产检详情',
                style: TextUtil.base
                    .sp(18)
                    .semiBold
                    .withColor(widget.cardColor),
              ),
            ),
          ),

          // 保存按钮
          GestureDetector(
            onTap: _handlePrenatalDetailSave,
            child: Text(
              '保存',
              style: TextUtil.base
                  .sp(16)
                  .semiBold
                  .withColor(widget.cardColor),
            ),
          ),
        ],
      ),
    );
  }

  void _handlePrenatalDetailSave() {
    // TODO: 实现产检详情保存逻辑
    Navigator.of(context).pop();
  }

  Widget _buildContentArea() {
    // 根据选中的分类显示不同的表单
    if (selectedCategoryIndex == 0) {
      return PregnancyCheckForm(
        cardColor: widget.cardColor,
        cardBgColor: widget.cardBgColor,
        isDetailMode: false,
      );
    } else if (selectedCategoryIndex == 1) {
      return EventForm(
        cardColor: widget.cardColor,
        cardBgColor: widget.cardBgColor,
      );
    } else if (selectedCategoryIndex == 2) {
      return TaskForm(
        cardColor: widget.cardColor,
        cardBgColor: widget.cardBgColor,
      );
    }

    // 默认返回空容器
    return Container();
  }
}
