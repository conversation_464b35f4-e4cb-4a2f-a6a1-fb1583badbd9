openapi: 3.0.3
info:
  title: Align Forum API
  version: 1.0.0
  description: 论坛系统后端API文档
paths:
  /api/auth/login/:
    post:
      operationId: auth_login_create
      description: 使用邮箱和密码进行登录，返回JWT Token
      summary: 邮箱密码登录
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomTokenObtainPairRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CustomTokenObtainPairRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CustomTokenObtainPairRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  refresh:
                    type: string
                    description: 刷新Token
                  access:
                    type: string
                    description: 访问Token
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      email:
                        type: string
                      username:
                        type: string
                      nickname:
                        type: string
                      avatar_url:
                        type: string
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: 登录失败
          description: ''
  /api/auth/login/email-code/:
    post:
      operationId: auth_login_email_code_create
      description: 使用邮箱验证码进行登录
      summary: 邮箱验证码登录
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailCodeLoginRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailCodeLoginRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailCodeLoginRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  refresh:
                    type: string
                    description: 刷新Token
                  access:
                    type: string
                    description: 访问Token
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      email:
                        type: string
                      username:
                        type: string
                      nickname:
                        type: string
                      avatar_url:
                        type: string
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: 验证码错误或已过期
          description: ''
  /api/auth/logout/:
    post:
      operationId: auth_logout_create
      description: 登出当前用户，将刷新Token加入黑名单
      summary: 用户登出
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LogoutRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LogoutRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LogoutRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                description: 登出成功
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: Token无效
          description: ''
  /api/auth/refresh/:
    post:
      operationId: auth_refresh_create
      description: |-
        Takes a refresh type JSON web token and returns an access type JSON web
        token if the refresh token is valid.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenRefresh'
          description: ''
  /api/auth/send-code/:
    post:
      operationId: auth_send_code_create
      description: 向指定邮箱发送验证码，支持注册、登录、重置密码等场景
      summary: 发送邮箱验证码
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SendVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SendVerificationCodeRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                description: 验证码发送成功
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: 请求参数错误
          description: ''
  /api/auth/verify/:
    get:
      operationId: auth_verify_retrieve
      description: 验证当前用户的Token是否有效
      summary: 验证Token有效性
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                    description: Token是否有效
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      email:
                        type: string
                      username:
                        type: string
                      nickname:
                        type: string
          description: ''
        '401':
          content:
            application/json:
              schema:
                description: Token无效或已过期
          description: ''
  /api/calendar/checkups/:
    get:
      operationId: calendar_checkups_list
      description: 产检记录视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPrenatalCheckupListList'
          description: ''
    post:
      operationId: calendar_checkups_create
      description: 产检记录视图集
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
  /api/calendar/checkups/{id}/:
    get:
      operationId: calendar_checkups_retrieve
      description: 产检记录视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
    put:
      operationId: calendar_checkups_update
      description: 产检记录视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
    patch:
      operationId: calendar_checkups_partial_update
      description: 产检记录视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPrenatalCheckupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPrenatalCheckupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPrenatalCheckupRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
    delete:
      operationId: calendar_checkups_destroy
      description: 产检记录视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/calendar/checkups/{id}/add_image/:
    post:
      operationId: calendar_checkups_add_image_create
      description: 添加产检图片（通过图片ID）
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
  /api/calendar/checkups/{id}/images/:
    get:
      operationId: calendar_checkups_images_retrieve
      description: 获取产检记录的所有图片
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
  /api/calendar/checkups/{id}/remove_image/:
    post:
      operationId: calendar_checkups_remove_image_create
      description: 移除产检图片
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PrenatalCheckupRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
  /api/calendar/checkups/past_due/:
    get:
      operationId: calendar_checkups_past_due_retrieve
      description: 获取已过期的产检
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
  /api/calendar/checkups/upcoming/:
    get:
      operationId: calendar_checkups_upcoming_retrieve
      description: 获取即将到来的产检
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalCheckup'
          description: ''
  /api/calendar/custom-items/:
    get:
      operationId: calendar_custom_items_list
      description: 自定义产检项目视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCustomPrenatalItemList'
          description: ''
    post:
      operationId: calendar_custom_items_create
      description: 自定义产检项目视图集
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomPrenatalItemRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CustomPrenatalItemRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CustomPrenatalItemRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomPrenatalItem'
          description: ''
  /api/calendar/custom-items/{id}/:
    get:
      operationId: calendar_custom_items_retrieve
      description: 自定义产检项目视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomPrenatalItem'
          description: ''
    put:
      operationId: calendar_custom_items_update
      description: 自定义产检项目视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomPrenatalItemRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CustomPrenatalItemRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CustomPrenatalItemRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomPrenatalItem'
          description: ''
    patch:
      operationId: calendar_custom_items_partial_update
      description: 自定义产检项目视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCustomPrenatalItemRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCustomPrenatalItemRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCustomPrenatalItemRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomPrenatalItem'
          description: ''
    delete:
      operationId: calendar_custom_items_destroy
      description: 自定义产检项目视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/calendar/images/:
    get:
      operationId: calendar_images_list
      description: 产检图片视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPrenatalImageListList'
          description: ''
    post:
      operationId: calendar_images_create
      description: 产检图片视图集
      tags:
      - calendar
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PrenatalImageUploadRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PrenatalImageUploadRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImageUpload'
          description: ''
  /api/calendar/images/{id}/:
    get:
      operationId: calendar_images_retrieve
      description: 产检图片视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImage'
          description: ''
    put:
      operationId: calendar_images_update
      description: 产检图片视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PrenatalImageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PrenatalImageRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImage'
          description: ''
    patch:
      operationId: calendar_images_partial_update
      description: 产检图片视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPrenatalImageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPrenatalImageRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImage'
          description: ''
    delete:
      operationId: calendar_images_destroy
      description: 产检图片视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/calendar/images/batch_upload/:
    post:
      operationId: calendar_images_batch_upload_create
      description: 批量上传图片
      tags:
      - calendar
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PrenatalImageRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PrenatalImageRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImage'
          description: ''
  /api/calendar/images/by_checkup/:
    get:
      operationId: calendar_images_by_checkup_retrieve
      description: 按产检记录获取图片
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImage'
          description: ''
  /api/calendar/images/by_type/:
    get:
      operationId: calendar_images_by_type_retrieve
      description: 按类型获取图片
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImage'
          description: ''
  /api/calendar/images/quick-upload/:
    post:
      operationId: calendar_images_quick_upload_create
      description: 快速上传单张图片
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/calendar/images/recent/:
    get:
      operationId: calendar_images_recent_retrieve
      description: 获取最近上传的图片
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImage'
          description: ''
  /api/calendar/images/statistics/:
    get:
      operationId: calendar_images_statistics_retrieve
      description: 获取图片统计信息
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalImage'
          description: ''
  /api/calendar/images/types/:
    get:
      operationId: calendar_images_types_retrieve
      description: 获取图片类型选项
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/calendar/items/:
    get:
      operationId: calendar_items_list
      description: 标准产检项目视图集（只读）
      parameters:
      - in: query
        name: end_week
        schema:
          type: integer
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      - in: query
        name: start_week
        schema:
          type: integer
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPrenatalItemList'
          description: ''
  /api/calendar/items/{id}/:
    get:
      operationId: calendar_items_retrieve
      description: 标准产检项目视图集（只读）
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 标准产检项目.
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalItem'
          description: ''
  /api/calendar/items/by_week/:
    get:
      operationId: calendar_items_by_week_retrieve
      description: 根据孕周获取适用的产检项目
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrenatalItem'
          description: ''
  /api/calendar/tasks/:
    get:
      operationId: calendar_tasks_list
      description: 任务管理视图集 - 支持自定义任务和吃药提醒
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTaskListList'
          description: ''
    post:
      operationId: calendar_tasks_create
      description: 任务管理视图集 - 支持自定义任务和吃药提醒
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TaskRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/calendar/tasks/{id}/:
    get:
      operationId: calendar_tasks_retrieve
      description: 任务管理视图集 - 支持自定义任务和吃药提醒
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
    put:
      operationId: calendar_tasks_update
      description: 任务管理视图集 - 支持自定义任务和吃药提醒
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TaskRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
    patch:
      operationId: calendar_tasks_partial_update
      description: 任务管理视图集 - 支持自定义任务和吃药提醒
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedTaskRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedTaskRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedTaskRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
    delete:
      operationId: calendar_tasks_destroy
      description: 任务管理视图集 - 支持自定义任务和吃药提醒
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/calendar/tasks/by_type/:
    get:
      operationId: calendar_tasks_by_type_retrieve
      description: 按任务类型获取任务
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/calendar/tasks/ongoing/:
    get:
      operationId: calendar_tasks_ongoing_retrieve
      description: 获取正在进行中的任务（仅适用于自定义任务）
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/calendar/tasks/past/:
    get:
      operationId: calendar_tasks_past_retrieve
      description: 获取已过期的任务
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/calendar/tasks/today_medications/:
    get:
      operationId: calendar_tasks_today_medications_retrieve
      description: 获取今天的吃药提醒
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/calendar/tasks/upcoming/:
    get:
      operationId: calendar_tasks_upcoming_retrieve
      description: 获取即将到来的任务
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/calendar/tasks/weekly_schedule/:
    get:
      operationId: calendar_tasks_weekly_schedule_retrieve
      description: 获取本周的任务安排
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/calendar/upload-sessions/:
    get:
      operationId: calendar_upload_sessions_list
      description: 图片上传会话视图集
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedImageUploadSessionList'
          description: ''
  /api/calendar/upload-sessions/{id}/:
    get:
      operationId: calendar_upload_sessions_retrieve
      description: 图片上传会话视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageUploadSession'
          description: ''
  /api/calendar/upload-sessions/{id}/images/:
    get:
      operationId: calendar_upload_sessions_images_retrieve
      description: 获取上传会话中的所有图片
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageUploadSession'
          description: ''
  /api/calendar/upload-sessions/active/:
    get:
      operationId: calendar_upload_sessions_active_retrieve
      description: 获取活跃的上传会话
      tags:
      - calendar
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageUploadSession'
          description: ''
  /api/login-logs/:
    get:
      operationId: login_logs_list
      description: 获取用户登录日志记录
      summary: 获取登录日志
      parameters:
      - in: query
        name: email
        schema:
          type: string
      - in: query
        name: login_type
        schema:
          type: string
          enum:
          - email_code
          - password
          - phone_code
          - social
        description: 登录方式过滤
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: status
        schema:
          type: string
          enum:
          - blocked
          - failed
          - success
        description: 登录状态过滤
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedLoginLogList'
          description: ''
  /api/login-logs/{id}/:
    get:
      operationId: login_logs_retrieve
      description: 登录日志视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 登录日志.
        required: true
      tags:
      - login-logs
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginLog'
          description: ''
  /api/users/:
    get:
      operationId: users_list
      description: 获取系统中的用户列表，支持搜索和过滤
      summary: 获取用户列表
      parameters:
      - in: query
        name: is_active
        schema:
          type: boolean
        description: 过滤活跃用户
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: profile__is_email_verified
        schema:
          type: boolean
      - in: query
        name: profile__is_phone_verified
        schema:
          type: boolean
      - in: query
        name: search
        schema:
          type: string
        description: 搜索用户名、邮箱或昵称
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: users_create
      description: 注册新用户账户
      summary: 创建用户
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserCreate'
          description: ''
  /api/users/{id}/:
    get:
      operationId: users_retrieve
      description: 根据用户ID获取用户详细信息
      summary: 获取用户详情
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户账户.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetail'
          description: ''
    put:
      operationId: users_update
      description: 更新用户的基本信息
      summary: 更新用户信息
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户账户.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    patch:
      operationId: users_partial_update
      description: 部分更新用户的基本信息
      summary: 部分更新用户信息
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户账户.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    delete:
      operationId: users_destroy
      description: 删除用户账户（软删除）
      summary: 删除用户
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户账户.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/users/{id}/change_password/:
    post:
      operationId: users_change_password_create
      description: 修改当前用户的密码
      summary: 修改密码
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户账户.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                description: 密码修改成功
          description: ''
  /api/users/{id}/posts/:
    get:
      operationId: users_posts_retrieve
      description: 获取指定用户发布的帖子列表
      summary: 获取用户的帖子
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户账户.
        required: true
      - in: query
        name: status
        schema:
          type: string
          enum:
          - deleted
          - draft
          - hidden
          - published
        description: 帖子状态过滤
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetail'
          description: ''
  /api/users/{id}/stats/:
    get:
      operationId: users_stats_retrieve
      description: 获取用户的发帖、评论、点赞等统计信息
      summary: 获取用户统计信息
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户账户.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  post_count:
                    type: integer
                    description: 发帖数
                  comment_count:
                    type: integer
                    description: 评论数
                  like_count:
                    type: integer
                    description: 获赞数
                  login_count:
                    type: integer
                    description: 登录次数
                  last_login:
                    type: string
                    format: date-time
                    description: 最后登录时间
          description: ''
  /api/verification-codes/:
    get:
      operationId: verification_codes_list
      description: 获取邮箱验证码记录（管理员功能）
      summary: 获取邮箱验证码列表
      parameters:
      - in: query
        name: code_type
        schema:
          type: string
          title: 验证码类型
          enum:
          - change_email
          - login
          - register
          - reset_password
        description: |-
          * `register` - 注册验证
          * `login` - 登录验证
          * `reset_password` - 重置密码
          * `change_email` - 更换邮箱
      - in: query
        name: email
        schema:
          type: string
      - in: query
        name: is_used
        schema:
          type: boolean
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedEmailVerificationCodeList'
          description: ''
    post:
      operationId: verification_codes_create
      description: 向指定邮箱发送验证码
      summary: 发送邮箱验证码
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationCode'
          description: ''
  /api/verification-codes/{id}/:
    get:
      operationId: verification_codes_retrieve
      description: 邮箱验证码管理视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 邮箱验证码.
        required: true
      tags:
      - verification-codes
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationCode'
          description: ''
    put:
      operationId: verification_codes_update
      description: 邮箱验证码管理视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 邮箱验证码.
        required: true
      tags:
      - verification-codes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationCode'
          description: ''
    patch:
      operationId: verification_codes_partial_update
      description: 邮箱验证码管理视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 邮箱验证码.
        required: true
      tags:
      - verification-codes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmailVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmailVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmailVerificationCodeRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationCode'
          description: ''
    delete:
      operationId: verification_codes_destroy
      description: 邮箱验证码管理视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 邮箱验证码.
        required: true
      tags:
      - verification-codes
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
components:
  schemas:
    BlankEnum:
      enum:
      - ''
    ChangePasswordRequest:
      type: object
      description: 修改密码序列化器
      properties:
        old_password:
          type: string
          minLength: 1
        new_password:
          type: string
          minLength: 1
        new_password_confirm:
          type: string
          minLength: 1
      required:
      - new_password
      - new_password_confirm
      - old_password
    CodeTypeEnum:
      enum:
      - register
      - login
      - reset_password
      - change_email
      type: string
      description: |-
        * `register` - 注册验证
        * `login` - 登录验证
        * `reset_password` - 重置密码
        * `change_email` - 更换邮箱
    CustomPrenatalItem:
      type: object
      description: 自定义产检项目序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 项目名称
          maxLength: 100
        content:
          type: string
          title: 项目详细内容
          description: 详细描述产检项目的内容和注意事项
        start_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用开始孕周
          description: 适用的开始孕周（1-42周）
        end_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用结束孕周
          description: 适用的结束孕周（1-42周）
        created_by:
          type: integer
          title: 创建者
          description: 创建该自定义项目的用户
        created_by_email:
          type: string
          readOnly: true
        week_range:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - created_by
      - created_by_email
      - id
      - name
      - updated_at
      - week_range
    CustomPrenatalItemRequest:
      type: object
      description: 自定义产检项目序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 项目名称
          maxLength: 100
        content:
          type: string
          title: 项目详细内容
          description: 详细描述产检项目的内容和注意事项
        start_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用开始孕周
          description: 适用的开始孕周（1-42周）
        end_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用结束孕周
          description: 适用的结束孕周（1-42周）
        created_by:
          type: integer
          title: 创建者
          description: 创建该自定义项目的用户
      required:
      - created_by
      - name
    CustomTokenObtainPairRequest:
      type: object
      description: 自定义JWT Token获取序列化器
      properties:
        email:
          type: string
          writeOnly: true
          minLength: 1
        password:
          type: string
          writeOnly: true
          minLength: 1
      required:
      - email
      - password
    DosageUnitEnum:
      enum:
      - tablet
      - capsule
      - ml
      - mg
      - g
      - drop
      - spoon
      - other
      type: string
      description: |-
        * `tablet` - 片
        * `capsule` - 粒
        * `ml` - 毫升
        * `mg` - 毫克
        * `g` - 克
        * `drop` - 滴
        * `spoon` - 勺
        * `other` - 其他
    EmailCodeLoginRequest:
      type: object
      description: 邮箱验证码登录序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
        code:
          type: string
          minLength: 1
          maxLength: 6
      required:
      - code
      - email
    EmailVerificationCode:
      type: object
      description: 邮箱验证码序列化器
      properties:
        email:
          type: string
          format: email
          title: 邮箱地址
          maxLength: 254
        code_type:
          allOf:
          - $ref: '#/components/schemas/CodeTypeEnum'
          title: 验证码类型
      required:
      - code_type
      - email
    EmailVerificationCodeRequest:
      type: object
      description: 邮箱验证码序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
          title: 邮箱地址
          maxLength: 254
        code_type:
          allOf:
          - $ref: '#/components/schemas/CodeTypeEnum'
          title: 验证码类型
      required:
      - code_type
      - email
    ImageTypeEnum:
      enum:
      - checkup_report
      - ultrasound
      - blood_test
      - urine_test
      - other
      type: string
      description: |-
        * `checkup_report` - 产检报告
        * `ultrasound` - B超单
        * `blood_test` - 血检报告
        * `urine_test` - 尿检报告
        * `other` - 其他
    ImageUploadSession:
      type: object
      description: 图片上传会话序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          title: 用户
        user_email:
          type: string
          readOnly: true
        session_name:
          type: string
          title: 会话名称
          maxLength: 100
        total_files:
          type: integer
          maximum: 4294967295
          minimum: 0
          format: int64
          title: 总文件数
        uploaded_files:
          type: integer
          readOnly: true
          title: 已上传文件数
        failed_files:
          type: integer
          readOnly: true
          title: 失败文件数
        status:
          allOf:
          - $ref: '#/components/schemas/ImageUploadSessionStatusEnum'
          title: 状态
        progress_percentage:
          type: string
          readOnly: true
        is_completed:
          type: string
          readOnly: true
        checkup:
          type: string
          format: uuid
          nullable: true
          title: 关联产检记录
        checkup_datetime:
          type: string
          format: date-time
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        completed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 完成时间
      required:
      - checkup_datetime
      - completed_at
      - created_at
      - failed_files
      - id
      - is_completed
      - progress_percentage
      - updated_at
      - uploaded_files
      - user
      - user_email
    ImageUploadSessionStatusEnum:
      enum:
      - pending
      - uploading
      - completed
      - failed
      type: string
      description: |-
        * `pending` - 等待中
        * `uploading` - 上传中
        * `completed` - 已完成
        * `failed` - 失败
    LoginLog:
      type: object
      description: 登录日志序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          nullable: true
          title: 用户
        user_display:
          type: string
          readOnly: true
        email:
          type: string
          format: email
          title: 登录邮箱
          maxLength: 254
        login_type:
          allOf:
          - $ref: '#/components/schemas/LoginTypeEnum'
          title: 登录方式
        status:
          allOf:
          - $ref: '#/components/schemas/LoginLogStatusEnum'
          title: 登录状态
        ip_address:
          type: string
          title: IP地址
        location:
          type: string
          title: 登录地点
          maxLength: 100
        failure_reason:
          type: string
          title: 失败原因
          maxLength: 200
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 登录时间
      required:
      - created_at
      - email
      - id
      - ip_address
      - login_type
      - status
      - user_display
    LoginLogStatusEnum:
      enum:
      - success
      - failed
      - blocked
      type: string
      description: |-
        * `success` - 成功
        * `failed` - 失败
        * `blocked` - 被阻止
    LoginTypeEnum:
      enum:
      - password
      - email_code
      - phone_code
      - social
      type: string
      description: |-
        * `password` - 密码登录
        * `email_code` - 邮箱验证码登录
        * `phone_code` - 手机验证码登录
        * `social` - 第三方登录
    LogoutRequest:
      type: object
      description: 登出序列化器
      properties:
        refresh:
          type: string
          minLength: 1
      required:
      - refresh
    PaginatedCustomPrenatalItemList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/CustomPrenatalItem'
    PaginatedEmailVerificationCodeList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/EmailVerificationCode'
    PaginatedImageUploadSessionList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ImageUploadSession'
    PaginatedLoginLogList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/LoginLog'
    PaginatedPrenatalCheckupListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PrenatalCheckupList'
    PaginatedPrenatalImageListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PrenatalImageList'
    PaginatedPrenatalItemList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PrenatalItem'
    PaginatedTaskListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TaskList'
    PaginatedUserList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/User'
    PatchedCustomPrenatalItemRequest:
      type: object
      description: 自定义产检项目序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 项目名称
          maxLength: 100
        content:
          type: string
          title: 项目详细内容
          description: 详细描述产检项目的内容和注意事项
        start_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用开始孕周
          description: 适用的开始孕周（1-42周）
        end_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用结束孕周
          description: 适用的结束孕周（1-42周）
        created_by:
          type: integer
          title: 创建者
          description: 创建该自定义项目的用户
    PatchedEmailVerificationCodeRequest:
      type: object
      description: 邮箱验证码序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
          title: 邮箱地址
          maxLength: 254
        code_type:
          allOf:
          - $ref: '#/components/schemas/CodeTypeEnum'
          title: 验证码类型
    PatchedPrenatalCheckupRequest:
      type: object
      description: 产检记录序列化器
      properties:
        user:
          type: integer
          title: 用户
          description: 进行产检的用户
        date:
          type: string
          format: date
          title: 产检日期
          description: 预约的产检日期
        time:
          type: string
          format: time
          title: 产检时间
          description: 预约的产检时间（小时和分钟）
        location:
          type: string
          minLength: 1
          title: 产检地点
          description: 医院或诊所名称及地址
          maxLength: 200
        prenatal_items:
          type: array
          items:
            type: string
            format: uuid
            title: 标准产检项目
          title: 标准产检项目
          description: 选择的标准产检项目
        custom_prenatal_items:
          type: array
          items:
            type: string
            format: uuid
            title: 自定义产检项目
          title: 自定义产检项目
          description: 选择的自定义产检项目
        travel_time:
          type: string
          format: time
          nullable: true
          title: 出行时间
          description: 计划出发时间
        reminder_time:
          type: string
          format: time
          nullable: true
          title: 提醒时间
          description: 提醒时间
        preparation_notes:
          type: string
          title: 产检准备笔记
          description: 产检前的准备事项、注意事项等
        checkup_notes:
          type: string
          title: 产检笔记
          description: 产检过程中的记录、医生建议等
        checkup_images:
          title: 产检单图片
          description: 产检单、B超单等图片的URL列表
    PatchedPrenatalImageRequest:
      type: object
      description: 产检图片序列化器
      properties:
        user:
          type: integer
          title: 用户
          description: 上传图片的用户
        image:
          type: string
          format: binary
          title: 图片文件
          description: 支持的格式：jpg, jpeg, png, gif, bmp, webp
          pattern: (?:jpg|jpeg|png|gif|bmp|webp)$
        image_type:
          allOf:
          - $ref: '#/components/schemas/ImageTypeEnum'
          title: 图片类型
          description: |-
            图片类型分类

            * `checkup_report` - 产检报告
            * `ultrasound` - B超单
            * `blood_test` - 血检报告
            * `urine_test` - 尿检报告
            * `other` - 其他
        title:
          type: string
          title: 图片标题
          description: 图片的简短描述
          maxLength: 100
        description:
          type: string
          title: 图片描述
          description: 详细描述图片内容
        checkup:
          type: string
          format: uuid
          nullable: true
          title: 关联产检记录
          description: 可选：关联到具体的产检记录
    PatchedTaskRequest:
      type: object
      description: 任务序列化器 - 支持自定义任务和吃药提醒
      properties:
        task_type:
          allOf:
          - $ref: '#/components/schemas/TaskTypeEnum'
          title: 任务类型
          description: |-
            任务类型：自定义任务或吃药提醒

            * `custom` - 自定义任务
            * `medication` - 吃药提醒
        title:
          type: string
          title: 任务标题
          description: 自定义任务的标题（自定义任务必填）
          maxLength: 200
        is_all_day:
          type: boolean
          title: 是否全天
          description: 是否为全天任务
        start_time:
          type: string
          format: date-time
          nullable: true
          title: 开始时间
          description: 任务开始时间
        end_time:
          type: string
          format: date-time
          nullable: true
          title: 结束时间
          description: 任务结束时间
        location:
          type: string
          title: 地点
          description: 任务地点
          maxLength: 300
        travel_time:
          type: string
          nullable: true
          title: 出行时间
          description: 到达地点所需的出行时间
        reminder_settings:
          title: 提醒设置
          description: '提醒时间设置，格式：[{"minutes": 15, "enabled": true}, {"minutes": 60,
            "enabled": false}]'
        notes:
          type: string
          title: 备注
          description: 任务备注信息
        medicine_name:
          type: string
          title: 药物名称
          description: 药物名称（吃药提醒必填）
          maxLength: 200
        dosage_amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: 药物数量
          description: 药物数量
        dosage_unit:
          title: 药物单位
          description: |-
            药物单位

            * `tablet` - 片
            * `capsule` - 粒
            * `ml` - 毫升
            * `mg` - 毫克
            * `g` - 克
            * `drop` - 滴
            * `spoon` - 勺
            * `other` - 其他
          oneOf:
          - $ref: '#/components/schemas/DosageUnitEnum'
          - $ref: '#/components/schemas/BlankEnum'
        medication_time:
          type: string
          format: time
          nullable: true
          title: 服药时间
          description: 每日服药时间
        reminder_advance_time:
          type: string
          nullable: true
          title: 提前提醒时间
          description: 提前多长时间提醒服药
        weekly_schedule:
          title: 周期设置
          description: 周一到周日的选择，格式：[1,2,3,4,5,6,7] 表示周一到周日
    PatchedUserUpdateRequest:
      type: object
      description: 用户信息更新序列化器
      properties:
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 150
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: binary
          nullable: true
          title: 头像
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        phone:
          type: string
          title: 手机号
          maxLength: 20
        expected_delivery_date:
          type: string
          format: date
          nullable: true
          title: 预产期
          description: 预计分娩日期
        receive_email_notifications:
          type: boolean
          title: 接收邮件通知
        receive_sms_notifications:
          type: boolean
          title: 接收短信通知
    PrenatalCheckup:
      type: object
      description: 产检记录序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          title: 用户
          description: 进行产检的用户
        user_email:
          type: string
          readOnly: true
        date:
          type: string
          format: date
          title: 产检日期
          description: 预约的产检日期
        time:
          type: string
          format: time
          title: 产检时间
          description: 预约的产检时间（小时和分钟）
        location:
          type: string
          title: 产检地点
          description: 医院或诊所名称及地址
          maxLength: 200
        prenatal_items:
          type: array
          items:
            type: string
            format: uuid
            title: 标准产检项目
          title: 标准产检项目
          description: 选择的标准产检项目
        custom_prenatal_items:
          type: array
          items:
            type: string
            format: uuid
            title: 自定义产检项目
          title: 自定义产检项目
          description: 选择的自定义产检项目
        prenatal_items_detail:
          type: array
          items:
            $ref: '#/components/schemas/PrenatalItem'
          readOnly: true
        custom_prenatal_items_detail:
          type: array
          items:
            $ref: '#/components/schemas/CustomPrenatalItem'
          readOnly: true
        travel_time:
          type: string
          format: time
          nullable: true
          title: 出行时间
          description: 计划出发时间
        reminder_time:
          type: string
          format: time
          nullable: true
          title: 提醒时间
          description: 提醒时间
        preparation_notes:
          type: string
          title: 产检准备笔记
          description: 产检前的准备事项、注意事项等
        checkup_notes:
          type: string
          title: 产检笔记
          description: 产检过程中的记录、医生建议等
        checkup_images:
          title: 产检单图片
          description: 产检单、B超单等图片的URL列表
        items_count:
          type: string
          readOnly: true
        is_upcoming:
          type: string
          readOnly: true
        is_past_due:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - custom_prenatal_items_detail
      - date
      - id
      - is_past_due
      - is_upcoming
      - items_count
      - location
      - prenatal_items_detail
      - time
      - updated_at
      - user
      - user_email
    PrenatalCheckupList:
      type: object
      description: 产检记录列表序列化器（简化版）
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        date:
          type: string
          format: date
          title: 产检日期
          description: 预约的产检日期
        time:
          type: string
          format: time
          title: 产检时间
          description: 预约的产检时间（小时和分钟）
        location:
          type: string
          title: 产检地点
          description: 医院或诊所名称及地址
          maxLength: 200
        items_count:
          type: string
          readOnly: true
        is_upcoming:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
      required:
      - created_at
      - date
      - id
      - is_upcoming
      - items_count
      - location
      - time
    PrenatalCheckupRequest:
      type: object
      description: 产检记录序列化器
      properties:
        user:
          type: integer
          title: 用户
          description: 进行产检的用户
        date:
          type: string
          format: date
          title: 产检日期
          description: 预约的产检日期
        time:
          type: string
          format: time
          title: 产检时间
          description: 预约的产检时间（小时和分钟）
        location:
          type: string
          minLength: 1
          title: 产检地点
          description: 医院或诊所名称及地址
          maxLength: 200
        prenatal_items:
          type: array
          items:
            type: string
            format: uuid
            title: 标准产检项目
          title: 标准产检项目
          description: 选择的标准产检项目
        custom_prenatal_items:
          type: array
          items:
            type: string
            format: uuid
            title: 自定义产检项目
          title: 自定义产检项目
          description: 选择的自定义产检项目
        travel_time:
          type: string
          format: time
          nullable: true
          title: 出行时间
          description: 计划出发时间
        reminder_time:
          type: string
          format: time
          nullable: true
          title: 提醒时间
          description: 提醒时间
        preparation_notes:
          type: string
          title: 产检准备笔记
          description: 产检前的准备事项、注意事项等
        checkup_notes:
          type: string
          title: 产检笔记
          description: 产检过程中的记录、医生建议等
        checkup_images:
          title: 产检单图片
          description: 产检单、B超单等图片的URL列表
      required:
      - date
      - location
      - time
      - user
    PrenatalImage:
      type: object
      description: 产检图片序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          title: 用户
          description: 上传图片的用户
        user_email:
          type: string
          readOnly: true
        image:
          type: string
          format: uri
          title: 图片文件
          description: 支持的格式：jpg, jpeg, png, gif, bmp, webp
          pattern: (?:jpg|jpeg|png|gif|bmp|webp)$
        image_type:
          allOf:
          - $ref: '#/components/schemas/ImageTypeEnum'
          title: 图片类型
          description: |-
            图片类型分类

            * `checkup_report` - 产检报告
            * `ultrasound` - B超单
            * `blood_test` - 血检报告
            * `urine_test` - 尿检报告
            * `other` - 其他
        title:
          type: string
          title: 图片标题
          description: 图片的简短描述
          maxLength: 100
        description:
          type: string
          title: 图片描述
          description: 详细描述图片内容
        file_size:
          type: integer
          readOnly: true
          title: 文件大小
          description: 文件大小（字节）
        file_size_human:
          type: string
          readOnly: true
        width:
          type: integer
          readOnly: true
          title: 图片宽度
          description: 图片宽度（像素）
        height:
          type: integer
          readOnly: true
          title: 图片高度
          description: 图片高度（像素）
        image_url:
          type: string
          readOnly: true
        thumbnail_url:
          type: string
          readOnly: true
        checkup:
          type: string
          format: uuid
          nullable: true
          title: 关联产检记录
          description: 可选：关联到具体的产检记录
        checkup_datetime:
          type: string
          format: date-time
          readOnly: true
        checkup_location:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 上传时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - checkup_datetime
      - checkup_location
      - created_at
      - file_size
      - file_size_human
      - height
      - id
      - image
      - image_url
      - thumbnail_url
      - updated_at
      - user
      - user_email
      - width
    PrenatalImageList:
      type: object
      description: 产检图片列表序列化器（简化版）
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        image_type:
          allOf:
          - $ref: '#/components/schemas/ImageTypeEnum'
          title: 图片类型
          description: |-
            图片类型分类

            * `checkup_report` - 产检报告
            * `ultrasound` - B超单
            * `blood_test` - 血检报告
            * `urine_test` - 尿检报告
            * `other` - 其他
        title:
          type: string
          title: 图片标题
          description: 图片的简短描述
          maxLength: 100
        file_size_human:
          type: string
          readOnly: true
        image_url:
          type: string
          readOnly: true
        thumbnail_url:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 上传时间
      required:
      - created_at
      - file_size_human
      - id
      - image_url
      - thumbnail_url
    PrenatalImageRequest:
      type: object
      description: 产检图片序列化器
      properties:
        user:
          type: integer
          title: 用户
          description: 上传图片的用户
        image:
          type: string
          format: binary
          title: 图片文件
          description: 支持的格式：jpg, jpeg, png, gif, bmp, webp
          pattern: (?:jpg|jpeg|png|gif|bmp|webp)$
        image_type:
          allOf:
          - $ref: '#/components/schemas/ImageTypeEnum'
          title: 图片类型
          description: |-
            图片类型分类

            * `checkup_report` - 产检报告
            * `ultrasound` - B超单
            * `blood_test` - 血检报告
            * `urine_test` - 尿检报告
            * `other` - 其他
        title:
          type: string
          title: 图片标题
          description: 图片的简短描述
          maxLength: 100
        description:
          type: string
          title: 图片描述
          description: 详细描述图片内容
        checkup:
          type: string
          format: uuid
          nullable: true
          title: 关联产检记录
          description: 可选：关联到具体的产检记录
      required:
      - image
      - user
    PrenatalImageUpload:
      type: object
      description: 产检图片上传序列化器（简化版）
      properties:
        image:
          type: string
          format: uri
          title: 图片文件
          description: 支持的格式：jpg, jpeg, png, gif, bmp, webp
          pattern: (?:jpg|jpeg|png|gif|bmp|webp)$
        image_type:
          allOf:
          - $ref: '#/components/schemas/ImageTypeEnum'
          title: 图片类型
          description: |-
            图片类型分类

            * `checkup_report` - 产检报告
            * `ultrasound` - B超单
            * `blood_test` - 血检报告
            * `urine_test` - 尿检报告
            * `other` - 其他
        title:
          type: string
          title: 图片标题
          description: 图片的简短描述
          maxLength: 100
        description:
          type: string
          title: 图片描述
          description: 详细描述图片内容
        checkup:
          type: string
          format: uuid
          nullable: true
          title: 关联产检记录
          description: 可选：关联到具体的产检记录
      required:
      - image
    PrenatalImageUploadRequest:
      type: object
      description: 产检图片上传序列化器（简化版）
      properties:
        image:
          type: string
          format: binary
          title: 图片文件
          description: 支持的格式：jpg, jpeg, png, gif, bmp, webp
          pattern: (?:jpg|jpeg|png|gif|bmp|webp)$
        image_type:
          allOf:
          - $ref: '#/components/schemas/ImageTypeEnum'
          title: 图片类型
          description: |-
            图片类型分类

            * `checkup_report` - 产检报告
            * `ultrasound` - B超单
            * `blood_test` - 血检报告
            * `urine_test` - 尿检报告
            * `other` - 其他
        title:
          type: string
          title: 图片标题
          description: 图片的简短描述
          maxLength: 100
        description:
          type: string
          title: 图片描述
          description: 详细描述图片内容
        checkup:
          type: string
          format: uuid
          nullable: true
          title: 关联产检记录
          description: 可选：关联到具体的产检记录
      required:
      - image
    PrenatalItem:
      type: object
      description: 标准产检项目序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 项目名称
          maxLength: 100
        content:
          type: string
          title: 项目详细内容
          description: 详细描述产检项目的内容和注意事项
        start_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用开始孕周
          description: 适用的开始孕周（1-42周）
        end_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用结束孕周
          description: 适用的结束孕周（1-42周）
        week_range:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - id
      - name
      - updated_at
      - week_range
    PrenatalItemRequest:
      type: object
      description: 标准产检项目序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 项目名称
          maxLength: 100
        content:
          type: string
          title: 项目详细内容
          description: 详细描述产检项目的内容和注意事项
        start_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用开始孕周
          description: 适用的开始孕周（1-42周）
        end_week:
          type: integer
          maximum: 42
          minimum: 1
          nullable: true
          title: 适用结束孕周
          description: 适用的结束孕周（1-42周）
      required:
      - name
    SendVerificationCodeRequest:
      type: object
      description: 发送验证码序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
        code_type:
          $ref: '#/components/schemas/CodeTypeEnum'
      required:
      - code_type
      - email
    Task:
      type: object
      description: 任务序列化器 - 支持自定义任务和吃药提醒
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          readOnly: true
          title: 用户
          description: 创建该任务的用户
        user_email:
          type: string
          readOnly: true
        task_type:
          allOf:
          - $ref: '#/components/schemas/TaskTypeEnum'
          title: 任务类型
          description: |-
            任务类型：自定义任务或吃药提醒

            * `custom` - 自定义任务
            * `medication` - 吃药提醒
        task_type_display:
          type: string
          readOnly: true
        title:
          type: string
          title: 任务标题
          description: 自定义任务的标题（自定义任务必填）
          maxLength: 200
        is_all_day:
          type: boolean
          title: 是否全天
          description: 是否为全天任务
        start_time:
          type: string
          format: date-time
          nullable: true
          title: 开始时间
          description: 任务开始时间
        end_time:
          type: string
          format: date-time
          nullable: true
          title: 结束时间
          description: 任务结束时间
        location:
          type: string
          title: 地点
          description: 任务地点
          maxLength: 300
        travel_time:
          type: string
          nullable: true
          title: 出行时间
          description: 到达地点所需的出行时间
        reminder_settings:
          title: 提醒设置
          description: '提醒时间设置，格式：[{"minutes": 15, "enabled": true}, {"minutes": 60,
            "enabled": false}]'
        notes:
          type: string
          title: 备注
          description: 任务备注信息
        medicine_name:
          type: string
          title: 药物名称
          description: 药物名称（吃药提醒必填）
          maxLength: 200
        dosage_amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: 药物数量
          description: 药物数量
        dosage_unit:
          title: 药物单位
          description: |-
            药物单位

            * `tablet` - 片
            * `capsule` - 粒
            * `ml` - 毫升
            * `mg` - 毫克
            * `g` - 克
            * `drop` - 滴
            * `spoon` - 勺
            * `other` - 其他
          oneOf:
          - $ref: '#/components/schemas/DosageUnitEnum'
          - $ref: '#/components/schemas/BlankEnum'
        dosage_unit_display:
          type: string
          readOnly: true
        medication_time:
          type: string
          format: time
          nullable: true
          title: 服药时间
          description: 每日服药时间
        reminder_advance_time:
          type: string
          nullable: true
          title: 提前提醒时间
          description: 提前多长时间提醒服药
        weekly_schedule:
          title: 周期设置
          description: 周一到周日的选择，格式：[1,2,3,4,5,6,7] 表示周一到周日
        weekly_schedule_display:
          type: string
          readOnly: true
        dosage_display:
          type: string
          readOnly: true
        is_upcoming:
          type: string
          readOnly: true
        is_past:
          type: string
          readOnly: true
        is_ongoing:
          type: string
          readOnly: true
        duration:
          type: string
          readOnly: true
        next_reminder_time:
          type: string
          readOnly: true
        departure_time:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - departure_time
      - dosage_display
      - dosage_unit_display
      - duration
      - id
      - is_ongoing
      - is_past
      - is_upcoming
      - next_reminder_time
      - task_type
      - task_type_display
      - updated_at
      - user
      - user_email
      - weekly_schedule_display
    TaskList:
      type: object
      description: 任务列表序列化器（简化版）
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        task_type:
          allOf:
          - $ref: '#/components/schemas/TaskTypeEnum'
          title: 任务类型
          description: |-
            任务类型：自定义任务或吃药提醒

            * `custom` - 自定义任务
            * `medication` - 吃药提醒
        task_type_display:
          type: string
          readOnly: true
        title:
          type: string
          title: 任务标题
          description: 自定义任务的标题（自定义任务必填）
          maxLength: 200
        medicine_name:
          type: string
          title: 药物名称
          description: 药物名称（吃药提醒必填）
          maxLength: 200
        start_time:
          type: string
          format: date-time
          nullable: true
          title: 开始时间
          description: 任务开始时间
        medication_time:
          type: string
          format: time
          nullable: true
          title: 服药时间
          description: 每日服药时间
        weekly_schedule_display:
          type: string
          readOnly: true
        dosage_display:
          type: string
          readOnly: true
        is_upcoming:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
      required:
      - created_at
      - dosage_display
      - id
      - is_upcoming
      - task_type
      - task_type_display
      - weekly_schedule_display
    TaskRequest:
      type: object
      description: 任务序列化器 - 支持自定义任务和吃药提醒
      properties:
        task_type:
          allOf:
          - $ref: '#/components/schemas/TaskTypeEnum'
          title: 任务类型
          description: |-
            任务类型：自定义任务或吃药提醒

            * `custom` - 自定义任务
            * `medication` - 吃药提醒
        title:
          type: string
          title: 任务标题
          description: 自定义任务的标题（自定义任务必填）
          maxLength: 200
        is_all_day:
          type: boolean
          title: 是否全天
          description: 是否为全天任务
        start_time:
          type: string
          format: date-time
          nullable: true
          title: 开始时间
          description: 任务开始时间
        end_time:
          type: string
          format: date-time
          nullable: true
          title: 结束时间
          description: 任务结束时间
        location:
          type: string
          title: 地点
          description: 任务地点
          maxLength: 300
        travel_time:
          type: string
          nullable: true
          title: 出行时间
          description: 到达地点所需的出行时间
        reminder_settings:
          title: 提醒设置
          description: '提醒时间设置，格式：[{"minutes": 15, "enabled": true}, {"minutes": 60,
            "enabled": false}]'
        notes:
          type: string
          title: 备注
          description: 任务备注信息
        medicine_name:
          type: string
          title: 药物名称
          description: 药物名称（吃药提醒必填）
          maxLength: 200
        dosage_amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
          title: 药物数量
          description: 药物数量
        dosage_unit:
          title: 药物单位
          description: |-
            药物单位

            * `tablet` - 片
            * `capsule` - 粒
            * `ml` - 毫升
            * `mg` - 毫克
            * `g` - 克
            * `drop` - 滴
            * `spoon` - 勺
            * `other` - 其他
          oneOf:
          - $ref: '#/components/schemas/DosageUnitEnum'
          - $ref: '#/components/schemas/BlankEnum'
        medication_time:
          type: string
          format: time
          nullable: true
          title: 服药时间
          description: 每日服药时间
        reminder_advance_time:
          type: string
          nullable: true
          title: 提前提醒时间
          description: 提前多长时间提醒服药
        weekly_schedule:
          title: 周期设置
          description: 周一到周日的选择，格式：[1,2,3,4,5,6,7] 表示周一到周日
      required:
      - task_type
    TaskTypeEnum:
      enum:
      - custom
      - medication
      type: string
      description: |-
        * `custom` - 自定义任务
        * `medication` - 吃药提醒
    TokenRefresh:
      type: object
      properties:
        access:
          type: string
          readOnly: true
        refresh:
          type: string
      required:
      - access
      - refresh
    TokenRefreshRequest:
      type: object
      properties:
        refresh:
          type: string
          minLength: 1
      required:
      - refresh
    User:
      type: object
      description: 用户基本信息序列化器
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
          title: 邮箱地址
        username:
          type: string
          readOnly: true
        nickname:
          type: string
          readOnly: true
        avatar_url:
          type: string
          readOnly: true
        display_name:
          type: string
          readOnly: true
        is_active:
          type: boolean
          readOnly: true
          title: 激活状态
        date_joined:
          type: string
          format: date-time
          readOnly: true
          title: 注册时间
        last_login:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 最后登录时间
        profile:
          allOf:
          - $ref: '#/components/schemas/UserProfile'
          readOnly: true
      required:
      - avatar_url
      - date_joined
      - display_name
      - email
      - id
      - is_active
      - last_login
      - nickname
      - profile
      - username
    UserCreate:
      type: object
      description: 用户注册序列化器
      properties:
        email:
          type: string
          format: email
          title: 邮箱地址
          maxLength: 254
      required:
      - email
    UserCreateRequest:
      type: object
      description: 用户注册序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
          title: 邮箱地址
          maxLength: 254
        password:
          type: string
          writeOnly: true
          minLength: 1
        password_confirm:
          type: string
          writeOnly: true
          minLength: 1
        username:
          type: string
          writeOnly: true
          minLength: 1
        nickname:
          type: string
          writeOnly: true
          minLength: 1
      required:
      - email
      - password
      - password_confirm
      - username
    UserDetail:
      type: object
      description: 用户详细信息序列化器
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
          title: 邮箱地址
        username:
          type: string
          readOnly: true
        nickname:
          type: string
          readOnly: true
        avatar_url:
          type: string
          readOnly: true
        display_name:
          type: string
          readOnly: true
        is_active:
          type: boolean
          readOnly: true
          title: 激活状态
        date_joined:
          type: string
          format: date-time
          readOnly: true
          title: 注册时间
        last_login:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 最后登录时间
        profile:
          allOf:
          - $ref: '#/components/schemas/UserProfile'
          readOnly: true
      required:
      - avatar_url
      - date_joined
      - display_name
      - email
      - id
      - is_active
      - last_login
      - nickname
      - profile
      - username
    UserProfile:
      type: object
      description: 用户资料序列化器
      properties:
        username:
          type: string
          title: 用户名
          maxLength: 150
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: uri
          nullable: true
          title: 头像
        avatar_url:
          type: string
          readOnly: true
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        display_name:
          type: string
          readOnly: true
        phone:
          type: string
          title: 手机号
          maxLength: 20
        expected_delivery_date:
          type: string
          format: date
          nullable: true
          title: 预产期
          description: 预计分娩日期
        is_email_verified:
          type: boolean
          readOnly: true
          title: 邮箱已验证
        is_phone_verified:
          type: boolean
          readOnly: true
          title: 手机已验证
        receive_email_notifications:
          type: boolean
          title: 接收邮件通知
        receive_sms_notifications:
          type: boolean
          title: 接收短信通知
        post_count:
          type: integer
          readOnly: true
          title: 发帖数
        comment_count:
          type: integer
          readOnly: true
          title: 评论数
        like_count:
          type: integer
          readOnly: true
          title: 获赞数
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - avatar_url
      - comment_count
      - created_at
      - display_name
      - is_email_verified
      - is_phone_verified
      - like_count
      - post_count
      - updated_at
      - username
    UserUpdate:
      type: object
      description: 用户信息更新序列化器
      properties:
        username:
          type: string
          title: 用户名
          maxLength: 150
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: uri
          nullable: true
          title: 头像
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        phone:
          type: string
          title: 手机号
          maxLength: 20
        expected_delivery_date:
          type: string
          format: date
          nullable: true
          title: 预产期
          description: 预计分娩日期
        receive_email_notifications:
          type: boolean
          title: 接收邮件通知
        receive_sms_notifications:
          type: boolean
          title: 接收短信通知
      required:
      - username
    UserUpdateRequest:
      type: object
      description: 用户信息更新序列化器
      properties:
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 150
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: binary
          nullable: true
          title: 头像
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        phone:
          type: string
          title: 手机号
          maxLength: 20
        expected_delivery_date:
          type: string
          format: date
          nullable: true
          title: 预产期
          description: 预计分娩日期
        receive_email_notifications:
          type: boolean
          title: 接收邮件通知
        receive_sms_notifications:
          type: boolean
          title: 接收短信通知
      required:
      - username
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    jwtAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
servers:
- url: http://localhost:8000
  description: 开发环境
- url: https://api.align-forum.com
  description: 生产环境
tags:
- name: auth
  description: 用户认证相关接口
- name: users
  description: 用户管理相关接口
- name: posts
  description: 帖子相关接口
- name: comments
  description: 评论相关接口
