import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 一个静态类，作为链式调用 TextStyle 的起点。
/// 如无特殊需求，调用顺序为：大小、粗细、颜色、字体
///
/// 用法:
/// ```dart
/// Text('Hello', style: TextUtil.font(16).bold.blue);
/// ```
class TextUtil {
  // 私有构造函数，防止实例化
  TextUtil._();

  /// 链式调用的起点，创建一个带指定字号的 TextStyle。
  static TextStyle _font(double size) {
    return TextStyle(fontSize: size.sp);
  }

  /// 基础样式
  static TextStyle get base => _font(14).regular.black;
}

/// TextStyle 的扩展，用于实现链式调用。
/// 这里的每个方法和 getter 都使用 copyWith 返回一个新的 TextStyle 实例。
extension TextStyleHelper on TextStyle {
  
  // ----------- 基础属性方法 -----------

  /// 设置字体大小
  TextStyle sp(double size) => copyWith(fontSize: size.sp);
  
  /// 设置颜色
  TextStyle withColor(Color color) => copyWith(color: color);
  
  /// 设置字体
  TextStyle family(String fontFamily) => copyWith(fontFamily: fontFamily);

  /// 设置字间距
  TextStyle letterSpacing(double spacing) => copyWith(letterSpacing: spacing);
  
  /// 设置词间距
  TextStyle wordSpacing(double spacing) => copyWith(wordSpacing: spacing);
  
  /// 设置行高
  TextStyle height(double value) => copyWith(height: value);

  // ----------- 快捷方式 getter -----------
  TextStyle get bold => copyWith(fontWeight: FontWeight.w700);

  TextStyle get semiBold => copyWith(fontWeight: FontWeight.w600);

  TextStyle get medium => copyWith(fontWeight: FontWeight.w500);

  TextStyle get regular => copyWith(fontWeight: FontWeight.w400);

  /// 快捷方式：设置斜体 (FontStyle.italic)
  TextStyle get italic => copyWith(fontStyle: FontStyle.italic);

  /// 快捷方式：设置下划线
  TextStyle get underline => copyWith(decoration: TextDecoration.underline);

  /// 快捷方式：设置删除线
  TextStyle get lineThrough => copyWith(decoration: TextDecoration.lineThrough);
  
  // ----------- 常用颜色快捷方式 getter -----------

  TextStyle get white => withColor(CupertinoColors.white);
  TextStyle get black => withColor(CupertinoColors.black);
  TextStyle get grey => withColor(CupertinoColors.inactiveGray);
  TextStyle get blue => withColor(CupertinoColors.systemBlue);
  TextStyle get red => withColor(CupertinoColors.systemRed);
  TextStyle get green => withColor(CupertinoColors.systemGreen);
  TextStyle get yellow => withColor(CupertinoColors.systemYellow);
}