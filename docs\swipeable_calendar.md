# SwipeableCalendar - 自定义滑动日历组件

## 概述

SwipeableCalendar 是一个完全自定义的Flutter日历组件，用于替换原有的table_calendar插件。它保留了所有原有功能，并新增了流畅的上下滑动切换月份功能。

## 主要特性

### ✅ 保留的原有功能
- **三种视图模式**：周视图、月视图、年视图
- **日期选择**：支持单日期选择，带有选中状态高亮
- **当前日期标识**：今天的日期有特殊样式标识
- **事件回调**：完整的回调函数支持
- **样式一致性**：使用项目的ColorUtil和TextUtil工具类
- **API兼容性**：与原有CustomTableCalendar完全兼容

### 🆕 新增功能
- **多方向滑动**：月视图支持垂直、水平、双向滑动切换月份
- **周视图优化**：周视图始终使用水平滑动切换周
- **流畅动画**：使用PageView和AnimatedContainer实现的流畅切换动画
- **无限滚动**：支持无限向前和向后滚动
- **手势优化**：改进的拖拽手势识别
- **视图切换动画**：平滑的视图模式切换动画效果

## 文件结构

```
lib/widgets/
├── swipeable_calendar.dart      # 新的自定义日历组件
└── custom_table_calendar.dart   # 兼容性包装器

test/
├── swipeable_calendar_test.dart      # 新组件测试
└── custom_table_calendar_test.dart   # 原有测试（仍然通过）

example/
└── swipeable_calendar_demo.dart      # 演示应用
```

## API 文档

### SwipeableCalendar

```dart
class SwipeableCalendar extends StatefulWidget {
  final DateTime? selectedDay;                           // 当前选中的日期
  final Function(DateTime)? onDaySelected;               // 日期选择回调
  final Function(String)? onMonthChanged;                // 月份变化回调
  final Function(CustomCalendarFormat)? onViewChanged;   // 视图变化回调
  final CustomCalendarFormat? initialFormat;             // 初始视图格式
  final SwipeDirection swipeDirection;                   // 滑动方向控制（默认垂直）
}
```

### CustomCalendarFormat

```dart
enum CustomCalendarFormat {
  week,   // 周视图
  month,  // 月视图
  year,   // 年视图
}
```

### SwipeDirection

```dart
enum SwipeDirection {
  vertical,   // 垂直滑动（上下切换月份）
  horizontal, // 水平滑动（左右切换月份）
  both,       // 支持两个方向
}
```

## 使用方法

### 基本用法

```dart
SwipeableCalendar(
  selectedDay: DateTime.now(),
  onDaySelected: (selectedDay) {
    print('选中日期: $selectedDay');
  },
  onMonthChanged: (monthTitle) {
    print('月份变化: $monthTitle');
  },
  onViewChanged: (format) {
    print('视图变化: $format');
  },
  initialFormat: CustomCalendarFormat.month,
  swipeDirection: SwipeDirection.vertical, // 垂直滑动
)
```

### 滑动方向控制

```dart
// 垂直滑动（默认）
SwipeableCalendar(
  swipeDirection: SwipeDirection.vertical,
  // ... 其他参数
)

// 水平滑动
SwipeableCalendar(
  swipeDirection: SwipeDirection.horizontal,
  // ... 其他参数
)

// 双向滑动
SwipeableCalendar(
  swipeDirection: SwipeDirection.both,
  // ... 其他参数
)
```

### 兼容性使用

原有的CustomTableCalendar仍然可以正常使用，它现在内部使用SwipeableCalendar：

```dart
CustomTableCalendar(
  selectedDay: _selectedDay,
  onDaySelected: _onDaySelected,
  onMonthChanged: _onMonthChanged,
  onViewChanged: _onViewChanged,
  swipeDirection: SwipeDirection.horizontal, // 新增：可选的滑动方向参数
)
```

## 手势操作

### 滑动操作
- **周视图滑动**：始终使用水平滑动（左右）切换上下周
- **月视图滑动**：根据`swipeDirection`参数控制滑动方向
  - `SwipeDirection.vertical`：上下滑动切换月份（默认）
  - `SwipeDirection.horizontal`：左右滑动切换月份
  - `SwipeDirection.both`：支持上下和左右两个方向
- **年视图**：不支持滑动，通过点击月份进行导航

### 拖拽操作
- **拖拽底部横杠**：向上拖拽提升视图级别，向下拖拽降低视图级别
  - 周视图 ↔ 月视图 ↔ 年视图
- **平滑动画**：视图切换时带有400ms的缓动动画

## 视图模式详解

### 周视图 (Week View)
- 显示一周7天的日期
- 紧凑布局，适合快速浏览
- 支持上下滑动切换周

### 月视图 (Month View)
- 显示完整月份的日历网格
- 包含星期标题
- 支持上下滑动切换月份

### 年视图 (Year View)
- 显示12个月的缩略日历
- 3列4行网格布局
- 点击月份可快速跳转到月视图

## 技术实现

### 核心技术
- **PageView**：实现流畅的滑动切换，支持垂直和水平方向
- **AnimatedContainer**：实现视图切换的高度动画
- **AnimationController**：控制视图切换的动画时序
- **无限滚动**：使用大数值索引实现无限滚动效果
- **状态管理**：完整的状态管理和同步
- **手势识别**：优化的手势识别和处理

### 动画系统
- **视图切换动画**：400ms的缓动动画，使用Curves.easeInOut
- **高度变化动画**：AnimatedContainer实现的平滑高度过渡
- **动画状态管理**：防止动画冲突的状态控制

### 性能优化
- **页面预加载**：PageView自动预加载相邻页面
- **懒加载**：日期网格按需生成
- **内存管理**：合理的页面缓存策略
- **动画优化**：使用TickerProviderStateMixin优化动画性能

## 测试覆盖

### 单元测试
- ✅ 组件渲染测试
- ✅ 日期选择功能测试
- ✅ 月份切换测试
- ✅ 视图模式切换测试
- ✅ 手势操作测试
- ✅ 回调函数测试
- ✅ 滑动方向控制测试
- ✅ 视图切换动画测试

### 兼容性测试
- ✅ 原有CustomTableCalendar测试全部通过
- ✅ API兼容性验证
- ✅ 样式一致性验证

## 迁移指南

### 从table_calendar迁移

1. **无需修改现有代码**：CustomTableCalendar的API保持不变
2. **移除依赖**：可以从pubspec.yaml中移除table_calendar依赖
3. **享受新功能**：自动获得滑动切换功能

### 直接使用SwipeableCalendar

如果要直接使用新组件：

```dart
// 旧代码
CustomTableCalendar(...)

// 新代码
SwipeableCalendar(...)  // API完全相同
```

## 演示应用

运行演示应用查看所有功能：

```bash
flutter run example/swipeable_calendar_demo.dart
```

## 总结

SwipeableCalendar成功实现了以下目标：

1. ✅ **功能保持**：完全保留了原有table_calendar的所有功能
2. ✅ **多方向滑动**：实现了垂直、水平、双向滑动月份切换
3. ✅ **流畅动画**：添加了平滑的视图切换动画效果
4. ✅ **API兼容**：保持了完全的向后兼容性
5. ✅ **性能优化**：使用原生Flutter组件，性能更好
6. ✅ **测试覆盖**：完整的测试覆盖，确保稳定性
7. ✅ **无缝替换**：现有代码无需修改即可使用新功能
8. ✅ **灵活配置**：通过参数控制滑动方向和动画效果

这个自定义日历组件不仅满足了所有原有需求，还提供了更好的用户体验、更流畅的交互效果和更灵活的配置选项。
