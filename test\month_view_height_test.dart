import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/swipeable_calendar.dart';

void main() {
  group('Month View Height Tests', () {
    testWidgets('Should use default month view height when not specified', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证月视图正确渲染
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      // 验证没有渲染异常
      expect(tester.takeException(), isNull);
      
      // 验证月视图基本结构存在
      expect(find.byType(SwipeableCalendar), findsOneWidget);

      // 验证星期标题存在
      expect(find.text('一'), findsOneWidget);
      expect(find.text('日'), findsOneWidget);
    });

    testWidgets('Should use custom month view height when specified', (WidgetTester tester) async {
      // 测试不同的月视图高度
      final testHeights = [250.0, 350.0, 400.0];

      for (final height in testHeights) {
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: const Size(393, 852),
            builder: (context, child) {
              return CupertinoApp(
                home: CupertinoPageScaffold(
                  child: SafeArea(
                    child: SwipeableCalendar(
                      selectedDay: DateTime(2024, 6, 15),
                      initialFormat: CustomCalendarFormat.month,
                      monthViewHeight: height.h, // 传入经过ScreenUtil处理的值
                      onDaySelected: (day) {},
                      onMonthChanged: (month) {},
                      onViewChanged: (format) {},
                    ),
                  ),
                ),
              );
            },
          ),
        );

        await tester.pumpAndSettle();

        // 验证月视图能够正常渲染，不会溢出
        expect(tester.takeException(), isNull, reason: 'Should not overflow with height $height');

        // 验证月视图基本结构存在
        expect(find.byType(SwipeableCalendar), findsOneWidget);

        // 验证星期标题存在
        expect(find.text('一'), findsOneWidget);
        expect(find.text('日'), findsOneWidget);
      }
    });

    testWidgets('Should handle both month and year view heights correctly', (WidgetTester tester) async {
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    monthViewHeight: 350.0.h, // 自定义月视图高度
                    yearViewHeight: 600.0.h, // 自定义年视图高度
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态为月视图
      expect(currentFormat, equals(CustomCalendarFormat.month));
      expect(find.text('15'), findsOneWidget);

      // 切换到年视图（通过拖拽手势）
      final calendarFinder = find.byType(SwipeableCalendar);
      await tester.drag(calendarFinder, const Offset(0, -50));
      await tester.pumpAndSettle();

      // 验证切换到年视图
      expect(currentFormat, equals(CustomCalendarFormat.year));
      
      // 验证年视图内容显示
      expect(find.text('六月'), findsOneWidget);
    });

    testWidgets('Should maintain consistent behavior across view switches', (WidgetTester tester) async {
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.week,
                    monthViewHeight: 320.0.h, // 自定义月视图高度
                    yearViewHeight: 550.0.h, // 自定义年视图高度
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态为周视图
      expect(currentFormat, equals(CustomCalendarFormat.week));

      // 切换到月视图
      final calendarFinder = find.byType(SwipeableCalendar);
      await tester.drag(calendarFinder, const Offset(0, -50));
      await tester.pumpAndSettle();

      // 验证切换到月视图
      expect(currentFormat, equals(CustomCalendarFormat.month));
      expect(tester.takeException(), isNull);

      // 切换到年视图
      await tester.drag(calendarFinder, const Offset(0, -50));
      await tester.pumpAndSettle();

      // 验证切换到年视图
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(tester.takeException(), isNull);
    });

    testWidgets('Should work with different screen sizes', (WidgetTester tester) async {
      // 测试不同的屏幕尺寸
      final screenSizes = [
        const Size(320, 568), // iPhone SE
        const Size(375, 667), // iPhone 8
        const Size(393, 852), // 默认设计尺寸
        const Size(414, 896), // iPhone 11
      ];

      for (final size in screenSizes) {
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: size,
            builder: (context, child) {
              return CupertinoApp(
                home: CupertinoPageScaffold(
                  child: SafeArea(
                    child: SwipeableCalendar(
                      selectedDay: DateTime(2024, 6, 15),
                      initialFormat: CustomCalendarFormat.month,
                      monthViewHeight: 300.0.h, // 使用响应式高度
                      yearViewHeight: 500.0.h, // 使用响应式高度
                      onDaySelected: (day) {},
                      onMonthChanged: (month) {},
                      onViewChanged: (format) {},
                    ),
                  ),
                ),
              );
            },
          ),
        );

        await tester.pumpAndSettle();

        // 验证没有渲染异常
        expect(tester.takeException(), isNull, reason: 'Should not overflow with screen size $size');
        
        // 验证基本内容能够显示
        expect(find.text('15'), findsOneWidget);
      }
    });
  });
}
