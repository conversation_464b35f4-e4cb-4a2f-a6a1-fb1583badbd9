import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('Calendar Fixes Verification Tests', () {
    testWidgets('Should handle month title updates correctly during view switches', (WidgetTester tester) async {
      final testDate = DateTime(2024, 5, 15); // 2024年5月15日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      List<String> monthTitleHistory = [];
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: Safe<PERSON><PERSON>(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      monthTitleHistory.add(month);
                      print('Month title updated: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                      print('View changed to: $format');
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentFormat, equals(CustomCalendarFormat.month));
      expect(currentMonthTitle, contains('2024年5月'));
      
      // 选择一个日期
      final dayFinder18 = find.text('18');
      if (tester.any(dayFinder18)) {
        await tester.tap(dayFinder18);
        await tester.pumpAndSettle();
        
        expect(selectedDay!.day, equals(18));
        expect(selectedDay!.month, equals(5));
        expect(selectedDay!.year, equals(2024));
      }

      // 清空历史记录
      monthTitleHistory.clear();
      
      // 验证月份标题在选择日期后保持正确
      expect(currentMonthTitle, contains('2024年5月'));
      
      print('Month title consistency verified');
    });

    testWidgets('Should handle year view scrolling without layout overflow', (WidgetTester tester) async {
      final testDate = DateTime(2024, 8, 10); // 2024年8月10日
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SizedBox(
                    height: 500, // 限制高度来测试滚动
                    child: SwipeableCalendar(
                      selectedDay: testDate,
                      onDaySelected: (day) {},
                      onMonthChanged: (month) {
                        currentMonthTitle = month;
                      },
                      onViewChanged: (format) {
                        currentFormat = format;
                      },
                      initialFormat: CustomCalendarFormat.year,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证年视图正确显示
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年8月'));
      
      // 验证年视图包含滚动组件
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // 验证月份名称显示
      expect(find.text('八月'), findsOneWidget);
      
      // 测试滚动功能不会导致溢出
      final scrollView = find.byType(SingleChildScrollView);
      await tester.drag(scrollView, Offset(0, -100)); // 向上滚动
      await tester.pumpAndSettle();
      
      // 验证滚动后状态仍然正确
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年8月'));
      
      print('Year view scrolling verified without overflow');
    });

    testWidgets('Should maintain selectedDay consistency across different operations', (WidgetTester tester) async {
      final testDate = DateTime(2024, 11, 25); // 2024年11月25日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.week,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentFormat, equals(CustomCalendarFormat.week));
      expect(currentMonthTitle, contains('2024年11月'));
      
      // 选择一个日期
      final dayFinder27 = find.text('27');
      if (tester.any(dayFinder27)) {
        await tester.tap(dayFinder27);
        await tester.pumpAndSettle();
        
        expect(selectedDay!.day, equals(27));
        expect(selectedDay!.month, equals(11));
        expect(selectedDay!.year, equals(2024));
      }

      // 验证selectedDay在各种操作后保持一致
      expect(selectedDay!.day, equals(27));
      expect(selectedDay!.month, equals(11));
      expect(selectedDay!.year, equals(2024));
      expect(currentMonthTitle, contains('2024年11月'));
      
      print('SelectedDay consistency verified');
    });

    testWidgets('Should handle _notifyMonthChanged calls correctly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 2, 14); // 2024年2月14日
      DateTime? selectedDay;
      String? currentMonthTitle;
      int monthChangeCallCount = 0;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      monthChangeCallCount++;
                      print('Month changed called: $month (count: $monthChangeCallCount)');
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentMonthTitle, contains('2024年2月'));
      final initialCallCount = monthChangeCallCount;
      
      // 选择一个日期
      final dayFinder16 = find.text('16');
      if (tester.any(dayFinder16)) {
        await tester.tap(dayFinder16);
        await tester.pumpAndSettle();
        
        expect(selectedDay!.day, equals(16));
        expect(selectedDay!.month, equals(2));
      }

      // 验证月份变化回调被正确调用（点击日期时应该调用一次）
      expect(monthChangeCallCount, greaterThanOrEqualTo(initialCallCount));
      expect(currentMonthTitle, contains('2024年2月'));
      
      print('_notifyMonthChanged calls verified');
    });
  });
}
