import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 分类列表控制器 - 提供逻辑控制，UI完全由用户自定义
///
/// 这个组件不包含任何预设的UI布局，只提供：
/// 1. 分类数据管理
/// 2. 选中状态管理
/// 3. 滚动联动逻辑
/// 4. 导航跳转功能
class CategoryListController<T> extends StatefulWidget {
  /// 数据列表
  final List<T> categories;

  /// 初始选中的索引
  final int initialSelectedIndex;

  /// 选中索引变化的回调
  final void Function(int index)? onSelectedIndexChanged;

  /// 是否启用滚动联动（默认启用）
  final bool enableScrollSync;

  /// 构建器函数 - 用户完全自定义UI
  /// 参数：
  /// - categories: 分类数据列表
  /// - selectedIndex: 当前选中的索引
  /// - onCategoryTap: 点击分类的回调函数
  /// - scrollController: 内容区域的滚动控制器
  /// - sectionKeys: 每个分组的GlobalKey列表，用于滚动定位
  /// - wrapScrollView: 包装滚动视图的函数，用于监听滚动事件
  final Widget Function({
    required List<T> categories,
    required int selectedIndex,
    required void Function(int index) onCategoryTap,
    required ScrollController scrollController,
    required List<GlobalKey> sectionKeys,
    required Widget Function(Widget child) wrapScrollView,
  }) builder;

  const CategoryListController({
    super.key,
    required this.categories,
    required this.builder,
    this.initialSelectedIndex = 0,
    this.onSelectedIndexChanged,
    this.enableScrollSync = true,
  });

  @override
  State<CategoryListController<T>> createState() => _CategoryListControllerState<T>();
}

class _CategoryListControllerState<T> extends State<CategoryListController<T>> {
  late int selectedCategoryIndex;
  final ScrollController _scrollController = ScrollController();
  final List<GlobalKey> _sectionKeys = [];
  bool _isScrollingToSection = false; // 标志位：是否正在执行点击导航的滚动
  int? _pendingScrollIndex; // 待处理的滚动目标索引
  DateTime? _lastClickTime; // 最后一次点击时间，用于防抖

  @override
  void initState() {
    super.initState();
    selectedCategoryIndex = widget.initialSelectedIndex;

    // 为每个分类创建 GlobalKey
    _sectionKeys.clear();
    for (int i = 0; i < widget.categories.length; i++) {
      _sectionKeys.add(GlobalKey());
    }
  }

  @override
  void didUpdateWidget(CategoryListController<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果数据列表长度发生变化，重新创建 GlobalKey
    if (oldWidget.categories.length != widget.categories.length) {
      _sectionKeys.clear();
      for (int i = 0; i < widget.categories.length; i++) {
        _sectionKeys.add(GlobalKey());
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder(
      categories: widget.categories,
      selectedIndex: selectedCategoryIndex,
      onCategoryTap: _onCategoryTap,
      scrollController: _scrollController,
      sectionKeys: _sectionKeys,
      wrapScrollView: _wrapScrollView,
    );
  }

  // 包装滚动视图，添加滚动监听
  Widget _wrapScrollView(Widget child) {
    if (!widget.enableScrollSync) {
      // 即使不启用滚动联动，也要拦截滚动通知，避免往外传递
      return NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          return true; // 拦截滚动通知，避免往外传递
        },
        child: child,
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (!_isScrollingToSection &&
            (notification is ScrollUpdateNotification ||
                notification is ScrollEndNotification)) {
          // 使用 WidgetsBinding.instance.addPostFrameCallback 确保在下一帧执行
          // 这样可以避免在布局过程中访问 RenderBox
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && !_isScrollingToSection) {
              _updateSelectedCategoryOnScroll();
            }
          });
        }
        return true; // 拦截滚动通知，避免往外传递
      },
      child: child,
    );
  }

  // 处理分类点击事件
  void _onCategoryTap(int index) {
    setState(() {
      selectedCategoryIndex = index;
    });
    widget.onSelectedIndexChanged?.call(index);
    _scrollToSection(index);
  }

  // 根据滚动位置更新选中的分类（优化版）
  void _updateSelectedCategoryOnScroll() {
    // 前置条件检查，如果没有滚动控制器或分组为空，则直接返回
    if (!_scrollController.hasClients || _sectionKeys.isEmpty) {
      return;
    }

    // 如果有待处理的滚动目标，不要更新选中状态，避免冲突
    if (_pendingScrollIndex != null) {
      return;
    }

    try {
      // 将视口的参考点定义在靠上的位置（例如顶部下方 100 像素），
      // 这样当一个分组的顶部到达该位置时，它就会被选中，体验更好。
      final double viewportReferencePoint = _scrollController.offset + 100.h;

      double minDistance = double.infinity; // 用于记录最小距离
      int newSelectedIndex = selectedCategoryIndex; // 默认为当前选中的索引

      double accumulatedHeight = 0; // 用于计算每个分组的垂直位置

      // 单次遍历所有分组，找到距离参考点最近的一个
      for (int i = 0; i < _sectionKeys.length; i++) {
        final BuildContext? context = _sectionKeys[i].currentContext;
        if (context != null) {
          final RenderObject? renderObject = context.findRenderObject();

          // 检查 RenderObject 是否为 RenderBox 且已经完成布局
          if (renderObject is RenderBox && renderObject.hasSize) {
            final double sectionHeight = renderObject.size.height;

            // 计算分组的中心点位置
            final double sectionCenter = accumulatedHeight + sectionHeight / 2;

            // 计算分组中心点与视口参考点的绝对距离
            final double distance = (viewportReferencePoint - sectionCenter).abs();

            // 如果当前分组的距离更小，则更新最小距离和选中索引
            if (distance < minDistance) {
              minDistance = distance;
              newSelectedIndex = i;
            }

            // 累加高度，为下一个分组的位置计算做准备（包含分组间的固定间距）
            accumulatedHeight += sectionHeight + 24.h;
          } else {
            // 如果无法获取准确的高度，使用估算值
            accumulatedHeight += 200.h; // 估算的分组高度
          }
        } else {
          // 如果无法获取上下文，使用估算值
          accumulatedHeight += 200.h; // 估算的分组高度
        }
      }

      // 只有在计算出的索引与当前索引不同时，才更新状态以避免不必要的重绘
      if (newSelectedIndex != selectedCategoryIndex) {
        setState(() {
          selectedCategoryIndex = newSelectedIndex;
        });
        widget.onSelectedIndexChanged?.call(newSelectedIndex);
      }
    } catch (e) {
      // 如果在滚动过程中出现任何错误，静默处理，避免崩溃
      // 这通常发生在快速滚动或布局变化期间
      debugPrint('滚动联动更新时出现错误: $e');
    }
  }

  // 滚动到指定分组
  void _scrollToSection(int index) {
    if (!_scrollController.hasClients || index >= _sectionKeys.length) {
      return;
    }

    final now = DateTime.now();

    // 防抖处理：如果距离上次点击时间太短，延迟执行
    if (_lastClickTime != null && now.difference(_lastClickTime!).inMilliseconds < 100) {
      _pendingScrollIndex = index;
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_pendingScrollIndex == index) {
          _executeScrollToSection(index);
        }
      });
      return;
    }

    _lastClickTime = now;
    _pendingScrollIndex = index;
    _executeScrollToSection(index);
  }

  // 执行实际的滚动操作
  void _executeScrollToSection(int index) {
    if (!_scrollController.hasClients || index >= _sectionKeys.length) {
      return;
    }

    try {
      // 如果已经在滚动中，取消之前的滚动状态并立即开始新的滚动
      if (_isScrollingToSection) {
        // 强制停止当前滚动动画
        _scrollController.jumpTo(_scrollController.offset);
      }

      // 设置标志位，防止滚动动画过程中的选中状态更新
      _isScrollingToSection = true;

      // 使用 ensureVisible 方法，这是 Flutter 推荐的滚动到指定组件的方式
      final BuildContext? targetContext = _sectionKeys[index].currentContext;
      if (targetContext != null) {
        // 检查目标上下文是否有效且已挂载
        if (targetContext.mounted) {
          Scrollable.ensureVisible(
            targetContext,
            duration: const Duration(milliseconds: 300), // 缩短动画时间，提高响应性
            curve: Curves.easeInOut,
            alignment: 0.0, // 0.0 表示滚动到顶部，1.0 表示滚动到底部
          ).then((_) {
            // 滚动完成后重置标志位，但只有当这是最后一次滚动时才重置
            if (_pendingScrollIndex == index) {
              _isScrollingToSection = false;
              _pendingScrollIndex = null;
            }
          }).catchError((error) {
            // 如果滚动出错，也要重置标志位
            debugPrint('滚动到指定分组时出现错误: $error');
            _isScrollingToSection = false;
            _pendingScrollIndex = null;
          });
        } else {
          // 如果上下文未挂载，立即重置标志位
          _isScrollingToSection = false;
          _pendingScrollIndex = null;
        }
      } else {
        // 如果目标上下文不存在，立即重置标志位
        _isScrollingToSection = false;
        _pendingScrollIndex = null;
      }
    } catch (e) {
      // 捕获任何可能的异常，确保标志位被正确重置
      debugPrint('执行滚动操作时出现异常: $e');
      _isScrollingToSection = false;
      _pendingScrollIndex = null;
    }
  }
}
