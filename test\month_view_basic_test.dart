import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/swipeable_calendar.dart';

void main() {
  group('Month View Basic Tests', () {
    testWidgets('Should render month view with default height', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证月视图正确渲染
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      // 验证没有渲染异常
      expect(tester.takeException(), isNull);
    });

    testWidgets('Should render month view with custom height', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    monthViewHeight: 400.0.h, // 使用自定义高度
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证月视图正确渲染
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      // 验证没有渲染异常
      expect(tester.takeException(), isNull);
    });

    testWidgets('Should handle both month and year view heights', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.month,
                    monthViewHeight: 350.0.h,
                    yearViewHeight: 600.0.h,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证月视图正确渲染
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      // 验证没有渲染异常
      expect(tester.takeException(), isNull);
    });
  });
}
