import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/text_util.dart';
import '../models/models.dart';

/// 症状内容展示Widget
/// 负责展示单个症状分类的内容，包括标题和症状标签
class SymptomContentWidget extends StatelessWidget {
  /// 症状分类数据
  final SymptomCategory category;
  
  /// 分类索引
  final int index;
  
  /// 是否被选中
  final bool isSelected;
  
  /// 主题颜色
  final Color cardColor;
  
  /// 选中的症状集合
  final Set<String> selectedSymptoms;
  
  /// 症状选择状态变化回调
  final void Function(String symptom, bool isSelected) onSymptomToggle;
  
  /// 添加症状回调
  final void Function(int categoryType) onAddSymptom;

  const SymptomContentWidget({
    super.key,
    required this.category,
    required this.index,
    required this.isSelected,
    required this.cardColor,
    required this.selectedSymptoms,
    required this.onSymptomToggle,
    required this.onAddSymptom,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分类标题
        Container(
          padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
          decoration: BoxDecoration(
            color: isSelected ? cardColor.withValues(alpha: 0.1) : null,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            category.title,
            style: TextUtil.base.sp(18).semiBold.withColor(cardColor),
          ),
        ),
        SizedBox(height: 12.h),

        // 症状内容列表
        category.content.isEmpty
            ? Padding(
                padding: EdgeInsets.symmetric(vertical: 16.h),
                child: Text(
                  '暂无数据',
                  style: TextUtil.base
                      .sp(14)
                      .withColor(CupertinoColors.systemGrey),
                ),
              )
            : Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: [
                  // 现有的症状标签
                  ...category.content
                      .map(
                        (content) => _buildSymptomTag(
                          _getDisplayText(content, category.type),
                        ),
                      ),
                  // 添加+号标签
                  _buildAddTag(category.type),
                ],
              ),
      ],
    );
  }

  /// 根据症状内容和分类类型获取显示文本
  String _getDisplayText(SymptomContent content, int categoryType) {
    switch (categoryType) {
      case 1: // 心情
        return content.description ?? '';
      case 2: // 食欲
        return '${content.name ?? ''} ${content.weight ?? ''}';
      case 3: // 体重
        return '${content.weight ?? ''} (${content.time ?? ''})';
      case 4: // 药物
        return '${content.medicineName ?? ''} ${content.quantity ?? ''}';
      default:
        return '';
    }
  }

  /// 构建症状标签
  Widget _buildSymptomTag(String text) {
    final isSymptomSelected = selectedSymptoms.contains(text);

    return GestureDetector(
      onTap: () {
        onSymptomToggle(text, !isSymptomSelected);
      },
      child: Stack(
        clipBehavior: Clip.none, // 允许子组件超出边界，避免裁剪
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: EdgeInsets.symmetric(horizontal: 4.w),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: isSymptomSelected ? cardColor : CupertinoColors.transparent,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: cardColor,
              ),
            ),
            child: Text(
              text,
              style: TextUtil.base.sp(14).withColor(
                isSymptomSelected ? CupertinoColors.white : cardColor,
              ),
            ),
          ),
          // 选中状态的小勾图标 - 使用AnimatedPositioned实现动画效果
          Positioned(
            right: 0,
            bottom: 0,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 150),
              opacity: isSymptomSelected ? 1.0 : 0.0,
              child: Container(
                width: 16.w,
                height: 16.w,
                decoration: BoxDecoration(
                  color: cardColor,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: CupertinoColors.white,
                    width: 1.5,
                  ),
                ),
                child: Icon(
                  CupertinoIcons.checkmark,
                  size: 10.sp,
                  color: CupertinoColors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建添加标签的+号按钮
  Widget _buildAddTag(int categoryType) {
    return GestureDetector(
      onTap: () => onAddSymptom(categoryType),
      child: Container(
        // 添加额外的padding来保持与其他标签的一致性
        padding: EdgeInsets.only(right: 8.w, bottom: 8.h),
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 4.h),
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
          decoration: BoxDecoration(
            color: CupertinoColors.transparent,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(
              color: cardColor,
              style: BorderStyle.solid,
            ),
          ),
          child: Icon(
            CupertinoIcons.add,
            size: 14.sp,
            color: cardColor,
          ),
        ),
      ),
    );
  }
}
