import 'package:flutter/cupertino.dart';
import 'package:flutter_markdown_plus/flutter_markdown_plus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/color_util.dart';
import '../utils/text_util.dart';
import '../models/preparation_info.dart';

/// 项目准备信息底部弹出sheet
class PreparationInfoBottomSheet extends StatefulWidget {
  final PreparationInfo preparationInfo;
  final Color cardColor;
  final Color cardBgColor;

  const PreparationInfoBottomSheet({
    super.key,
    required this.preparationInfo,
    required this.cardColor,
    required this.cardBgColor,
  });

  @override
  State<PreparationInfoBottomSheet> createState() =>
      _PreparationInfoBottomSheetState();
}

class _PreparationInfoBottomSheetState
    extends State<PreparationInfoBottomSheet> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 0.8.sh, // 占屏幕高度的80%
      decoration: BoxDecoration(
        color: widget.cardBgColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          // 顶部标题栏
          _buildHeader(),
          
          // 分割线
          Container(
            height: 1.h,
            color: ColorUtil.borderGrey,
          ),
          
          // 内容区域
          _buildContent(),
        ],
      ),
    );
  }

  /// 构建顶部标题栏
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Stack(
        children: [
          // 返回按钮
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                padding: EdgeInsets.all(8.w),
                child: Icon(
                  CupertinoIcons.back,
                  color: widget.cardColor,
                  size: 20.sp,
                ),
              ),
            ),
          ),

          // 居中标题
          Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 48.w), // 左右留出返回按钮的空间
              child: Text(
                widget.preparationInfo.title,
                style: TextUtil.base.sp(18).semiBold.withColor(widget.cardColor),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Expanded(
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: EdgeInsets.all(16.w),
        child: MarkdownBody(
          data: widget.preparationInfo.content,
          styleSheet: MarkdownStyleSheet(
            // 段落样式
            p: TextUtil.base.sp(14).withColor(widget.cardColor),
            // 标题样式
            h1: TextUtil.base.sp(20).bold.withColor(widget.cardColor),
            h2: TextUtil.base.sp(18).semiBold.withColor(widget.cardColor),
            h3: TextUtil.base.sp(16).semiBold.withColor(widget.cardColor),
            // 粗体样式
            strong: TextUtil.base.sp(14).bold.withColor(widget.cardColor),
            // 列表样式
            listBullet: TextUtil.base.sp(14).withColor(widget.cardColor),
            // 段落间距
            pPadding: EdgeInsets.only(bottom: 8.h),
            h1Padding: EdgeInsets.only(top: 16.h, bottom: 8.h),
            h2Padding: EdgeInsets.only(top: 14.h, bottom: 6.h),
            h3Padding: EdgeInsets.only(top: 12.h, bottom: 4.h),
          ),
          selectable: true, // 允许选择文本
        ),
      ),
    );
  }
}
