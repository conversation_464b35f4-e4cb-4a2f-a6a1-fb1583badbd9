import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/symptom_bottom_sheets/medicine_add_bottom_sheet.dart';

void main() {
  group('MedicineAddBottomSheet Tests', () {
    testWidgets('should display medicine add bottom sheet correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Si<PERSON>(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => MedicineAddBottomSheet(
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onMedicineAdded: (medicineName, quantity, unit, time, cycle) {
                    print('Medicine added: $medicineName $quantity$unit at $time, cycle: $cycle');
                  },
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证标题是否正确显示
      expect(find.text('添加药物'), findsOneWidget);

      // 验证输入框是否存在
      expect(find.text('请输入药品名称'), findsOneWidget);
      expect(find.text('数量'), findsOneWidget);

      // 验证单位选择框是否存在
      expect(find.text('mg'), findsOneWidget);

      // 验证时间选择框是否存在
      expect(find.byIcon(CupertinoIcons.clock), findsOneWidget);

      // 验证设置提示按钮是否存在
      expect(find.text('设置'), findsOneWidget);
      expect(find.text('提示'), findsOneWidget);

      // 验证周期性标签和一周七天按钮是否存在
      expect(find.text('周期性'), findsOneWidget);
      expect(find.text('一'), findsOneWidget);
      expect(find.text('二'), findsOneWidget);
      expect(find.text('三'), findsOneWidget);
      expect(find.text('四'), findsOneWidget);
      expect(find.text('五'), findsOneWidget);
      expect(find.text('六'), findsOneWidget);
      expect(find.text('日'), findsOneWidget);

      // 验证添加按钮是否存在
      expect(find.text('添加'), findsOneWidget);
    });

    testWidgets('should validate medicine input', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => MedicineAddBottomSheet(
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onMedicineAdded: (medicineName, quantity, unit, time, cycle) {},
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击添加按钮而不输入任何内容，应该显示错误提示
      await tester.tap(find.text('添加'));
      await tester.pumpAndSettle();

      // 验证错误对话框是否显示
      expect(find.text('请输入药品名称'), findsOneWidget);
      expect(find.text('提示'), findsWidgets); // 可能有多个"提示"文本
    });

    testWidgets('should handle cycle selection', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => MedicineAddBottomSheet(
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onMedicineAdded: (medicineName, quantity, unit, time, cycle) {},
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击周一按钮
      await tester.tap(find.text('一'));
      await tester.pumpAndSettle();

      // 点击周三按钮
      await tester.tap(find.text('三'));
      await tester.pumpAndSettle();

      // 点击周五按钮
      await tester.tap(find.text('五'));
      await tester.pumpAndSettle();

      // 验证按钮状态变化（这里只是确保没有错误）
      expect(find.text('一'), findsOneWidget);
      expect(find.text('三'), findsOneWidget);
      expect(find.text('五'), findsOneWidget);
    });

    testWidgets('should show unit picker when unit field is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => MedicineAddBottomSheet(
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onMedicineAdded: (medicineName, quantity, unit, time, cycle) {},
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证默认单位是mg
      expect(find.text('mg'), findsOneWidget);

      // 点击单位选择框
      await tester.tap(find.byIcon(CupertinoIcons.chevron_down));
      await tester.pumpAndSettle();

      // 验证单位选择器是否显示
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('确定'), findsOneWidget);
      expect(find.text('g'), findsOneWidget);
      expect(find.text('ml'), findsOneWidget);
      expect(find.text('片'), findsOneWidget);
      expect(find.text('粒'), findsOneWidget);
    });

    testWidgets('should show time picker when time field is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => MedicineAddBottomSheet(
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onMedicineAdded: (medicineName, quantity, unit, time, cycle) {},
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击时间选择框
      await tester.tap(find.byIcon(CupertinoIcons.clock));
      await tester.pumpAndSettle();

      // 验证时间选择器是否显示
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('确定'), findsOneWidget);
    });
  });
}
