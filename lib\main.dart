import 'package:align/pages/setup_page.dart';
import 'package:align/pages/main_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'services/auth_service.dart';

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化认证服务
  await AuthService.instance.initialize();

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool? _isLoggedIn;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkInitialLoginStatus();
  }

  Future<void> _checkInitialLoginStatus() async {
    try {
      final isLoggedIn = await AuthService.instance.isLoggedIn();
      if (mounted) {
        setState(() {
          _isLoggedIn = isLoggedIn;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoggedIn = false;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(393, 852), // iPhone 15 Pro design size
      builder: (context, child) {
        return CupertinoApp(
          title: 'Align',
          theme: const CupertinoThemeData(primaryColor: CupertinoColors.systemBlue),
          localizationsDelegates: const [
            GlobalCupertinoLocalizations.delegate, // Cupertino 本地化支持
            GlobalWidgetsLocalizations.delegate, // Widget 本地化支持
            GlobalMaterialLocalizations.delegate, // Material 本地化支持
            FlutterQuillLocalizations.delegate, // Flutter Quill 本地化支持
          ],
          supportedLocales: const [
            Locale('zh', 'CN'), // 支持中文
          ],
          home: _isLoading
              ? const CupertinoPageScaffold(
                  child: Center(child: CupertinoActivityIndicator()),
                )
              : (_isLoggedIn == true ? const MainPage() : const SetupPage()),
        );
      },
    );
  }
}
