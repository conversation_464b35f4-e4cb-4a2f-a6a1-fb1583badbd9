import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../widgets/category_list_view.dart';
import '../utils/text_util.dart';

/// 演示如何使用CategoryListController创建完全自定义布局的示例页面
class CategoryListExample extends StatefulWidget {
  const CategoryListExample({super.key});

  @override
  State<CategoryListExample> createState() => _CategoryListExampleState();
}

class _CategoryListExampleState extends State<CategoryListExample> {
  // 示例数据模型
  final List<ExampleCategory> categories = [
    ExampleCategory(
      title: '水果',
      items: ['苹果', '香蕉', '橙子', '葡萄', '草莓'],
    ),
    ExampleCategory(
      title: '蔬菜',
      items: ['胡萝卜', '西红柿', '黄瓜', '菠菜', '白菜'],
    ),
    ExampleCategory(
      title: '肉类',
      items: ['牛肉', '猪肉', '鸡肉', '鱼肉'],
    ),
    ExampleCategory(
      title: '饮料',
      items: ['水', '果汁', '咖啡', '茶', '汽水'],
    ),
  ];

  int selectedCategoryIndex = 0;
  final Set<String> selectedItems = {};

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('CategoryListController 示例'),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Container(
            decoration: BoxDecoration(
              color: CupertinoColors.white,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: CategoryListController<ExampleCategory>(
              categories: categories,
              initialSelectedIndex: selectedCategoryIndex,
              onSelectedIndexChanged: (index) {
                setState(() {
                  selectedCategoryIndex = index;
                });
              },
              builder: ({
                required categories,
                required selectedIndex,
                required onCategoryTap,
                required scrollController,
                required sectionKeys,
                required wrapScrollView,
              }) {
                return _buildCustomLayout(
                  categories,
                  selectedIndex,
                  onCategoryTap,
                  scrollController,
                  sectionKeys,
                  wrapScrollView,
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // 自定义布局 - 演示完全自定义的UI
  Widget _buildCustomLayout(
    List<ExampleCategory> categories,
    int selectedIndex,
    void Function(int index) onCategoryTap,
    ScrollController scrollController,
    List<GlobalKey> sectionKeys,
    Widget Function(Widget child) wrapScrollView,
  ) {
    return Column(
      children: [
        // 顶部标签栏
        Container(
          height: 50.h,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final isSelected = index == selectedIndex;
              return GestureDetector(
                onTap: () => onCategoryTap(index),
                child: Container(
                  margin: EdgeInsets.only(right: 12.w),
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: isSelected ? CupertinoColors.systemBlue : CupertinoColors.systemGrey6,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Center(
                    child: Text(
                      categories[index].title,
                      style: TextUtil.base.sp(14).withColor(
                        isSelected ? CupertinoColors.white : CupertinoColors.black,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // 内容区域
        Expanded(
          child: Container(
            padding: EdgeInsets.all(16.w),
            child: wrapScrollView(
              SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  children: categories.asMap().entries.map((entry) {
                    final index = entry.key;
                    final category = entry.value;

                    return Container(
                      key: sectionKeys[index],
                      margin: EdgeInsets.only(bottom: 24.h),
                      child: _buildCategoryContent(category, index, index == selectedIndex),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryContent(ExampleCategory category, int index, bool isSelected) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分类标题
        Container(
          padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
          decoration: BoxDecoration(
            color: isSelected ? CupertinoColors.systemBlue.withValues(alpha: 0.1) : null,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            category.title,
            style: TextUtil.base.sp(18).semiBold.withColor(CupertinoColors.systemBlue),
          ),
        ),
        SizedBox(height: 12.h),

        // 项目列表
        category.items.isEmpty
            ? Padding(
                padding: EdgeInsets.symmetric(vertical: 16.h),
                child: Text(
                  '暂无数据',
                  style: TextUtil.base.sp(14).withColor(CupertinoColors.systemGrey),
                ),
              )
            : Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: category.items.map((item) => _buildItemTag(item)).toList(),
              ),
      ],
    );
  }

  Widget _buildItemTag(String item) {
    final isItemSelected = selectedItems.contains(item);

    return GestureDetector(
      onTap: () {
        setState(() {
          if (isItemSelected) {
            selectedItems.remove(item);
          } else {
            selectedItems.add(item);
          }
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: isItemSelected ? CupertinoColors.systemBlue : CupertinoColors.transparent,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: CupertinoColors.systemBlue,
          ),
        ),
        child: Text(
          item,
          style: TextUtil.base.sp(14).withColor(
            isItemSelected ? CupertinoColors.white : CupertinoColors.systemBlue,
          ),
        ),
      ),
    );
  }
}

/// 示例数据模型
class ExampleCategory {
  final String title;
  final List<String> items;

  ExampleCategory({
    required this.title,
    required this.items,
  });
}
