import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/chinese_date_picker.dart';

void main() {
  group('ChineseDatePicker Tests', () {
    testWidgets('should display Chinese date picker correctly', (WidgetTester tester) async {
      DateTime selectedDate = DateTime(2025, 1, 15);
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: ChineseDatePicker(
                initialDate: selectedDate,
                textColor: CupertinoColors.systemBlue,
                onDateChanged: (date) {
                  selectedDate = date;
                },
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证年月日的中文标识
      expect(find.text('2025年'), findsOneWidget);
      expect(find.text('1月'), findsOneWidget);
      expect(find.text('15日'), findsOneWidget);
    });

    testWidgets('should handle month changes correctly', (WidgetTester tester) async {
      DateTime selectedDate = DateTime(2025, 1, 31); // 1月31日
      DateTime? changedDate;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: ChineseDatePicker(
                initialDate: selectedDate,
                textColor: CupertinoColors.systemBlue,
                onDateChanged: (date) {
                  changedDate = date;
                },
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(find.text('2025年'), findsOneWidget);
      expect(find.text('1月'), findsOneWidget);
      expect(find.text('31日'), findsOneWidget);
    });

    testWidgets('should handle leap year February correctly', (WidgetTester tester) async {
      DateTime selectedDate = DateTime(2024, 2, 15); // 2024年是闰年
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: ChineseDatePicker(
                initialDate: selectedDate,
                textColor: CupertinoColors.systemBlue,
                onDateChanged: (date) {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证闰年2月显示
      expect(find.text('2024年'), findsOneWidget);
      expect(find.text('2月'), findsOneWidget);
      expect(find.text('15日'), findsOneWidget);
    });

    testWidgets('should respect minimum and maximum dates', (WidgetTester tester) async {
      final minDate = DateTime(2020, 1, 1);
      final maxDate = DateTime(2030, 12, 31);
      DateTime selectedDate = DateTime(2025, 6, 15);
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: ChineseDatePicker(
                initialDate: selectedDate,
                textColor: CupertinoColors.systemBlue,
                minimumDate: minDate,
                maximumDate: maxDate,
                onDateChanged: (date) {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证日期显示
      expect(find.text('2025年'), findsOneWidget);
      expect(find.text('6月'), findsOneWidget);
      expect(find.text('15日'), findsOneWidget);
    });
  });

  group('DatePickerBottomSheet Tests', () {
    testWidgets('should display date picker bottom sheet correctly', (WidgetTester tester) async {
      DateTime selectedDate = DateTime(2025, 7, 27);
      DateTime? resultDate;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: DatePickerBottomSheet(
                initialDate: selectedDate,
                color: CupertinoColors.systemBlue,
                bgColor: CupertinoColors.systemGrey6,
                title: '选择日期',
                onDateSelected: (date) {
                  resultDate = date;
                },
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证标题和按钮
      expect(find.text('选择日期'), findsOneWidget);
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('确定'), findsOneWidget);

      // 验证日期显示
      expect(find.text('2025年'), findsOneWidget);
      expect(find.text('7月'), findsOneWidget);
      expect(find.text('27日'), findsOneWidget);
    });

    testWidgets('should handle confirm button correctly', (WidgetTester tester) async {
      DateTime selectedDate = DateTime(2025, 7, 27);
      DateTime? resultDate;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: CupertinoApp(
            home: CupertinoPageScaffold(
              child: Builder(
                builder: (context) => DatePickerBottomSheet(
                  initialDate: selectedDate,
                  color: CupertinoColors.systemBlue,
                  bgColor: CupertinoColors.systemGrey6,
                  onDateSelected: (date) {
                    resultDate = date;
                  },
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击确定按钮
      await tester.tap(find.text('确定'));
      await tester.pumpAndSettle();

      // 验证回调被调用
      expect(resultDate, isNotNull);
      expect(resultDate!.year, equals(2025));
      expect(resultDate!.month, equals(7));
      expect(resultDate!.day, equals(27));
    });
  });
}
