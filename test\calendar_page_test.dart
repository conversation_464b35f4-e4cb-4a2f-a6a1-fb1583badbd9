import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/pages/calendar_page.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('CalendarPage Tests', () {
    testWidgets('Should render without errors', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Si<PERSON>(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CalendarPage(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证页面基本元素
      expect(find.byType(CalendarPage), findsOneWidget);
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      // 验证视图切换按钮（可能有多个相同文本，所以使用findsAtLeastNWidgets）
      expect(find.text('周'), findsAtLeastNWidgets(1));
      expect(find.text('月'), findsAtLeastNWidgets(1));
      expect(find.text('年'), findsAtLeastNWidgets(1));
      
      print('CalendarPage rendered successfully');
    });

    testWidgets('Should handle view toggle buttons', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CalendarPage(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态（默认为周视图）
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      // 点击月视图按钮
      final monthButton = find.text('月');
      expect(monthButton, findsOneWidget);
      await tester.tap(monthButton);
      await tester.pumpAndSettle();

      // 验证月视图切换成功
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      // 点击年视图按钮
      final yearButton = find.text('年');
      expect(yearButton, findsOneWidget);
      await tester.tap(yearButton);
      await tester.pumpAndSettle();

      // 验证年视图切换成功
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      print('View toggle buttons work correctly');
    });

    testWidgets('Should display header elements correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CalendarPage(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证头部按钮
      expect(find.byIcon(CupertinoIcons.search), findsOneWidget);
      expect(find.byIcon(CupertinoIcons.add), findsOneWidget);
      
      // 验证月份标题存在（具体内容可能变化）
      expect(find.textContaining('年'), findsAtLeastNWidgets(1));
      expect(find.textContaining('月'), findsAtLeastNWidgets(1));
      
      print('Header elements displayed correctly');
    });

    testWidgets('Should display content cards when not in year view', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CalendarPage(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 在周视图或月视图时，应该显示内容卡片
      expect(find.text('要做的事'), findsOneWidget);
      expect(find.text('症状'), findsOneWidget);
      expect(find.text('日记'), findsOneWidget);
      
      print('Content cards displayed correctly');
    });

    testWidgets('Should handle calendar interactions', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CalendarPage(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证日历组件存在并可以交互
      final calendarFinder = find.byType(SwipeableCalendar);
      expect(calendarFinder, findsOneWidget);
      
      // 尝试点击日历中的日期（如果存在）
      final dayFinder = find.text('15');
      if (tester.any(dayFinder)) {
        await tester.tap(dayFinder);
        await tester.pumpAndSettle();
        
        print('Calendar date interaction works');
      }
      
      print('Calendar interactions handled correctly');
    });
  });
}
