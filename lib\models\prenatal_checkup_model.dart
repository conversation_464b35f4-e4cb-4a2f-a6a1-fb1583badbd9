import 'package:azlistview/azlistview.dart';

/// 产检项目模型
class PrenatalCheckupModel extends ISuspensionBean {
  final String name;
  final int startWeek;
  final int endWeek;
  String _suspensionTag = '';

  PrenatalCheckupModel({
    required this.name,
    required this.startWeek,
    required this.endWeek,
  }) {
    _suspensionTag = startWeek.toString();
  }

  @override
  String getSuspensionTag() => _suspensionTag;

  @override
  String toString() => 'PrenatalCheckupModel{name: $name, startWeek: $startWeek, endWeek: $endWeek}';
}

/// 产检数据管理器
class PrenatalCheckupDataManager {
  static final List<Map<String, dynamic>> _prenatalData = [
    {
      'startweek': 5,
      'endweek': 6,
      'activities': [
        '阴道超声',
        '血hCG'
      ]
    },
    {
      'startweek': 8,
      'endweek': 12,
      'activities': [
        '唐氏筛查',
        '血常规'
      ]
    },
    {
      'startweek': 16,
      'endweek': 18,
      'activities': [
        '胎儿心跳监测',
        '血糖检测'
      ]
    },
    {
      'startweek': 24,
      'endweek': 28,
      'activities': [
        '糖耐量测试',
        'B超'
      ]
    },
    {
      'startweek': 32,
      'endweek': 36,
      'activities': [
        '阴道超声',
        '胎动监测'
      ]
    }
  ];

  /// 获取所有产检项目
  static List<PrenatalCheckupModel> getAllCheckups() {
    List<PrenatalCheckupModel> checkups = [];
    
    for (var data in _prenatalData) {
      int startWeek = data['startweek'];
      int endWeek = data['endweek'];
      List<String> activities = List<String>.from(data['activities']);
      
      for (String activity in activities) {
        checkups.add(PrenatalCheckupModel(
          name: activity,
          startWeek: startWeek,
          endWeek: endWeek,
        ));
      }
    }
    
    return checkups;
  }

  /// 搜索产检项目
  static List<PrenatalCheckupModel> searchCheckups(String query) {
    if (query.isEmpty) {
      return getAllCheckups();
    }
    
    return getAllCheckups()
        .where((checkup) => checkup.name.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  /// 检查产检项目是否存在
  static bool isCheckupExists(String name) {
    return getAllCheckups().any((checkup) => checkup.name == name);
  }

  /// 添加新的产检项目（添加到第一个分组）
  static bool addCheckup(String name) {
    if (name.isEmpty || isCheckupExists(name)) {
      return false;
    }
    
    // 简化处理：添加到第一个分组
    if (_prenatalData.isNotEmpty) {
      List<String> activities = List<String>.from(_prenatalData[0]['activities']);
      activities.add(name);
      _prenatalData[0]['activities'] = activities;
      return true;
    }
    
    return false;
  }

  /// 获取分组标题
  static String getGroupTitle(int startWeek, int endWeek) {
    return '孕$startWeek-${endWeek}周';
  }
}
