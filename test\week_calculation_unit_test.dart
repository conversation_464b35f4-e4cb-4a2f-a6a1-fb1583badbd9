import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Week Calculation Unit Tests', () {
    // 模拟修复后的计算逻辑
    DateTime normalizeDate(DateTime date) {
      return DateTime(date.year, date.month, date.day);
    }
    
    DateTime getWeekStart(DateTime date) {
      final weekday = date.weekday;
      final weekStart = date.subtract(Duration(days: weekday - 1));
      return normalizeDate(weekStart);
    }
    
    int getPageIndexForDate(DateTime date, DateTime baseDate, int initialPageIndex) {
      final baseWeekStart = getWeekStart(baseDate);
      final targetWeekStart = getWeekStart(date);
      final weekDiff = targetWeekStart.difference(baseWeekStart).inDays ~/ 7;
      return initialPageIndex + weekDiff;
    }
    
    DateTime getDateForPageIndex(int index, DateTime baseDate, int initialPageIndex) {
      final offset = index - initialPageIndex;
      final baseWeekStart = getWeekStart(baseDate);
      final targetDate = baseWeekStart.add(Duration(days: offset * 7));
      return normalizeDate(targetDate);
    }

    test('Should normalize dates correctly', () {
      final dateWithTime = DateTime(2024, 1, 17, 14, 30, 45);
      final normalized = normalizeDate(dateWithTime);
      
      expect(normalized.year, equals(2024));
      expect(normalized.month, equals(1));
      expect(normalized.day, equals(17));
      expect(normalized.hour, equals(0));
      expect(normalized.minute, equals(0));
      expect(normalized.second, equals(0));
      expect(normalized.millisecond, equals(0));
    });

    test('Should calculate week start correctly', () {
      // 测试不同星期几的日期
      final monday = DateTime(2024, 1, 15); // 周一
      final wednesday = DateTime(2024, 1, 17); // 周三
      final sunday = DateTime(2024, 1, 21); // 周日
      
      expect(getWeekStart(monday), equals(DateTime(2024, 1, 15)));
      expect(getWeekStart(wednesday), equals(DateTime(2024, 1, 15)));
      expect(getWeekStart(sunday), equals(DateTime(2024, 1, 15)));
    });

    test('Should calculate page index correctly for same week', () {
      final baseDate = DateTime.now();
      final normalizedBaseDate = normalizeDate(baseDate);
      final selectedDate = DateTime(2024, 1, 17); // 周三
      final initialPageIndex = 520;
      
      final pageIndex = getPageIndexForDate(selectedDate, normalizedBaseDate, initialPageIndex);
      final calculatedDate = getDateForPageIndex(pageIndex, normalizedBaseDate, initialPageIndex);
      
      // 验证计算出的日期与选中日期在同一周
      expect(getWeekStart(selectedDate), equals(getWeekStart(calculatedDate)));
    });

    test('Should handle cross-month weeks correctly', () {
      final baseDate = normalizeDate(DateTime.now());
      final initialPageIndex = 520;
      
      // 测试跨月的周：1月29日到2月4日
      final jan29 = DateTime(2024, 1, 29); // 周一
      final feb4 = DateTime(2024, 2, 4); // 周日
      
      final jan29PageIndex = getPageIndexForDate(jan29, baseDate, initialPageIndex);
      final feb4PageIndex = getPageIndexForDate(feb4, baseDate, initialPageIndex);
      
      // 同一周的不同日期应该计算出相同的页面索引
      expect(jan29PageIndex, equals(feb4PageIndex));
      
      final jan29Calculated = getDateForPageIndex(jan29PageIndex, baseDate, initialPageIndex);
      final feb4Calculated = getDateForPageIndex(feb4PageIndex, baseDate, initialPageIndex);
      
      // 验证计算出的日期都在同一周
      expect(getWeekStart(jan29), equals(getWeekStart(jan29Calculated)));
      expect(getWeekStart(feb4), equals(getWeekStart(feb4Calculated)));
      expect(getWeekStart(jan29Calculated), equals(getWeekStart(feb4Calculated)));
    });

    test('Should handle year boundaries correctly', () {
      final baseDate = normalizeDate(DateTime.now());
      final initialPageIndex = 520;
      
      // 测试跨年的情况
      final dec25_2023 = DateTime(2023, 12, 25); // 2023年12月25日，周一
      final jan1_2024 = DateTime(2024, 1, 1); // 2024年1月1日，周一
      
      final dec25PageIndex = getPageIndexForDate(dec25_2023, baseDate, initialPageIndex);
      final jan1PageIndex = getPageIndexForDate(jan1_2024, baseDate, initialPageIndex);
      
      // 验证页面索引差为1周
      expect(jan1PageIndex - dec25PageIndex, equals(1));
      
      final dec25Calculated = getDateForPageIndex(dec25PageIndex, baseDate, initialPageIndex);
      final jan1Calculated = getDateForPageIndex(jan1PageIndex, baseDate, initialPageIndex);
      
      // 验证计算正确性
      expect(getWeekStart(dec25_2023), equals(getWeekStart(dec25Calculated)));
      expect(getWeekStart(jan1_2024), equals(getWeekStart(jan1Calculated)));
    });

    test('Should be consistent with bidirectional calculation', () {
      final baseDate = normalizeDate(DateTime.now());
      final initialPageIndex = 520;
      
      // 测试多个不同的日期
      final testDates = [
        DateTime(2024, 1, 17), // 周三
        DateTime(2024, 2, 5), // 周一
        DateTime(2024, 3, 15), // 周五
        DateTime(2024, 12, 25), // 周三
      ];
      
      for (final date in testDates) {
        final pageIndex = getPageIndexForDate(date, baseDate, initialPageIndex);
        final calculatedDate = getDateForPageIndex(pageIndex, baseDate, initialPageIndex);
        
        // 验证双向计算的一致性
        expect(
          getWeekStart(date), 
          equals(getWeekStart(calculatedDate)),
          reason: 'Failed for date: $date'
        );
      }
    });
  });
}
