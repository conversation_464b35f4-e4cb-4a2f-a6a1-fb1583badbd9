import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:azlistview/azlistview.dart';
import '../../utils/text_util.dart';
import '../../utils/color_util.dart';
import '../../utils/toast_util.dart';
import '../../models/emotion_model.dart';

class EmotionSelectionBottomSheet extends StatefulWidget {
  final Function(String emotion)? onEmotionSelected;
  final Color color;
  final Color bgColor;

  const EmotionSelectionBottomSheet({
    super.key,
    this.onEmotionSelected,
    required this.color,
    required this.bgColor,
  });

  @override
  State<EmotionSelectionBottomSheet> createState() =>
      _EmotionSelectionBottomSheetState();
}

class _EmotionSelectionBottomSheetState
    extends State<EmotionSelectionBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<EmotionModel> _emotions = [];
  List<EmotionModel> _filteredEmotions = [];

  @override
  void initState() {
    super.initState();
    _emotions = EmotionDataManager.getAllEmotions();
    _filteredEmotions = _emotions;

    // 确保数据正确排序，以便AzListView能够正确识别分组
    SuspensionUtil.sortListBySuspensionTag(_filteredEmotions);
    SuspensionUtil.setShowSuspensionStatus(_filteredEmotions);

    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _filteredEmotions = EmotionDataManager.searchEmotions(
        _searchController.text,
      );
      // 确保数据正确排序，以便AzListView能够正确识别分组
      SuspensionUtil.sortListBySuspensionTag(_filteredEmotions);
      SuspensionUtil.setShowSuspensionStatus(_filteredEmotions);
    });
  }

  void _onEmotionTap(String emotion) {
    widget.onEmotionSelected?.call(emotion);
    Navigator.of(context).pop();
  }

  void _showAddEmotionDialog() {
    final TextEditingController controller = TextEditingController();

    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(
          '添加新情绪',
          style: TextUtil.base
              .sp(18)
              .semiBold
              .withColor(widget.color),
        ),
        content: Column(
          children: [
            SizedBox(height: 16.h),
            CupertinoTextField(
              controller: controller,
              placeholder: '请输入情绪词汇',
              placeholderStyle: TextUtil.base
                  .sp(16)
                  .withColor(CupertinoColors.systemGrey),
              style: TextUtil.base
                  .sp(16)
                  .withColor(widget.color),
              decoration: BoxDecoration(
                border: Border.all(
                  color: ColorUtil.borderGrey,
                  width: 1.w,
                ),
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text(
              '取消',
              style: TextUtil.base
                  .sp(16)
                  .withColor(CupertinoColors.systemGrey),
            ),
            onPressed: () {
              controller.dispose();
              Navigator.of(context).pop();
            },
          ),
          CupertinoDialogAction(
            child: Text(
              '确定',
              style: TextUtil.base
                  .sp(16)
                  .semiBold
                  .withColor(widget.color),
            ),
            onPressed: () => _addNewEmotion(controller),
          ),
        ],
      ),
    );
  }

  void _addNewEmotion(TextEditingController controller) {
    final emotion = controller.text.trim();

    // 验证输入不能为空
    if (emotion.isEmpty) {
      ToastUtil.showError('请输入情绪词汇');
      return;
    }

    // 验证不能重复
    if (EmotionDataManager.isEmotionExists(emotion)) {
      ToastUtil.showError('该情绪词汇已存在');
      return;
    }

    // 添加新情绪
    if (EmotionDataManager.addEmotion(emotion)) {
      // 更新列表
      setState(() {
        _emotions = EmotionDataManager.getAllEmotions();
        _filteredEmotions = EmotionDataManager.searchEmotions(_searchController.text);

        // 确保数据正确排序
        SuspensionUtil.sortListBySuspensionTag(_filteredEmotions);
        SuspensionUtil.setShowSuspensionStatus(_filteredEmotions);
      });

      // 关闭对话框
      controller.dispose();
      Navigator.of(context).pop();

      // 显示成功提示
      ToastUtil.showSuccess('添加成功');
    } else {
      ToastUtil.showError('添加失败，请重试');
    }
  }



  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白区域取消搜索框焦点并收起键盘
        FocusScope.of(context).unfocus();
      },
      child: SizedBox(
        height: 0.8.sh,
        child: Column(
          children: [
            // 标题栏
            _buildHeader(),

            // 搜索栏
            _buildSearchBar(),

            // 情绪列表
            Expanded(child: _buildEmotionList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
      child: Row(
        children: [
          // 左侧添加按钮
          GestureDetector(
            onTap: _showAddEmotionDialog,
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(CupertinoIcons.add, size: 20.sp, color: widget.color),
            ),
          ),

          // 中间标题
          Expanded(
            child: Center(
              child: Text(
                '添加情绪',
                style: TextUtil.base.sp(18).semiBold.withColor(widget.color),
              ),
            ),
          ),

          // 右侧关闭按钮
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                CupertinoIcons.xmark,
                size: 20.sp,
                color: widget.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: widget.bgColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        children: [
          Icon(CupertinoIcons.search, size: 20.sp, color: widget.color),
          SizedBox(width: 8.w),
          Expanded(
            child: CupertinoTextField(
              controller: _searchController,
              placeholder: '搜索情绪词汇',
              placeholderStyle: TextUtil.base
                  .sp(16)
                  .withColor(CupertinoColors.systemGrey),
              style: TextUtil.base.sp(16).withColor(widget.color),
              decoration: null,
              padding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmotionList() {
    if (_filteredEmotions.isEmpty) {
      return Center(
        child: Text(
          '未找到相关情绪词汇',
          style: TextUtil.base.sp(16).withColor(CupertinoColors.systemGrey),
        ),
      );
    }

    return Container(
      color: CupertinoColors.white,
      child: GestureDetector(
        onPanDown: (_) {
          // 开始滑动时取消搜索框焦点并收起键盘
          FocusScope.of(context).unfocus();
        },
        child: AzListView(
          data: _filteredEmotions,
          itemCount: _filteredEmotions.length,
          itemBuilder: (context, index) {
            final emotion = _filteredEmotions[index];
            final isFirstInGroup =
                index == 0 ||
                _filteredEmotions[index - 1].getSuspensionTag() !=
                    emotion.getSuspensionTag();

            final isLastInGroup =
                index == _filteredEmotions.length - 1 ||
                _filteredEmotions[index + 1].getSuspensionTag() !=
                    emotion.getSuspensionTag();

            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 如果是分组的第一个项目，显示分组标题
                  if (isFirstInGroup)
                    _buildSectionHeader(emotion.getSuspensionTag()),
                  // 情绪项目
                  _buildEmotionItem(emotion, isLastInGroup),
                ],
              ),
            );
          },
          indexBarData: _getIndexBarData(),
          indexBarOptions: IndexBarOptions(
            needRebuild: true,
            ignoreDragCancel: true,
            downTextStyle: TextUtil.base.sp(12).withColor(CupertinoColors.white),
            downItemDecoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.color,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String letter) {
    return Container(
      height: 40.h,
      margin: EdgeInsets.only(top: 16.h),
      alignment: Alignment.centerLeft,
      child: Text(
        letter,
        style: TextUtil.base.sp(16).semiBold.withColor(widget.color),
      ),
    );
  }

  Widget _buildEmotionItem(EmotionModel emotion, bool isLastInGroup) {
    return GestureDetector(
      onTap: () => _onEmotionTap(emotion.name),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4.h),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    emotion.name,
                    style: TextUtil.base.sp(16).withColor(widget.color),
                  ),
                ),
              ],
            ),
          ),
          // 如果不是分组中的最后一项，显示分隔线
          if (!isLastInGroup)
            Container(
              margin: EdgeInsets.fromLTRB(0, 4.h, 24.w, 0),
              height: 0.5.w,
              color: widget.color.withValues(alpha: 0.5),
            ),
        ],
      ),
    );
  }

  List<String> _getIndexBarData() {
    final Set<String> indexSet = {};
    for (final emotion in _filteredEmotions) {
      indexSet.add(emotion.getSuspensionTag());
    }
    final List<String> indexList = indexSet.toList();
    indexList.sort();
    return indexList;
  }
}
