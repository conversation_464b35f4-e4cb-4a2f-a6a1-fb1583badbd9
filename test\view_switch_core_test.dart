import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('SwipeableCalendar View Switch Core Tests', () {
    testWidgets('Should correctly calculate page index for different view formats', (WidgetTester tester) async {
      final testDate = DateTime(2024, 1, 15); // 2024年1月15日
      String? currentMonthTitle;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(currentMonthTitle, contains('2024年1月'));
      
      print('Month view initialized correctly');
    });

    testWidgets('Should handle week view initialization correctly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 3, 20); // 2024年3月20日（周三）
      String? currentMonthTitle;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    initialFormat: CustomCalendarFormat.week,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证周视图初始化
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      expect(currentMonthTitle, contains('2024年3月'));
      
      // 验证周视图显示包含3月20日的那一周
      // 3月20日是周三，所以这一周应该包含18-24日
      expect(find.text('18'), findsOneWidget); // 周一
      expect(find.text('19'), findsOneWidget); // 周二
      expect(find.text('20'), findsOneWidget); // 周三（选中的日期）
      expect(find.text('21'), findsOneWidget); // 周四
      expect(find.text('22'), findsOneWidget); // 周五
      
      print('Week view shows correct week containing selected date');
    });

    testWidgets('Should maintain state consistency across view switches', (WidgetTester tester) async {
      final testDate = DateTime(2024, 5, 10); // 2024年5月10日
      DateTime? selectedDay;
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentFormat, equals(CustomCalendarFormat.month));
      expect(currentMonthTitle, contains('2024年5月'));
      
      // 点击选择一个日期
      final dayFinder12 = find.text('12');
      if (tester.any(dayFinder12)) {
        await tester.tap(dayFinder12);
        await tester.pumpAndSettle();

        expect(selectedDay, isNotNull);
        expect(selectedDay!.day, equals(12));
        expect(selectedDay!.month, equals(5));
        expect(selectedDay!.year, equals(2024));
      }

      print('State consistency maintained');
    });

    testWidgets('Should handle year view correctly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 8, 15); // 2024年8月15日
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证年视图
      expect(currentFormat, equals(CustomCalendarFormat.year));
      expect(currentMonthTitle, contains('2024年8月'));
      
      // 验证年视图显示月份名称
      expect(find.text('八月'), findsOneWidget);
      expect(find.text('九月'), findsOneWidget);
      
      print('Year view works correctly');
    });

    testWidgets('Should handle _syncViewForSelectedDay method correctly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 2, 14); // 2024年2月14日（情人节）
      DateTime? selectedDay;
      String? currentMonthTitle;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {
                      selectedDay = day;
                    },
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    initialFormat: CustomCalendarFormat.month,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(currentMonthTitle, contains('2024年2月'));
      
      // 点击选择2月14日
      final dayFinder14 = find.text('14');
      if (tester.any(dayFinder14)) {
        await tester.tap(dayFinder14);
        await tester.pumpAndSettle();

        expect(selectedDay!.day, equals(14));
        expect(selectedDay!.month, equals(2));
      }

      // 验证同步方法的核心功能：正确的日期计算
      expect(selectedDay!.year, equals(2024));
      expect(selectedDay!.month, equals(2));
      expect(selectedDay!.day, equals(14));
      
      print('_syncViewForSelectedDay method works correctly');
    });
  });
}
