import 'package:align/widgets/app_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/color_util.dart';
import '../utils/text_util.dart';

// 自定义视图模式枚举
enum CustomCalendarFormat { week, month, year }

// 滑动方向枚举
enum SwipeDirection {
  vertical, // 垂直滑动（上下切换月份）
  horizontal, // 水平滑动（左右切换月份）
  both, // 支持两个方向
}

class SwipeableCalendar extends StatefulWidget {
  final DateTime? selectedDay;
  final Function(DateTime)? onDaySelected;
  final Function(String)? onMonthChanged; // 月份变化回调
  final Function(CustomCalendarFormat)? onViewChanged; // 视图变化回调
  final CustomCalendarFormat? initialFormat; // 初始视图格式
  final SwipeDirection swipeDirection; // 滑动方向控制
  final double monthViewHeight; // 月视图高度（已经过ScreenUtil处理的响应式值）
  final double yearViewHeight; // 年视图高度（已经过ScreenUtil处理的响应式值）

  const SwipeableCalendar({
    super.key,
    this.selectedDay,
    this.onDaySelected,
    this.onMonthChanged,
    this.onViewChanged,
    this.initialFormat,
    this.swipeDirection = SwipeDirection.vertical, // 默认垂直滑动
    this.monthViewHeight = 300.0, // 月视图默认高度（调用者应传入如300.h的值）
    this.yearViewHeight = 500.0, // 年视图默认高度（调用者应传入如500.h的值）
  });

  @override
  State<SwipeableCalendar> createState() => _SwipeableCalendarState();
}

class _SwipeableCalendarState extends State<SwipeableCalendar>
    with TickerProviderStateMixin {
  // 核心状态分离 - 参考table_calendar设计
  late DateTime _selectedDay; // 用户实际选中的日期，只有点击时才改变
  late DateTime _focusedDay; // 当前显示的月份/周，控制PageView显示
  late DateTime _baseDate; // 固定的基准日期，用于页面索引计算
  late CustomCalendarFormat _customFormat;
  late PageController _pageController;
  late AnimationController _viewSwitchAnimationController;
  late Animation<double> _heightAnimation;

  // 拖拽状态管理
  bool _isDragging = false;
  double _dragStartY = 0.0;
  static const double _dragThreshold = 20.0; // 拖拽阈值

  // 页面索引管理 - 使用大数值实现无限滚动
  static const int _initialPageIndex = 520; // 统一的初始页面索引

  // 视图切换动画状态
  bool _isAnimating = false;

  // 年视图月份点击标志位 - 防止回弹
  bool _isYearViewMonthClick = false;

  @override
  void initState() {
    super.initState();
    // 分离状态初始化 - 参考table_calendar设计
    final now = DateTime.now();
    _selectedDay = widget.selectedDay ?? _normalizeDate(now); // 用户选中的日期
    _focusedDay = _selectedDay; // 当前显示的日期，初始时与选中日期相同
    _baseDate = _normalizeDate(now); // 使用当前日期（不含时分秒）作为固定基准日期

    // 使用传入的初始格式，如果没有传入则默认为周视图
    _customFormat = widget.initialFormat ?? CustomCalendarFormat.week;

    // 计算基于焦点日期的初始页面索引
    final initialPageIndex = _getPageIndexForDate(_focusedDay);

    // 初始化PageController
    _pageController = PageController(initialPage: initialPageIndex);

    // 初始化动画控制器
    _viewSwitchAnimationController = AnimationController(
      duration: Duration(milliseconds: 400),
      vsync: this,
    );

    // 初始化高度动画
    _heightAnimation =
        Tween<double>(
          begin: _getViewHeight(_customFormat),
          end: _getViewHeight(_customFormat),
        ).animate(
          CurvedAnimation(
            parent: _viewSwitchAnimationController,
            curve: Curves.easeInOut,
          ),
        );

    // 通知初始视图状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notifyMonthChanged();
      widget.onViewChanged?.call(_customFormat);
    });
  }

  @override
  void didUpdateWidget(SwipeableCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 处理外部传入的initialFormat变化
    if (widget.initialFormat != null &&
        widget.initialFormat != _customFormat &&
        widget.initialFormat != oldWidget.initialFormat) {
      // 使用内部的视图切换机制，而不是重建组件
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _animateViewSwitch(widget.initialFormat!);
      });
    }

    // 处理selectedDay变化
    if (widget.selectedDay != null &&
        widget.selectedDay != oldWidget.selectedDay) {
      _selectedDay = widget.selectedDay!;
      // 如果不是年视图月份点击，则同步视图
      if (!_isYearViewMonthClick) {
        _syncViewForSelectedDay(_customFormat);
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _viewSwitchAnimationController.dispose();
    super.dispose();
  }

  // 获取不同视图模式的高度
  double _getViewHeight(CustomCalendarFormat format) {
    switch (format) {
      case CustomCalendarFormat.week:
        return 50.h; // 周视图保持固定高度处理
      case CustomCalendarFormat.month:
        // 月视图使用组件传入的高度（已经过ScreenUtil处理）
        return widget.monthViewHeight;
      case CustomCalendarFormat.year:
        // 年视图使用组件传入的高度（已经过ScreenUtil处理）
        return widget.yearViewHeight;
    }
  }

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 所有视图都使用AnimatedBuilder包装，支持平滑的高度动画过渡
          AnimatedBuilder(
            animation: _heightAnimation,
            builder: (context, child) {
              return ClipRect(
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 400),
                  curve: Curves.easeInOut,
                  height: _isAnimating
                      ? _heightAnimation.value
                      : _getViewHeight(_customFormat),
                  child: switch (_customFormat) {
                    CustomCalendarFormat.year => _buildYearView(),
                    CustomCalendarFormat.week => _buildWeekView(),
                    CustomCalendarFormat.month => _buildMonthView(),
                  },
                ),
              );
            },
          ),
          // 只在非年视图时显示间距和切换按钮
          if (_customFormat != CustomCalendarFormat.year) ...[
            SizedBox(height: 8.h),
            _buildViewSwitchBar(),

          ],
        ],
      ),
    );
  }

  Widget _buildWeekView() {
    return SizedBox(
      height: 50.h, // 周视图固定高度
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.horizontal, // 周视图始终使用水平滑动
        onPageChanged: _onPageChanged,
        itemBuilder: (context, index) {
          final targetDate = _getDateForPageIndex(
            index,
            CustomCalendarFormat.week,
          );
          return _buildWeekCalendar(targetDate);
        },
      ),
    );
  }

  Widget _buildMonthView() {
    // 获取月视图容器的高度（已经过ScreenUtil处理）
    final containerHeight = widget.monthViewHeight;
    final weekdayHeaderHeight = 20.h; // 星期标题行高度
    final headerSpacing = 8.h; // 标题下方间距
    final availableCalendarHeight = containerHeight - weekdayHeaderHeight - headerSpacing;

    return SizedBox(
      height: containerHeight,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 固定的星期标题
          SizedBox(
            height: weekdayHeaderHeight,
            child: _buildWeekdayHeaders(),
          ),
          SizedBox(height: headerSpacing),
          // 可滑动的月份日历
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              scrollDirection: _getMonthViewScrollDirection(), // 月视图根据设置决定滑动方向
              onPageChanged: _onPageChanged,
              itemBuilder: (context, index) {
                final targetDate = _getDateForPageIndex(
                  index,
                  CustomCalendarFormat.month,
                );
                return _buildMonthCalendar(targetDate, containerHeight: availableCalendarHeight);
              },
            ),
          ),
        ],
      ),
    );
  }

  // 获取月视图的滚动方向
  Axis _getMonthViewScrollDirection() {
    switch (widget.swipeDirection) {
      case SwipeDirection.vertical:
        return Axis.vertical;
      case SwipeDirection.horizontal:
        return Axis.horizontal;
      case SwipeDirection.both:
        return Axis.vertical; // 默认使用垂直方向，可以通过手势检测支持两个方向
    }
  }

  // 获取年视图的滚动方向
  Axis _getYearViewScrollDirection() {
    switch (widget.swipeDirection) {
      case SwipeDirection.vertical:
        return Axis.vertical;
      case SwipeDirection.horizontal:
        return Axis.horizontal;
      case SwipeDirection.both:
        return Axis.horizontal; // 年视图默认使用水平方向，更符合年份切换的直觉
    }
  }

  // 统一的日期计算方法
  DateTime _getDateForPageIndex(int index, CustomCalendarFormat format) {
    final offset = index - _initialPageIndex;

    switch (format) {
      case CustomCalendarFormat.week:
        // 周视图：按周计算
        final baseWeekStart = _getWeekStart(_baseDate);
        final targetDate = baseWeekStart.add(Duration(days: offset * 7));
        return _normalizeDate(targetDate); // 确保返回标准化日期
      case CustomCalendarFormat.month:
        // 月视图：按月计算
        return DateTime(_baseDate.year, _baseDate.month + offset, 1);
      case CustomCalendarFormat.year:
        // 年视图：按年计算
        return DateTime(_baseDate.year + offset, _baseDate.month, 1);
    }
  }

  // 统一的页面索引计算方法
  int _getPageIndexForDate(DateTime date, [CustomCalendarFormat? format]) {
    final targetFormat = format ?? _customFormat;
    switch (targetFormat) {
      case CustomCalendarFormat.week:
        final baseWeekStart = _getWeekStart(_baseDate);
        final targetWeekStart = _getWeekStart(date);
        final weekDiff = targetWeekStart.difference(baseWeekStart).inDays ~/ 7;
        return _initialPageIndex + weekDiff;
      case CustomCalendarFormat.month:
        final monthDiff =
            (date.year - _baseDate.year) * 12 + (date.month - _baseDate.month);
        return _initialPageIndex + monthDiff;
      case CustomCalendarFormat.year:
        final yearDiff = date.year - _baseDate.year;
        return _initialPageIndex + yearDiff;
    }
  }

  // 页面变化回调 - 关键修复：只更新focusedDay，不影响selectedDay
  void _onPageChanged(int index) {
    print('called _onPageChanged index $index, isYearViewMonthClick: $_isYearViewMonthClick');

    // 如果是年视图月份点击，跳过自动更新（标志位在_onYearViewMonthTapped中处理）
    if (_isYearViewMonthClick) {
      print('Skipping _onPageChanged due to year view month click');
      return;
    }

    setState(() {
      // 只更新显示的焦点日期，不改变用户选中的日期
      _focusedDay = _getDateForPageIndex(index, _customFormat);
    });
    _notifyMonthChanged();
  }

  // 构建周视图日历
  Widget _buildWeekCalendar(DateTime targetDate) {
    // 获取目标日期所在周的开始日期（周一）
    final weekStart = _getWeekStart(targetDate);

    return Row(
      children: List.generate(7, (index) {
        final date = weekStart.add(Duration(days: index));
        return Expanded(child: _buildDayCell(date, isWeekView: true));
      }),
    );
  }

  // 构建月视图日历
  Widget _buildMonthCalendar(DateTime targetDate, {required double containerHeight}) {
    final firstDayOfMonth = DateTime(targetDate.year, targetDate.month, 1);
    final firstDayWeekday = firstDayOfMonth.weekday;

    // 计算日历网格的开始日期（包含上个月的日期）
    final calendarStart = firstDayOfMonth.subtract(
      Duration(days: firstDayWeekday - 1),
    );

    // 动态计算月视图布局（containerHeight现在是可用于日期网格的高度）
    final availableGridHeight = containerHeight;

    // 动态计算该月需要的行数
    final daysInMonth = DateTime(targetDate.year, targetDate.month + 1, 0).day;
    final emptyDays = firstDayWeekday - 1; // 月初空白天数（使用已定义的firstDayWeekday）
    final totalCells = emptyDays + daysInMonth; // 总单元格数
    final rows = (totalCells / 7).ceil(); // 实际需要的行数（4-6行）
    final columns = 7; // 7列（一周7天）

    // 保守的高度计算策略，确保不会溢出
    final minCellHeight = 15.h; // 最小单元格高度
    final minMainAxisSpacing = 1.h; // 最小行间距
    final maxMainAxisSpacing = 6.h; // 最大行间距

    // 计算理想的单元格高度（不考虑间距）
    final idealCellHeight = availableGridHeight / rows;

    double cellHeight;
    double mainAxisSpacing;

    if (idealCellHeight >= minCellHeight + maxMainAxisSpacing) {
      // 空间充足，可以使用较大的间距
      final totalMaxSpacing = maxMainAxisSpacing * (rows - 1);
      cellHeight = (availableGridHeight - totalMaxSpacing) / rows;
      mainAxisSpacing = maxMainAxisSpacing;
    } else if (idealCellHeight >= minCellHeight + minMainAxisSpacing) {
      // 空间有限，使用最小间距
      final totalMinSpacing = minMainAxisSpacing * (rows - 1);
      cellHeight = (availableGridHeight - totalMinSpacing) / rows;
      mainAxisSpacing = minMainAxisSpacing;
    } else {
      // 空间非常有限，优先保证内容显示
      cellHeight = minCellHeight;
      final remainingHeight = availableGridHeight - (cellHeight * rows);
      mainAxisSpacing = rows > 1 ? (remainingHeight / (rows - 1)).clamp(0.h, minMainAxisSpacing) : 0.h;
    }

    final crossAxisSpacing = 1.w; // 列间距保持最小值

    return GridView.builder(
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisExtent: cellHeight, // 使用计算出的每行高度
      ),
      itemCount: rows * columns, // 动态计算的行数 * 7天
      itemBuilder: (context, index) {
        final date = calendarStart.add(Duration(days: index));
        final isCurrentMonth = date.month == targetDate.month;
        return _buildDayCell(date, isCurrentMonth: isCurrentMonth);
      },
    );
  }

  // 构建星期标题
  Widget _buildWeekdayHeaders() {
    final weekdays = ['一', '二', '三', '四', '五', '六', '日'];
    return Row(
      children: weekdays
          .map(
            (day) => Expanded(
              child: Center(
                child: Text(
                  day,
                  style: TextUtil.base
                      .sp(14)
                      .withColor(CupertinoColors.inactiveGray),
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  // 构建日期单元格
  Widget _buildDayCell(
    DateTime date, {
    bool isCurrentMonth = true,
    bool isWeekView = false,
  }) {
    // 使用selectedDay来判断选中状态，而不是focusedDay
    final isSelected = _isSameDay(date, _selectedDay);
    final isToday = _isSameDay(date, DateTime.now());

    return GestureDetector(
      onTap: () => _onDayTapped(date),
      child: Container(
        margin: isWeekView
            ? EdgeInsets.symmetric(horizontal: 4.w)
            : EdgeInsets.all(2.w),
        decoration: BoxDecoration(
          color: isSelected
              ? ColorUtil.primaryBgColor
              : (isToday
                    ? ColorUtil.primaryBgColor.withValues(alpha: 0.3)
                    : null),
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            '${date.day}',
            style: TextUtil.base
                .sp(16)
                .withColor(
                  isSelected
                      ? CupertinoColors.white
                      : (isCurrentMonth
                            ? (isToday
                                  ? CupertinoColors.black
                                  : CupertinoColors.black)
                            : CupertinoColors.inactiveGray),
                )
                .copyWith(
                  fontWeight: isSelected || isToday
                      ? FontWeight.w600
                      : FontWeight.normal,
                ),
          ),
        ),
      ),
    );
  }

  // 日期点击处理 - 关键修复：同时更新selectedDay和focusedDay
  void _onDayTapped(DateTime date) {
    setState(() {
      _selectedDay = date; // 更新用户选中的日期
      _focusedDay = date; // 同时更新显示焦点
    });

    // 更新PageController到正确的页面
    _updatePageControllerForDate(date);

    // 只有在用户点击时才触发回调
    widget.onDaySelected?.call(date);
  }

  // 根据选中的日期更新PageController位置
  void _updatePageControllerForDate(DateTime date) {
    final newPageIndex = _getPageIndexForDate(date);

    if (_pageController.hasClients) {
      _pageController.jumpToPage(newPageIndex);
    }
  }

  // 视图切换时同步选中日期的显示
  void _syncViewForSelectedDay(CustomCalendarFormat newFormat) {
    // 如果是年视图月份点击，跳过基于selectedDay的同步，避免回弹
    if (_isYearViewMonthClick) {
      return;
    }

    // 计算在新视图格式下，包含selectedDay的页面索引
    final targetPageIndex = _getPageIndexForDate(_selectedDay, newFormat);
    // 更新focusedDay为新视图格式下的正确日期
    switch (newFormat) {
      case CustomCalendarFormat.week:
        // 周视图：focusedDay应该是包含selectedDay的周的开始日期
        _focusedDay = _getWeekStart(_selectedDay);
        break;
      case CustomCalendarFormat.month:
        // 月视图：focusedDay应该是包含selectedDay的月的第一天
        _focusedDay = DateTime(_selectedDay.year, _selectedDay.month, 1);
        break;
      case CustomCalendarFormat.year:
        // 年视图：focusedDay保持为selectedDay
        _focusedDay = _selectedDay;
        break;
    }

    // 更新PageController到正确的页面
    if (_pageController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_pageController.hasClients) {
          _pageController.jumpToPage(targetPageIndex);
        }
      });
    }
  }

  // 获取周的开始日期（周一）
  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    final weekStart = date.subtract(Duration(days: weekday - 1));
    return _normalizeDate(weekStart); // 确保返回标准化日期
  }

  // 标准化日期，移除时分秒信息
  DateTime _normalizeDate(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  // 判断两个日期是否为同一天
  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  // 年视图 - 基于PageView的动态年份切换
  Widget _buildYearView() {
    // 获取年视图容器的固定高度（已经过ScreenUtil处理）
    final containerHeight = widget.yearViewHeight;

    return SizedBox(
      height: containerHeight,
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: _getYearViewScrollDirection(), // 年视图根据设置决定滑动方向
        onPageChanged: _onPageChanged,
        itemBuilder: (context, index) {
          final targetDate = _getDateForPageIndex(
            index,
            CustomCalendarFormat.year,
          );
          return _buildYearCalendar(targetDate, containerHeight: containerHeight);
        },
      ),
    );
  }

  // 构建单个年份的日历页面
  Widget _buildYearCalendar(DateTime targetDate, {required double containerHeight}) {
    final now = DateTime.now();
    final currentYear = targetDate.year; // 使用targetDate来显示年份

    // 动态计算GridView中每个月份项目的高度
    final verticalPadding = 8.h * 2; // 上下内边距
    final crossAxisSpacing = 12.w; // 列间距
    final mainAxisSpacing = 16.h; // 行间距
    final rows = 4; // 4行（12个月份 ÷ 3列 = 4行）
    final columns = 3; // 3列

    // 计算可用高度：总高度 - 内边距 - (行间距 × 行数-1)
    final availableHeight = containerHeight - verticalPadding - (mainAxisSpacing * (rows - 1));

    // 每个月份项目的高度 = 可用高度 ÷ 行数
    final itemHeight = availableHeight / rows;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(), // 禁用GridView自身的滚动
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns, // 3列
          crossAxisSpacing: crossAxisSpacing,
          mainAxisSpacing: mainAxisSpacing,
          mainAxisExtent: itemHeight, // 使用固定高度替代childAspectRatio
        ),
        itemCount: 12,
        itemBuilder: (context, index) {
          final month = index + 1;
          return _buildSimpleMonthCalendar(currentYear, month, now, itemHeight: itemHeight);
        },
      ),
    );
  }

  // 构建简化的月份日历
  Widget _buildSimpleMonthCalendar(int year, int month, DateTime now, {required double itemHeight}) {
    final isCurrentMonth = (year == now.year && month == now.month);

    // 月份名称
    final monthNames = [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ];

    // 根据itemHeight计算内部布局
    final containerPadding = 4.h * 2; // 上下内边距
    final titleHeight = 20.h; // 月份标题高度（估算）
    final titleSpacing = 8.h; // 标题下方间距
    final availableGridHeight = itemHeight - containerPadding - titleHeight - titleSpacing;

    return GestureDetector(
      onTap: () => _onYearViewMonthTapped(year, month),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: CupertinoColors.white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          children: [
            // 1. 月份标题部分
            SizedBox(
              height: titleHeight,
              child: Center(
                child: Text(
                  monthNames[month - 1],
                  style: TextUtil.base
                      .sp(14)
                      .medium
                      .withColor(
                        isCurrentMonth
                            ? ColorUtil.primaryBgColor
                            : CupertinoColors.black,
                      ),
                ),
              ),
            ),
            SizedBox(height: titleSpacing),
            // 2. 日期网格部分
            SizedBox(
              height: availableGridHeight,
              child: _buildMonthDateGrid(year, month, now, gridHeight: availableGridHeight),
            ),
          ],
        ),
      ),
    );
  }

  void _onYearViewMonthTapped(int year, int month) {
    try {
      final targetDate = DateTime(year, month, 1);

      // 设置年视图月份点击标志位，防止回弹
      _isYearViewMonthClick = true;

      // 先更新focusedDay到目标月份
      _focusedDay = targetDate;

      // 计算目标页面索引
      final targetPageIndex = _getPageIndexForDate(
        targetDate,
        CustomCalendarFormat.month,
      );

      // 使用内部的视图切换机制，确保平滑过渡
      setState(() {
        _customFormat = CustomCalendarFormat.month;
      });

      // 在下一帧跳转到目标页面，确保视图已经切换完成
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_pageController.hasClients) {
          _pageController.jumpToPage(targetPageIndex);
        }

        // 通知变化
        _notifyMonthChanged();
        widget.onViewChanged?.call(_customFormat);

        // 重置标志位
        _isYearViewMonthClick = false;
      });

    } catch (e) {
      print('Error in month tap: $e');
      _isYearViewMonthClick = false; // 确保在错误情况下也重置标志位
    }
  }

  // 构建月份日期网格
  Widget _buildMonthDateGrid(int year, int month, DateTime now, {double? gridHeight}) {
    // 获取该月的天数
    final daysInMonth = DateTime(year, month + 1, 0).day;
    // 获取该月第一天是星期几（1=Monday, 7=Sunday）
    final firstDayWeekday = DateTime(year, month, 1).weekday;
    // 计算需要的空白占位符数量（周一为第一天）
    final emptyDays = firstDayWeekday - 1;

    // 计算需要的行数（最多6行）
    final totalCells = emptyDays + daysInMonth;
    final rows = (totalCells / 7).ceil();

    // 根据gridHeight计算合适的行间距
    double mainAxisSpacing = 4.h; // 默认行间距
    if (gridHeight != null && rows > 1) {
      // 计算每行的理想高度
      final availableSpacing = gridHeight - (rows * 12.h); // 假设每行高度约12.h
      final maxSpacing = availableSpacing / (rows - 1);
      mainAxisSpacing = maxSpacing.clamp(2.h, 6.h); // 限制行间距范围
    }

    // 创建日期列表（包含空白占位符）
    final List<Widget> dayWidgets = [];

    // 添加空白占位符
    for (int i = 0; i < emptyDays; i++) {
      dayWidgets.add(Container());
    }

    // 添加实际日期
    for (int day = 1; day <= daysInMonth; day++) {
      final isToday =
          (year == now.year && month == now.month && day == now.day);

      dayWidgets.add(
        Container(
          decoration: isToday
              ? BoxDecoration(
                  color: ColorUtil.primaryBgColor,
                  shape: BoxShape.circle,
                )
              : null,
          child: Center(
            child: Text(
              '$day',
              style: TextUtil.base
                  .sp(8)
                  .withColor(
                    isToday ? CupertinoColors.white : CupertinoColors.black,
                  ),
            ),
          ),
        ),
      );
    }

    return GridView.count(
      crossAxisCount: 7, // 7列（一周7天）
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      mainAxisSpacing: mainAxisSpacing, // 使用计算出的行间距
      children: dayWidgets,
    );
  }

  // 视图切换按钮
  Widget _buildViewSwitchBar() {
    return Center(
      child: GestureDetector(
        onPanStart: (details) {
          _dragStartY = details.globalPosition.dy;
          setState(() {
            _isDragging = true;
          });
        },
        onPanUpdate: (details) {
          if (!_isDragging) return;

          final deltaY = details.globalPosition.dy - _dragStartY;

          if (deltaY.abs() > _dragThreshold) {
            _handleDragGesture(deltaY);
          }
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
          });
        },
        child: Container(
          width: 30.w,
          height: 5.h,
          decoration: BoxDecoration(
            color: _isDragging
                ? CupertinoColors.systemGrey
                : CupertinoColors.systemGrey2,
            borderRadius: BorderRadius.circular(2.5.r),
            boxShadow: _isDragging
                ? [
                    BoxShadow(
                      color: CupertinoColors.systemGrey.withOpacity(0.3),
                      blurRadius: 4.r,
                      offset: Offset(0, 2.h),
                    ),
                  ]
                : null,
          ),
        ),
      ),
    );
  }

  // 处理拖拽手势
  void _handleDragGesture(double deltaY) {
    setState(() {
      _isDragging = false; // 重置拖拽状态，防止重复触发
    });

    if (deltaY < 0) {
      // 向下拖拽：视图级别降低
      _switchToLowerView();
    } else {
      // 向上拖拽：视图级别提升
      _switchToUpperView();
    }
  }

  // 切换到上级视图
  void _switchToUpperView() {
    CustomCalendarFormat newFormat = _customFormat;
    if (_customFormat == CustomCalendarFormat.week) {
      newFormat = CustomCalendarFormat.month;
    } else if (_customFormat == CustomCalendarFormat.month) {
      newFormat = CustomCalendarFormat.year;
    }

    if (newFormat != _customFormat) {
      _animateViewSwitch(newFormat);
    }
  }

  // 切换到下级视图
  void _switchToLowerView() {
    CustomCalendarFormat newFormat = _customFormat;
    if (_customFormat == CustomCalendarFormat.year) {
      newFormat = CustomCalendarFormat.month;
    } else if (_customFormat == CustomCalendarFormat.month) {
      newFormat = CustomCalendarFormat.week;
    }

    if (newFormat != _customFormat) {
      _animateViewSwitch(newFormat);
    }
  }

  // 执行视图切换动画
  void _animateViewSwitch(CustomCalendarFormat newFormat) async {
    print('called _animateViewSwitch from $_customFormat to $newFormat');
    if (_isAnimating) return;

    setState(() {
      _isAnimating = true;
    });

    // 所有视图切换都使用高度动画，包括年视图
    _heightAnimation =
        Tween<double>(
          begin: _getViewHeight(_customFormat),
          end: _getViewHeight(newFormat),
        ).animate(
          CurvedAnimation(
            parent: _viewSwitchAnimationController,
            curve: Curves.easeInOut,
          ),
        );

    // 启动动画
    await _viewSwitchAnimationController.forward(from: 0.0);

    // 更新视图格式
    setState(() {
      _customFormat = newFormat;
      _isAnimating = false;
    });

    // 重置动画控制器
    _viewSwitchAnimationController.reset();

    // 同步视图状态
    _syncViewForSelectedDay(newFormat);


    // 通知变化
    _notifyMonthChanged();
    widget.onViewChanged?.call(_customFormat);
  }

  void _notifyMonthChanged() {
    // 根据当前视图格式决定显示内容
    String displayText;
    switch (_customFormat) {
      case CustomCalendarFormat.year:
        // 年视图只显示年份
        displayText = '${_focusedDay.year}年';
        break;
      case CustomCalendarFormat.month:
      case CustomCalendarFormat.week:
        // 月视图和周视图显示年月
        displayText = '${_focusedDay.year}年${_focusedDay.month}月';
        break;
    }
    widget.onMonthChanged?.call(displayText);
  }

  // 用于测试的公共方法：手动切换视图
  @visibleForTesting
  void switchToFormat(CustomCalendarFormat format) {
    if (format != _customFormat) {
      _animateViewSwitch(format);
    }
  }
}
