import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/color_util.dart';
import '../utils/text_util.dart';
import '../utils/toast_util.dart';
import '../models/user_models.dart';
import '../services/auth_service.dart';
import '../utils/sp_util.dart';
import 'main_page.dart';

/// 预产期设置页面
class DueDateSetupPage extends StatefulWidget {
  const DueDateSetupPage({super.key});

  @override
  State<DueDateSetupPage> createState() => _DueDateSetupPageState();
}

class _DueDateSetupPageState extends State<DueDateSetupPage> {
  late DateTime _selectedDate;
  late DateTime _minimumDate;
  late DateTime _maximumDate;
  bool _isLoading = false;
  int _selectedMethod = 0; // 0: 预产期, 1: 末次月经第一天

  @override
  void initState() {
    super.initState();
    // 设置日期范围，确保时间一致性
    final now = DateTime.now();
    _minimumDate = DateTime(now.year - 1, now.month, now.day); // 一年前到现在
    _maximumDate = DateTime(now.year + 1, now.month, now.day); // 一年后的同一天
    _selectedDate = _minimumDate.add(const Duration(days: 280)); // 默认40周后

    // 确保选择的日期在有效范围内
    if (_selectedDate.isAfter(_maximumDate)) {
      _selectedDate = _maximumDate;
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: const Color(0xFFF4C2C2), // 粉色背景
      child: SafeArea(
        child: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 40.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 标题
                Text(
                  'Align',
                  style: TextUtil.base.sp(34).semiBold.withColor(CupertinoColors.black),
                ),

                SizedBox(height: 40.h),

                // 主要内容卡片
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(24.w),
                  decoration: BoxDecoration(
                    color: CupertinoColors.white,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Column(
                    children: [
                      // 方法选择标题
                      Row(
                        children: [
                          _buildMethodTab('预产期', 0),
                          Spacer(),
                          _buildMethodTab('末次月经第一天', 1),
                        ],
                      ),

                      SizedBox(height: 20.h),

                      // 日期选择器容器
                      Container(
                        height: 120.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF4C2C2).withOpacity(0.3),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: _selectedDate,
                          minimumDate: _minimumDate,
                          maximumDate: _maximumDate,
                          onDateTimeChanged: (DateTime newDate) {
                            setState(() {
                              _selectedDate = newDate;
                            });
                          },
                          dateOrder: DatePickerDateOrder.ymd,
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 40.h),

                // 确认按钮
                SizedBox(
                  width: double.infinity,
                  child: CupertinoButton(
                    onPressed: _isLoading ? null : _confirmDueDate,
                    color: CupertinoColors.white,
                    borderRadius: BorderRadius.circular(25.r),
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: _isLoading
                        ? const CupertinoActivityIndicator()
                        : Text(
                            '确认',
                            style: TextUtil.base.sp(18).semiBold.withColor(CupertinoColors.black),
                          ),
                  ),
                ),

              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建方法选择标签
  Widget _buildMethodTab(String title, int index) {
    final bool isSelected = _selectedMethod == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedMethod = index;
          // 根据选择的方法调整日期范围和默认值
          _updateDateConstraints();
        });
      },
      child: Column(
        children: [
          Text(
            title,
            style: isSelected
                ? TextUtil.base.sp(18).semiBold.withColor(CupertinoColors.black)
                : TextUtil.base.sp(18).withColor(CupertinoColors.systemGrey),
          ),
          SizedBox(height: 8.h),
          if (isSelected)
            Container(
              height: 2.h,
              width: title.length * 9.w, // 根据文字长度调整下划线宽度
              color: CupertinoColors.black,
            )
          else
            Container(height: 2.h),
        ],
      ),
    );
  }

  /// 根据选择的方法更新日期约束
  void _updateDateConstraints() {
    final now = DateTime.now();

    if (_selectedMethod == 0) {
      // 预产期方法：可以选择未来的日期
      _minimumDate = DateTime(now.year, now.month, now.day);
      _maximumDate = DateTime(now.year + 1, now.month, now.day);
      _selectedDate = _minimumDate.add(const Duration(days: 280)); // 默认40周后
    } else {
      // 末次月经第一天方法：可以选择过去的日期
      _minimumDate = DateTime(now.year - 1, now.month, now.day); // 一年前
      _maximumDate = DateTime(now.year, now.month, now.day); // 今天
      _selectedDate = _maximumDate.subtract(const Duration(days: 280)); // 默认40周前
    }

    // 确保选择的日期在有效范围内
    if (_selectedDate.isBefore(_minimumDate)) {
      _selectedDate = _minimumDate;
    }
    if (_selectedDate.isAfter(_maximumDate)) {
      _selectedDate = _maximumDate;
    }
  }

  /// 计算预产期（根据末次月经第一天）
  DateTime _calculateDueDateFromLMP(DateTime lmpDate) {
    return lmpDate.add(const Duration(days: 280)); // 280天 = 40周
  }

  /// 确认预产期设置
  Future<void> _confirmDueDate() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 获取当前用户信息
      final currentUser = await AuthService.instance.getCurrentUser();
      if (currentUser == null) {
        ToastUtil.showError('用户信息获取失败');
        return;
      }

      // 计算最终的预产期
      DateTime finalDueDate;
      if (_selectedMethod == 0) {
        // 直接选择的预产期
        finalDueDate = _selectedDate;
      } else {
        // 根据末次月经第一天计算预产期
        finalDueDate = _calculateDueDateFromLMP(_selectedDate);
      }

      // 更新用户信息，添加预产期
      final updatedUser = currentUser.copyWith(
        dueDate: finalDueDate.toIso8601String(),
      );

      // 保存到本地存储
      final success = await SpUtil.saveUserInfo(updatedUser);
      if (success) {
        // 更新认证服务中的用户信息
        await AuthService.instance.updateUserInfo(updatedUser);

        ToastUtil.showSuccess('预产期设置成功');

        // 跳转到主页面
        if (mounted) {
          Navigator.of(context).pushReplacement(
            CupertinoPageRoute(
              builder: (context) => const MainPage(),
            ),
          );
        }
      } else {
        ToastUtil.showError('预产期设置失败');
      }
    } catch (e) {
      ToastUtil.showError('设置过程中发生错误');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
