import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import '../utils/text_util.dart';
import '../utils/sp_util.dart';
import '../models/models.dart';
import '../widgets/category_list_view.dart';
import '../widgets/symptom_content_widget.dart';
import '../widgets/symptom_bottom_sheets/emotion_selection_bottom_sheet.dart';
import '../widgets/symptom_bottom_sheets/food_add_bottom_sheet.dart';
import '../widgets/symptom_bottom_sheets/weight_add_bottom_sheet.dart';
import '../widgets/symptom_bottom_sheets/medicine_add_bottom_sheet.dart';

class SymptomPage extends StatefulWidget {
  final Color cardColor;
  final Color cardBgColor;

  const SymptomPage({
    super.key,
    required this.cardColor,
    required this.cardBgColor,
  });

  @override
  State<SymptomPage> createState() => _SymptomPageState();
}

class _SymptomPageState extends State<SymptomPage> {
  late SymptomData symptomData;
  int selectedCategoryIndex = 0;
  final Set<String> _selectedSymptoms = {}; // 选中的症状标签集合

  // 日期相关状态
  DateTime _selectedDate = DateTime.now();
  UserModel? _userInfo;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
    // 初始化示例数据
    final dataJson = {
      'symptom': [
        {
          'title': '心情',
          'type': 1,
          'content': [
            {'description': '开心'},
            {'description': '惊喜'},
            {'description': '放松'},
            {'description': '满足'},
            {'description': '心动'},
            {'description': '自信'},
          ],
        },
        {
          'title': '食欲',
          'type': 2,
          'content': [
            {'name': '苹果', 'weight': '200g'},
            {'name': '香蕉', 'weight': '150g'},
          ],
        },
        {
          'title': '体重',
          'type': 3,
          'content': [
            {'weight': '65kg', 'unit': 'kg', 'time': '2025-07-25'},
            {'weight': '66kg', 'unit': 'kg', 'time': '2025-07-26'},
          ],
        },
        {
          'title': '药物',
          'type': 4,
          'content': [
            {
              'medicine_name': '阿莫西林',
              'quantity': '500mg',
              'unit': 'mg',
              'time': '2025-07-25',
              'cycle': [7],
            },
            {
              'medicine_name': '布洛芬',
              'quantity': '200mg',
              'unit': 'mg',
              'time': '2025-07-26',
              'cycle': [5],
            },
          ],
        },
        {
          'title': '体重12312312311',
          'type': 3,
          'content': [
            {'weight': '65kg', 'unit': 'kg', 'time': '2025-07-25'},
            {'weight': '66kg', 'unit': 'kg', 'time': '2025-07-26'},
          ],
        },
        {
          'title': '体重2',
          'type': 3,
          'content': [
            {'weight': '65kg', 'unit': 'kg', 'time': '2025-07-25'},
            {'weight': '66kg', 'unit': 'kg', 'time': '2025-07-26'},
          ],
        },
        {
          'title': '体重3',
          'type': 3,
          'content': [
            {'weight': '65kg', 'unit': 'kg', 'time': '2025-07-25'},
            {'weight': '66kg', 'unit': 'kg', 'time': '2025-07-26'},
          ],
        },
        {
          'title': '体重4',
          'type': 3,
          'content': [
            {'weight': '65kg', 'unit': 'kg', 'time': '2025-07-25'},
            {'weight': '66kg', 'unit': 'kg', 'time': '2025-07-26'},
          ],
        },
        {
          'title': '体重5',
          'type': 3,
          'content': [
            {'weight': '65kg', 'unit': 'kg', 'time': '2025-07-25'},
            {'weight': '66kg', 'unit': 'kg', 'time': '2025-07-26'},
          ],
        },
      ],
    };
    symptomData = SymptomData.fromJson(dataJson);
  }

  // 加载用户信息
  Future<void> _loadUserInfo() async {
    final userInfo = await SpUtil.getUserInfo();
    if (mounted) {
      setState(() {
        _userInfo = userInfo;
      });
    }
  }

  // 切换到上一天
  void _goToPreviousDay() {
    setState(() {
      _selectedDate = _selectedDate.subtract(const Duration(days: 1));
    });
  }

  // 切换到下一天
  void _goToNextDay() {
    setState(() {
      _selectedDate = _selectedDate.add(const Duration(days: 1));
    });
  }

  // 格式化日期显示
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return '今天';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return '昨天';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return '明天';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  // 格式化完整日期
  String _formatFullDate(DateTime date) {
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[date.weekday - 1];
    return '${date.year}年${date.month}月${date.day}日（$weekday）';
  }

  // 计算孕周信息
  String _getPregnancyWeekInfo() {
    if (_userInfo?.pregnancyWeeks == null) {
      return '15周 3天'; // 默认显示
    }

    final weeks = _userInfo!.pregnancyWeeks!;
    return '${weeks['weeks']}周 ${weeks['days']}天';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(top: 16.h),
          child: Center(
            child: Container(
              width: 36.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: widget.cardColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
        ),

        // 标题区域 - 水平布局
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 左箭头
              GestureDetector(
                onTap: _goToPreviousDay,
                child: Icon(
                  CupertinoIcons.chevron_left,
                  size: 20.sp,
                  color: widget.cardColor,
                ),
              ),

              // 中间标题内容
              Expanded(
                child: Column(
                  children: [
                    Text(
                      _formatDate(_selectedDate),
                      style: TextUtil.base
                          .sp(24)
                          .semiBold
                          .withColor(widget.cardColor),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      _formatFullDate(_selectedDate),
                      style: TextUtil.base.sp(14).withColor(widget.cardColor),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      _getPregnancyWeekInfo(),
                      style: TextUtil.base
                          .sp(12)
                          .withColor(CupertinoColors.systemGrey),
                    ),
                  ],
                ),
              ),

              // 右箭头
              GestureDetector(
                onTap: _goToNextDay,
                child: Icon(
                  CupertinoIcons.chevron_right,
                  size: 20.sp,
                  color: widget.cardColor,
                ),
              ),
            ],
          ),
        ),

        // 搜索框
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: CupertinoColors.white,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              children: [
                Icon(
                  CupertinoIcons.search,
                  size: 24.sp,
                  color: widget.cardColor,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 16.h),

        // 症状分类列表 - 左右分栏布局
        Expanded(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 24.w),
            clipBehavior: Clip.antiAlias, // 裁剪超出边界的内容，让圆角生效
            decoration: BoxDecoration(
              color: CupertinoColors.white,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: CategoryListController<SymptomCategory>(
              categories: symptomData.symptom,
              initialSelectedIndex: selectedCategoryIndex,
              onSelectedIndexChanged: (index) {
                setState(() {
                  selectedCategoryIndex = index;
                });
              },
              builder: ({
                required categories,
                required selectedIndex,
                required onCategoryTap,
                required scrollController,
                required sectionKeys,
                required wrapScrollView,
              }) {
                return Row(
                  children: [
                    // 左侧导航栏
                    _buildSidebar(categories, selectedIndex, onCategoryTap),
                    // 右侧内容区域
                    _buildContentArea(
                      categories,
                      selectedIndex,
                      scrollController,
                      sectionKeys,
                      wrapScrollView,
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  // 构建左侧导航栏
  Widget _buildSidebar(
    List<SymptomCategory> categories,
    int selectedIndex,
    void Function(int index) onCategoryTap,
  ) {
    return Container(
      width: 80.w,
      decoration: BoxDecoration(
        color: widget.cardBgColor.withValues(alpha: 0.3),
      ),
      child: ListView.builder(
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = index == selectedIndex;

          return GestureDetector(
            onTap: () => onCategoryTap(index),
            child: Container(
              decoration: BoxDecoration(
                color: isSelected ? CupertinoColors.white : CupertinoColors.transparent,
              ),
              child: Stack(
                alignment: Alignment.centerRight,
                children: [
                  // 选中状态的右半球装饰
                  if (isSelected)
                    Positioned(
                      left: 0,
                      child: Container(
                        width: 20.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: widget.cardColor,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(20.r),
                            bottomRight: Radius.circular(20.r),
                          ),
                        ),
                      ),
                    ),
                  // 导航项内容
                  Container(
                    margin: EdgeInsets.only(bottom: 8.h),
                    padding: EdgeInsets.fromLTRB(18.w, 12.h, 6.w, 12.h),
                    child: Text(
                      category.title,
                      style: TextUtil.base
                          .sp(14)
                          .medium
                          .withColor(widget.cardColor),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // 构建右侧内容区域
  Widget _buildContentArea(
    List<SymptomCategory> categories,
    int selectedIndex,
    ScrollController scrollController,
    List<GlobalKey> sectionKeys,
    Widget Function(Widget child) wrapScrollView,
  ) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: wrapScrollView(
          SingleChildScrollView(
            controller: scrollController,
            physics: const ClampingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: categories.asMap().entries.map((entry) {
                final index = entry.key;
                final category = entry.value;

                return Container(
                  key: sectionKeys[index],
                  margin: EdgeInsets.only(bottom: 24.h),
                  child: SymptomContentWidget(
                    category: category,
                    index: index,
                    isSelected: index == selectedIndex,
                    cardColor: widget.cardColor,
                    selectedSymptoms: _selectedSymptoms,
                    onSymptomToggle: _onSymptomToggle,
                    onAddSymptom: _showAddModal,
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  // 症状选择状态变化处理
  void _onSymptomToggle(String symptom, bool isSelected) {
    setState(() {
      if (isSelected) {
        _selectedSymptoms.add(symptom);
      } else {
        _selectedSymptoms.remove(symptom);
      }
    });
  }

  // 显示添加症状的模态窗口
  void _showAddModal(int categoryType) {
    switch (categoryType) {
      case 1:
        // 心情类型 - 显示情绪选择BottomSheet
        _showEmotionSelectionBottomSheet();
        break;
      case 2:
        // 食欲类型 - 显示食物添加BottomSheet
        _showFoodAddBottomSheet();
        break;
      case 3:
        // 体重类型 - 显示体重添加BottomSheet
        _showWeightAddBottomSheet();
        break;
      case 4:
        // 药物类型 - 显示药物添加BottomSheet
        _showMedicineAddBottomSheet();
        break;
      default:
        print('未知的分类类型: $categoryType');
    }
  }

  // 显示情绪选择BottomSheet
  void _showEmotionSelectionBottomSheet() {
    showCupertinoModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return EmotionSelectionBottomSheet(
          onEmotionSelected: (String emotion) {
            // 处理选中的情绪
            _addEmotionToCategory(emotion);
          },
          color: widget.cardColor,
          bgColor: widget.cardBgColor,
        );
      },
    );
  }

  // 显示食物添加BottomSheet
  void _showFoodAddBottomSheet() {
    showCupertinoModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return FoodAddBottomSheet(
          onFoodAdded: (String name, String unit) {
            // 处理添加的食物
            _addFoodToCategory(name, unit);
          },
          color: widget.cardColor,
          bgColor: widget.cardBgColor,
        );
      },
    );
  }

  // 显示体重添加BottomSheet
  void _showWeightAddBottomSheet() {
    showCupertinoModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return WeightAddBottomSheet(
          onWeightAdded: (String weight, String unit, String time) {
            // 处理添加的体重
            _addWeightToCategory(weight, unit, time);
          },
          color: widget.cardColor,
          bgColor: widget.cardBgColor,
        );
      },
    );
  }

  // 显示药物添加BottomSheet
  void _showMedicineAddBottomSheet() {
    showCupertinoModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return MedicineAddBottomSheet(
          onMedicineAdded: (String medicineName, String quantity, String unit, String time, List<int> cycle) {
            // 处理添加的药物
            _addMedicineToCategory(medicineName, quantity, unit, time, cycle);
          },
          color: widget.cardColor,
          bgColor: widget.cardBgColor,
        );
      },
    );
  }

  // 添加情绪到心情分类
  void _addEmotionToCategory(String emotion) {
    setState(() {
      // 找到心情分类（type为1）
      final moodCategoryIndex = symptomData.symptom.indexWhere((category) => category.type == 1);
      if (moodCategoryIndex != -1) {
        // 创建新的情绪内容
        final newEmotionContent = SymptomContent(description: emotion);

        // 添加到现有内容列表
        final updatedContent = List<SymptomContent>.from(symptomData.symptom[moodCategoryIndex].content);
        updatedContent.add(newEmotionContent);

        // 更新分类
        final updatedCategory = SymptomCategory(
          title: symptomData.symptom[moodCategoryIndex].title,
          type: symptomData.symptom[moodCategoryIndex].type,
          content: updatedContent,
        );

        // 更新症状数据
        final updatedSymptoms = List<SymptomCategory>.from(symptomData.symptom);
        updatedSymptoms[moodCategoryIndex] = updatedCategory;

        symptomData = SymptomData(symptom: updatedSymptoms);
      }
    });

    // 显示成功提示
    print('已添加情绪: $emotion');
  }

  // 添加食物到食欲分类
  void _addFoodToCategory(String name, String unit) {
    setState(() {
      // 找到食欲分类（type为2）
      final appetiteCategoryIndex = symptomData.symptom.indexWhere((category) => category.type == 2);
      if (appetiteCategoryIndex != -1) {
        // 创建新的食物内容
        final newFoodContent = SymptomContent(name: name, weight: unit);

        // 添加到现有内容列表
        final updatedContent = List<SymptomContent>.from(symptomData.symptom[appetiteCategoryIndex].content);
        updatedContent.add(newFoodContent);

        // 更新分类
        final updatedCategory = SymptomCategory(
          title: symptomData.symptom[appetiteCategoryIndex].title,
          type: symptomData.symptom[appetiteCategoryIndex].type,
          content: updatedContent,
        );

        // 更新症状数据
        final updatedSymptoms = List<SymptomCategory>.from(symptomData.symptom);
        updatedSymptoms[appetiteCategoryIndex] = updatedCategory;

        symptomData = SymptomData(symptom: updatedSymptoms);
      }
    });

    // 显示成功提示
    print('已添加食物: $name ($unit)');
  }

  // 添加体重到体重分类
  void _addWeightToCategory(String weight, String unit, String time) {
    setState(() {
      // 找到当前选中的体重分类（type为3）
      final weightCategoryIndex = selectedCategoryIndex;
      if (weightCategoryIndex < symptomData.symptom.length &&
          symptomData.symptom[weightCategoryIndex].type == 3) {
        // 创建新的体重内容
        final newWeightContent = SymptomContent(
          weight: '$weight$unit',
          unit: unit,
          time: time,
        );

        // 添加到现有内容列表
        final updatedContent = List<SymptomContent>.from(symptomData.symptom[weightCategoryIndex].content);
        updatedContent.add(newWeightContent);

        // 更新分类
        final updatedCategory = SymptomCategory(
          title: symptomData.symptom[weightCategoryIndex].title,
          type: symptomData.symptom[weightCategoryIndex].type,
          content: updatedContent,
        );

        // 更新症状数据
        final updatedSymptoms = List<SymptomCategory>.from(symptomData.symptom);
        updatedSymptoms[weightCategoryIndex] = updatedCategory;

        symptomData = SymptomData(symptom: updatedSymptoms);
      }
    });

    // 显示成功提示
    print('已添加体重: $weight$unit ($time)');
  }

  // 添加药物到药物分类
  void _addMedicineToCategory(String medicineName, String quantity, String unit, String time, List<int> cycle) {
    setState(() {
      // 找到当前选中的药物分类（type为4）
      final medicineCategoryIndex = selectedCategoryIndex;
      if (medicineCategoryIndex < symptomData.symptom.length &&
          symptomData.symptom[medicineCategoryIndex].type == 4) {
        // 创建新的药物内容
        final newMedicineContent = SymptomContent(
          medicineName: medicineName,
          quantity: '$quantity$unit',
          unit: unit,
          time: time,
          cycle: cycle,
        );

        // 添加到现有内容列表
        final updatedContent = List<SymptomContent>.from(symptomData.symptom[medicineCategoryIndex].content);
        updatedContent.add(newMedicineContent);

        // 更新分类
        final updatedCategory = SymptomCategory(
          title: symptomData.symptom[medicineCategoryIndex].title,
          type: symptomData.symptom[medicineCategoryIndex].type,
          content: updatedContent,
        );

        // 更新症状数据
        final updatedSymptoms = List<SymptomCategory>.from(symptomData.symptom);
        updatedSymptoms[medicineCategoryIndex] = updatedCategory;

        symptomData = SymptomData(symptom: updatedSymptoms);
      }
    });

    // 显示成功提示
    final cycleText = cycle.map((c) => ['一', '二', '三', '四', '五', '六', '日'][c - 1]).join('、');
    print('已添加药物: $medicineName $quantity$unit，时间: $time，周期: 周$cycleText');
  }
}


