import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../utils/text_util.dart';
import '../../utils/color_util.dart';

class FoodAddBottomSheet extends StatefulWidget {
  final Function(String name, String unit)? onFoodAdded;
  final Color color;
  final Color bgColor;

  const FoodAddBottomSheet({
    super.key,
    this.onFoodAdded,
    required this.color,
    required this.bgColor,
  });

  @override
  State<FoodAddBottomSheet> createState() => _FoodAddBottomSheetState();
}

class _FoodAddBottomSheetState extends State<FoodAddBottomSheet> {
  final TextEditingController _nameController = TextEditingController();
  final List<String> _units = ['一碗', '一个', '一份', '一杯', '一盘', '一块', '100g', '200g', '300g'];
  int _selectedUnitIndex = 2; // 默认选中"一份"

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  void _onAddFood() {
    final name = _nameController.text.trim();
    if (name.isEmpty) {
      // 显示错误提示
      _showErrorDialog('请输入食品名称');
      return;
    }

    final unit = _units[_selectedUnitIndex];
    widget.onFoodAdded?.call(name, unit);
    Navigator.of(context).pop();
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('提示'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            child: Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showUnitPicker() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => Container(
        height: 250.h,
        color: CupertinoColors.white,
        child: Column(
          children: [
            // 顶部操作栏
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: ColorUtil.borderGrey,
                    width: 1.w,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '取消',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(CupertinoColors.systemGrey),
                    ),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '确定',
                      style: TextUtil.base
                          .sp(16)
                          .withColor(widget.color),
                    ),
                  ),
                ],
              ),
            ),
            // 选择器
            Expanded(
              child: CupertinoPicker(
                itemExtent: 40.h,
                scrollController: FixedExtentScrollController(
                  initialItem: _selectedUnitIndex,
                ),
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedUnitIndex = index;
                  });
                },
                children: _units.map((unit) => Center(
                  child: Text(
                    unit,
                    style: TextUtil.base
                        .sp(16)
                        .withColor(widget.color),
                  ),
                )).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
       bottom: MediaQuery.of(context).viewInsets.bottom,
     ),
      child: Container(
        height: 250.h,
        color: widget.bgColor,
        child: Column(
          children: [
            // 标题栏
            _buildHeader(),
      
            // 可滚动的表单内容
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Column(
                  children: [
                    // 表单内容
                    _buildForm(),
      
                    // 添加按钮
                    _buildAddButton(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
      child: Row(
        children: [
          // 占位，保持标题居中
          SizedBox(width: 44.w),
          
          // 中间标题
          Expanded(
            child: Center(
              child: Text(
                '添加食物',
                style: TextUtil.base
                    .sp(18)
                    .semiBold
                    .withColor(widget.color),
              ),
            ),
          ),
          
          // 右侧关闭按钮
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                CupertinoIcons.xmark,
                size: 20.sp,
                color: widget.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Row(
        children: [
          // 食品名称输入框
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: CupertinoColors.white,
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: ColorUtil.borderGrey,
                  width: 1.w,
                ),
              ),
              child: CupertinoTextField(
                controller: _nameController,
                placeholder: '请输入食品名称',
                placeholderStyle: TextUtil.base
                    .sp(16)
                    .withColor(CupertinoColors.systemGrey),
                style: TextUtil.base
                    .sp(16)
                    .withColor(widget.color),
                decoration: null,
                padding: EdgeInsets.zero,
              ),
            ),
          ),

          SizedBox(width: 12.w),

          // 单位选择下拉框
          Expanded(
            flex: 1,
            child: GestureDetector(
              onTap: _showUnitPicker,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                decoration: BoxDecoration(
                  color: CupertinoColors.white,
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color: ColorUtil.borderGrey,
                    width: 1.w,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        _units[_selectedUnitIndex],
                        style: TextUtil.base
                            .sp(16)
                            .withColor(widget.color),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(
                      CupertinoIcons.chevron_down,
                      size: 16.sp,
                      color: CupertinoColors.systemGrey,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddButton() {
    return Container(
      padding: EdgeInsets.all(24.w),
      child: SizedBox(
        width: double.infinity,
        height: 48.h,
        child: CupertinoButton(
          color: widget.color,
          borderRadius: BorderRadius.circular(99.r),
          padding: EdgeInsets.zero,
          onPressed: _onAddFood,
          child: Text(
            '添加',
            style: TextUtil.base
                .sp(16)
                .semiBold
                .withColor(CupertinoColors.white),
          ),
        ),
      ),
    );
  }
}
