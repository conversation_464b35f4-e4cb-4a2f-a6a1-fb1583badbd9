import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../lib/widgets/swipeable_calendar.dart';

void main() {
  group('Year View Layout Tests', () {
    testWidgets('Should use mainAxisExtent instead of childAspectRatio in year view', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const <PERSON><PERSON>(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.year,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证年视图正确渲染
      expect(find.byType(SwipeableCalendar), findsOneWidget);
      
      // 查找GridView（年视图中的月份网格）
      final gridViewFinder = find.byType(GridView);
      expect(gridViewFinder, findsWidgets);
      
      // 验证月份标题存在
      expect(find.text('一月'), findsOneWidget);
      expect(find.text('六月'), findsOneWidget);
      expect(find.text('十二月'), findsOneWidget);
    });

    testWidgets('Should handle different year view heights correctly', (WidgetTester tester) async {
      // 测试不同的年视图高度
      final testHeights = [400.0, 500.0, 600.0];

      for (final height in testHeights) {
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: const Size(393, 852),
            builder: (context, child) {
              return CupertinoApp(
                home: CupertinoPageScaffold(
                  child: SafeArea(
                    child: SwipeableCalendar(
                      selectedDay: DateTime(2024, 6, 15),
                      initialFormat: CustomCalendarFormat.year,
                      yearViewHeight: height.h, // 传入经过ScreenUtil处理的值
                      onDaySelected: (day) {},
                      onMonthChanged: (month) {},
                      onViewChanged: (format) {},
                    ),
                  ),
                ),
              );
            },
          ),
        );

        await tester.pumpAndSettle();

        // 验证年视图能够正常渲染，不会溢出
        expect(tester.takeException(), isNull, reason: 'Should not overflow with height $height');

        // 验证所有月份都能显示
        expect(find.text('一月'), findsOneWidget);
        expect(find.text('十二月'), findsOneWidget);
      }
    });

    testWidgets('Should calculate item height and spacing correctly', (WidgetTester tester) async {
      // 测试动态高度计算功能
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.year,
                    yearViewHeight: 600.0.h, // 传入经过ScreenUtil处理的值
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证年视图正确渲染
      expect(find.byType(SwipeableCalendar), findsOneWidget);

      // 验证没有渲染异常
      expect(tester.takeException(), isNull);

      // 验证所有月份都能显示（说明高度计算正确）
      expect(find.text('一月'), findsOneWidget);
      expect(find.text('六月'), findsOneWidget);
      expect(find.text('十二月'), findsOneWidget);

      // 验证日期网格能够正确显示（检查一些具体日期）
      expect(find.text('1'), findsWidgets); // 应该有多个"1"（每个月的1号）
    });

    testWidgets('Should display all 12 months in year view', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.year,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {},
                    onViewChanged: (format) {},
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证所有12个月份都显示
      final monthNames = [
        '一月', '二月', '三月', '四月', '五月', '六月',
        '七月', '八月', '九月', '十月', '十一月', '十二月'
      ];

      for (final monthName in monthNames) {
        expect(find.text(monthName), findsOneWidget, reason: 'Month $monthName should be displayed');
      }
    });

    testWidgets('Should handle month tapping in year view', (WidgetTester tester) async {
      CustomCalendarFormat? currentFormat;
      String? currentMonthTitle;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: DateTime(2024, 6, 15),
                    initialFormat: CustomCalendarFormat.year,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                    },
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态为年视图
      expect(currentFormat, equals(CustomCalendarFormat.year));

      // 点击三月
      final marchFinder = find.text('三月');
      expect(marchFinder, findsOneWidget);
      
      await tester.tap(marchFinder);
      await tester.pumpAndSettle();

      // 验证切换到月视图
      expect(currentFormat, equals(CustomCalendarFormat.month));
    });

    testWidgets('Should not overflow with different screen sizes', (WidgetTester tester) async {
      // 测试不同的屏幕尺寸
      final screenSizes = [
        const Size(320, 568), // iPhone SE
        const Size(375, 667), // iPhone 8
        const Size(393, 852), // 默认设计尺寸
        const Size(414, 896), // iPhone 11
      ];

      for (final size in screenSizes) {
        await tester.pumpWidget(
          ScreenUtilInit(
            designSize: size,
            builder: (context, child) {
              return CupertinoApp(
                home: CupertinoPageScaffold(
                  child: SafeArea(
                    child: SwipeableCalendar(
                      selectedDay: DateTime(2024, 6, 15),
                      initialFormat: CustomCalendarFormat.year,
                      onDaySelected: (day) {},
                      onMonthChanged: (month) {},
                      onViewChanged: (format) {},
                    ),
                  ),
                ),
              );
            },
          ),
        );

        await tester.pumpAndSettle();

        // 验证没有渲染异常
        expect(tester.takeException(), isNull, reason: 'Should not overflow with screen size $size');
        
        // 验证基本内容能够显示
        expect(find.text('一月'), findsOneWidget);
        expect(find.text('十二月'), findsOneWidget);
      }
    });
  });
}
