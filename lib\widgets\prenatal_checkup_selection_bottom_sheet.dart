import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:azlistview/azlistview.dart';
import '../utils/text_util.dart';
import '../utils/color_util.dart';
import '../utils/toast_util.dart';
import '../models/prenatal_checkup_model.dart';

class PrenatalCheckupSelectionBottomSheet extends StatefulWidget {
  final Function(String checkup)? onCheckupSelected;
  final Color color;
  final Color bgColor;

  const PrenatalCheckupSelectionBottomSheet({
    super.key,
    this.onCheckupSelected,
    required this.color,
    required this.bgColor,
  });

  @override
  State<PrenatalCheckupSelectionBottomSheet> createState() =>
      _PrenatalCheckupSelectionBottomSheetState();
}

class _PrenatalCheckupSelectionBottomSheetState
    extends State<PrenatalCheckupSelectionBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<PrenatalCheckupModel> _checkups = [];
  List<PrenatalCheckupModel> _filteredCheckups = [];

  @override
  void initState() {
    super.initState();
    _checkups = PrenatalCheckupDataManager.getAllCheckups();
    _filteredCheckups = List.from(_checkups);

    // 先按startweek升序排序，再按名称排序（确保同一孕周内的项目有固定顺序）
    _filteredCheckups.sort((a, b) {
      int weekCompare = a.startWeek.compareTo(b.startWeek);
      if (weekCompare != 0) return weekCompare;
      return a.name.compareTo(b.name);
    });

    // 只设置显示状态，不使用字符串排序
    SuspensionUtil.setShowSuspensionStatus(_filteredCheckups);

    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _filteredCheckups = PrenatalCheckupDataManager.searchCheckups(
        _searchController.text,
      );

      // 先按startweek升序排序，再按名称排序（确保同一孕周内的项目有固定顺序）
      _filteredCheckups.sort((a, b) {
        int weekCompare = a.startWeek.compareTo(b.startWeek);
        if (weekCompare != 0) return weekCompare;
        return a.name.compareTo(b.name);
      });

      // 只设置显示状态，不使用字符串排序
      SuspensionUtil.setShowSuspensionStatus(_filteredCheckups);
    });
  }

  void _onCheckupTap(String checkup) {
    widget.onCheckupSelected?.call(checkup);
    Navigator.of(context).pop();
  }

  void _showAddCheckupDialog() {
    final TextEditingController controller = TextEditingController();

    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(
          '添加产检项目',
          style: TextUtil.base
              .sp(18)
              .semiBold
              .withColor(widget.color),
        ),
        content: Column(
          children: [
            SizedBox(height: 16.h),
            CupertinoTextField(
              controller: controller,
              placeholder: '请输入产检项目名称',
              placeholderStyle: TextUtil.base
                  .sp(16)
                  .withColor(CupertinoColors.systemGrey),
              style: TextUtil.base
                  .sp(16)
                  .withColor(widget.color),
              decoration: BoxDecoration(
                border: Border.all(
                  color: ColorUtil.borderGrey,
                  width: 1.w,
                ),
                borderRadius: BorderRadius.circular(8.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text(
              '取消',
              style: TextUtil.base
                  .sp(16)
                  .withColor(CupertinoColors.systemGrey),
            ),
            onPressed: () {
              controller.dispose();
              Navigator.of(context).pop();
            },
          ),
          CupertinoDialogAction(
            child: Text(
              '确定',
              style: TextUtil.base
                  .sp(16)
                  .semiBold
                  .withColor(widget.color),
            ),
            onPressed: () => _addNewCheckup(controller),
          ),
        ],
      ),
    );
  }

  void _addNewCheckup(TextEditingController controller) {
    final checkup = controller.text.trim();

    // 验证输入不能为空
    if (checkup.isEmpty) {
      ToastUtil.showError('请输入产检项目名称');
      return;
    }

    // 验证不能重复
    if (PrenatalCheckupDataManager.isCheckupExists(checkup)) {
      ToastUtil.showError('该产检项目已存在');
      return;
    }

    // 添加新产检项目
    if (PrenatalCheckupDataManager.addCheckup(checkup)) {
      // 更新列表
      setState(() {
        _checkups = PrenatalCheckupDataManager.getAllCheckups();
        _filteredCheckups = PrenatalCheckupDataManager.searchCheckups(_searchController.text);

        // 先按startweek升序排序，再按名称排序（确保同一孕周内的项目有固定顺序）
        _filteredCheckups.sort((a, b) {
          int weekCompare = a.startWeek.compareTo(b.startWeek);
          if (weekCompare != 0) return weekCompare;
          return a.name.compareTo(b.name);
        });

        // 只设置显示状态，不使用字符串排序
        SuspensionUtil.setShowSuspensionStatus(_filteredCheckups);
      });

      // 关闭对话框
      controller.dispose();
      Navigator.of(context).pop();

      // 显示成功提示
      ToastUtil.showSuccess('添加成功');
    } else {
      ToastUtil.showError('添加失败，请重试');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白区域取消搜索框焦点并收起键盘
        FocusScope.of(context).unfocus();
      },
      child: SizedBox(
        height: 0.8.sh,
        child: Column(
          children: [
            // 标题栏
            _buildHeader(),

            // 搜索栏
            _buildSearchBar(),

            // 产检项目列表
            Expanded(child: _buildCheckupList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
      child: Row(
        children: [
          // 左侧添加按钮
          GestureDetector(
            onTap: _showAddCheckupDialog,
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(CupertinoIcons.add, size: 20.sp, color: widget.color),
            ),
          ),

          // 中间标题
          Expanded(
            child: Center(
              child: Text(
                '添加产检项目',
                style: TextUtil.base.sp(18).semiBold.withColor(widget.color),
              ),
            ),
          ),

          // 右侧关闭按钮
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                CupertinoIcons.xmark,
                size: 20.sp,
                color: widget.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: widget.bgColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        children: [
          Icon(CupertinoIcons.search, size: 20.sp, color: widget.color),
          SizedBox(width: 8.w),
          Expanded(
            child: CupertinoTextField(
              controller: _searchController,
              placeholder: '搜索产检项目',
              placeholderStyle: TextUtil.base
                  .sp(16)
                  .withColor(CupertinoColors.systemGrey),
              style: TextUtil.base.sp(16).withColor(widget.color),
              decoration: null,
              padding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckupList() {
    if (_filteredCheckups.isEmpty) {
      return Center(
        child: Text(
          '未找到相关产检项目',
          style: TextUtil.base.sp(16).withColor(CupertinoColors.systemGrey),
        ),
      );
    }

    return Container(
      color: CupertinoColors.white,
      child: GestureDetector(
        onPanDown: (_) {
          // 开始滑动时取消搜索框焦点并收起键盘
          FocusScope.of(context).unfocus();
        },
        child: AzListView(
          data: _filteredCheckups,
          itemCount: _filteredCheckups.length,
          itemBuilder: (context, index) {
            final checkup = _filteredCheckups[index];
            final isFirstInGroup =
                index == 0 ||
                _filteredCheckups[index - 1].getSuspensionTag() !=
                    checkup.getSuspensionTag();

            final isLastInGroup =
                index == _filteredCheckups.length - 1 ||
                _filteredCheckups[index + 1].getSuspensionTag() !=
                    checkup.getSuspensionTag();

            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 如果是分组的第一个项目，显示分组标题
                  if (isFirstInGroup)
                    _buildSectionHeader(checkup.startWeek, checkup.endWeek),
                  // 产检项目
                  _buildCheckupItem(checkup, isLastInGroup),
                ],
              ),
            );
          },
          indexBarData: _getIndexBarData(),
          indexBarOptions: IndexBarOptions(
            needRebuild: true,
            ignoreDragCancel: true,
            downTextStyle: TextUtil.base.sp(12).withColor(CupertinoColors.white),
            downItemDecoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.color,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(int startWeek, int endWeek) {
    return Container(
      height: 40.h,
      margin: EdgeInsets.only(top: 16.h),
      alignment: Alignment.centerLeft,
      child: Text(
        PrenatalCheckupDataManager.getGroupTitle(startWeek, endWeek),
        style: TextUtil.base.sp(16).semiBold.withColor(widget.color),
      ),
    );
  }

  Widget _buildCheckupItem(PrenatalCheckupModel checkup, bool isLastInGroup) {
    return GestureDetector(
      onTap: () => _onCheckupTap(checkup.name),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4.h),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    checkup.name,
                    style: TextUtil.base.sp(16).withColor(widget.color),
                  ),
                ),
              ],
            ),
          ),
          // 如果不是分组中的最后一项，显示分隔线
          if (!isLastInGroup)
            Container(
              margin: EdgeInsets.fromLTRB(0, 4.h, 24.w, 0),
              height: 0.5.w,
              color: widget.color.withValues(alpha: 0.5),
            ),
        ],
      ),
    );
  }

  List<String> _getIndexBarData() {
    final Set<String> indexSet = {};
    for (final checkup in _filteredCheckups) {
      indexSet.add(checkup.getSuspensionTag());
    }
    final List<String> indexList = indexSet.toList();
    indexList.sort((a, b) => int.parse(a).compareTo(int.parse(b)));
    return indexList;
  }
}
