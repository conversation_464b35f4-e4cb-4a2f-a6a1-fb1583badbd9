# 年视图布局改进文档

## 概述

本次改进主要针对SwipeableCalendar组件中的年视图布局实现，解决了固定宽高比容易在不同屏幕尺寸下造成内容溢出的问题，并实现了动态高度计算以确保布局稳定。

## 主要改进

### 1. 添加年视图高度参数

**修改位置**: `SwipeableCalendar` 构造函数

```dart
class SwipeableCalendar extends StatefulWidget {
  // ... 其他参数
  final double yearViewHeight; // 新增：年视图高度

  const SwipeableCalendar({
    // ... 其他参数
    this.yearViewHeight = 500.0, // 年视图默认高度
  }) : super(key: key);
}
```

**改进效果**:
- 支持自定义年视图高度
- 提供合理的默认值（500.0）
- 便于在不同场景下调整布局

### 2. 修改GridView配置

**修改位置**: `_buildYearCalendar` 方法

**改进前**:
```dart
gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  crossAxisCount: 3,
  crossAxisSpacing: 12.w,
  mainAxisSpacing: 16.h,
  childAspectRatio: 0.85, // 固定宽高比，容易溢出
),
```

**改进后**:
```dart
gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  crossAxisCount: columns, // 3列
  crossAxisSpacing: crossAxisSpacing,
  mainAxisSpacing: mainAxisSpacing,
  mainAxisExtent: itemHeight, // 使用固定高度替代childAspectRatio
),
```

**改进效果**:
- 移除了容易造成溢出的 `childAspectRatio`
- 使用 `mainAxisExtent` 设置固定的项目高度
- 确保布局在不同屏幕尺寸下的稳定性

### 3. 实现动态高度计算

**修改位置**: `_buildYearCalendar` 方法

```dart
// 动态计算GridView中每个月份项目的高度
final verticalPadding = 8.h * 2; // 上下内边距
final crossAxisSpacing = 12.w; // 列间距
final mainAxisSpacing = 16.h; // 行间距
final rows = 4; // 4行（12个月份 ÷ 3列 = 4行）
final columns = 3; // 3列

// 计算可用高度：总高度 - 内边距 - (行间距 × 行数-1)
final availableHeight = containerHeight - verticalPadding - (mainAxisSpacing * (rows - 1));

// 每个月份项目的高度 = 可用高度 ÷ 行数
final itemHeight = availableHeight / rows;
```

**改进效果**:
- 基于容器总高度动态计算每个月份项目的高度
- 考虑了内边距和行间距的影响
- 确保所有月份项目能够完整显示

### 4. 优化月份内部布局

**修改位置**: `_buildSimpleMonthCalendar` 方法

```dart
// 根据itemHeight计算内部布局
final containerPadding = 4.h * 2; // 上下内边距
final titleHeight = 20.h; // 月份标题高度（估算）
final titleSpacing = 8.h; // 标题下方间距
final availableGridHeight = itemHeight - containerPadding - titleHeight - titleSpacing;
```

**改进效果**:
- 精确计算月份标题和日期网格的可用空间
- 确保内容不会被截断
- 提供更好的视觉层次

### 5. 智能行间距计算

**修改位置**: `_buildMonthDateGrid` 方法

```dart
// 计算需要的行数（最多6行）
final totalCells = emptyDays + daysInMonth;
final rows = (totalCells / 7).ceil();

// 根据gridHeight计算合适的行间距
double mainAxisSpacing = 4.h; // 默认行间距
if (gridHeight != null && rows > 1) {
  // 计算每行的理想高度
  final availableSpacing = gridHeight - (rows * 12.h); // 假设每行高度约12.h
  final maxSpacing = availableSpacing / (rows - 1);
  mainAxisSpacing = maxSpacing.clamp(2.h, 6.h); // 限制行间距范围
}
```

**改进效果**:
- 根据可用高度动态调整日期网格的行间距
- 确保所有日期都能正确显示
- 避免内容溢出或过度压缩

## 使用示例

### 基本使用（使用默认高度）

```dart
SwipeableCalendar(
  selectedDay: DateTime.now(),
  initialFormat: CustomCalendarFormat.year,
  onDaySelected: (day) {},
  onMonthChanged: (month) {},
  onViewChanged: (format) {},
)
```

### 自定义年视图高度

```dart
SwipeableCalendar(
  selectedDay: DateTime.now(),
  initialFormat: CustomCalendarFormat.year,
  yearViewHeight: 600.0, // 自定义高度
  onDaySelected: (day) {},
  onMonthChanged: (month) {},
  onViewChanged: (format) {},
)
```

## 测试验证

我们创建了全面的测试来验证改进效果：

1. **基本渲染测试**: 验证年视图能够正常渲染
2. **不同高度测试**: 测试不同年视图高度下的布局稳定性
3. **动态计算测试**: 验证高度和间距计算的正确性
4. **月份显示测试**: 确保所有12个月份都能正确显示
5. **交互测试**: 验证月份点击功能正常工作
6. **响应式测试**: 测试不同屏幕尺寸下的兼容性

## 技术优势

1. **响应式设计**: 使用ScreenUtil确保在不同屏幕尺寸下的一致性
2. **动态适应**: 根据容器高度自动调整内部布局
3. **内容完整性**: 确保所有月份和日期都能完整显示
4. **性能优化**: 避免不必要的滚动和溢出
5. **可配置性**: 支持自定义年视图高度以适应不同需求

## 兼容性

- 保持了原有API的完全兼容性
- 新增的 `yearViewHeight` 参数有合理的默认值
- 不影响周视图和月视图的功能
- 支持所有现有的回调和交互功能

这些改进确保了年视图在各种设备和屏幕尺寸下都能提供稳定、美观的用户体验。
