import '../utils/sp_util.dart';
import '../models/user_models.dart';

/// 认证服务，管理用户登录状态
class AuthService {
  static final AuthService _instance = AuthService._internal();
  static AuthService get instance => _instance;

  AuthService._internal();

  // 当前登录状态
  bool _isLoggedIn = false;

  // 当前用户信息
  UserModel? _currentUser;

  /// 检查是否已登录
  Future<bool> isLoggedIn() async {
    try {
      _isLoggedIn = await SpUtil.getLoginStatus();
      return _isLoggedIn;
    } catch (e) {
      // 如果读取失败，默认为未登录状态
      _isLoggedIn = false;
      return false;
    }
  }
  
  /// 登录
  Future<bool> login({required String username, required String password}) async {
    try {
      // 这里添加实际的登录逻辑，比如调用API
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // 模拟登录成功，创建用户信息
      final user = UserModel(
        id: 'demo_user_id',
        username: username,
        nickname: '演示用户',
        email: '<EMAIL>',
        createdAt: DateTime.now().toIso8601String(),
      );

      // 使用SpUtil完整登录
      final success = await SpUtil.completeLogin(
        user: user,
        autoLogin: true,
        rememberPassword: true,
      );

      if (success) {
        _isLoggedIn = true;
        _currentUser = user;
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      // 使用SpUtil完整登出
      await SpUtil.completeLogout();

      // 更新内存状态
      _isLoggedIn = false;
      _currentUser = null;
    } catch (e) {
      // 即使清除失败，也要更新内存状态
      _isLoggedIn = false;
      _currentUser = null;
    }
  }
  
  /// 获取当前登录状态（同步）
  bool get currentLoginStatus => _isLoggedIn;

  /// 获取当前用户信息（同步）
  UserModel? get currentUser => _currentUser;

  /// 获取当前用户信息（异步，从存储中读取）
  Future<UserModel?> getCurrentUser() async {
    try {
      _currentUser = await SpUtil.getUserInfo();
      return _currentUser;
    } catch (e) {
      return null;
    }
  }

  /// 更新用户信息
  Future<bool> updateUserInfo(UserModel user) async {
    try {
      final success = await SpUtil.updateUserInfo(user);
      if (success) {
        _currentUser = user;
      }
      return success;
    } catch (e) {
      return false;
    }
  }

  /// 初始化认证服务（应用启动时调用）
  Future<void> initialize() async {
    try {
      // 初始化SpUtil
      await SpUtil.init();

      // 检查登录状态
      _isLoggedIn = await SpUtil.getLoginStatus();

      // 如果已登录，加载用户信息
      if (_isLoggedIn) {
        _currentUser = await SpUtil.getUserInfo();
      }
    } catch (e) {
      _isLoggedIn = false;
      _currentUser = null;
    }
  }
}
