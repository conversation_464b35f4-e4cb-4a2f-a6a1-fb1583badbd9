import '../utils/sp_util.dart';
import '../models/user_models.dart';
import '../api/api_service.dart';

/// 认证服务，管理用户登录状态
class AuthService {
  static final AuthService _instance = AuthService._internal();
  static AuthService get instance => _instance;

  AuthService._internal();

  // 当前登录状态
  bool _isLoggedIn = false;

  // 当前用户信息
  UserModel? _currentUser;

  /// 检查是否已登录
  Future<bool> isLoggedIn() async {
    try {
      _isLoggedIn = await SpUtil.getLoginStatus();
      return _isLoggedIn;
    } catch (e) {
      // 如果读取失败，默认为未登录状态
      _isLoggedIn = false;
      return false;
    }
  }
  
  /// 登录
  Future<bool> login({required String email, required String password}) async {
    try {
      // 调用API登录接口
      final response = await ApiService.instance.login(
        email: email,
        password: password,
      );

      if (response.success && response.data != null) {
        final authResponse = response.data!;
        final user = authResponse.toUserModel();
        final token = authResponse.toAuthTokenModel();

        if (user != null) {
          // 使用SpUtil完整登录
          final success = await SpUtil.completeLogin(
            user: user,
            token: token,
            autoLogin: true,
            rememberPassword: true,
          );

          if (success) {
            _isLoggedIn = true;
            _currentUser = user;
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 邮箱验证码登录
  Future<bool> loginWithEmailCode({required String email, required String code}) async {
    try {
      // 调用API验证码登录接口
      final response = await ApiService.instance.loginWithEmailCode(
        email: email,
        code: code,
      );

      if (response.success && response.data != null) {
        final authResponse = response.data!;
        final user = authResponse.toUserModel();
        final token = authResponse.toAuthTokenModel();

        if (user != null) {
          // 使用SpUtil完整登录
          final success = await SpUtil.completeLogin(
            user: user,
            token: token,
            autoLogin: true,
            rememberPassword: false, // 验证码登录不记住密码
          );

          if (success) {
            _isLoggedIn = true;
            _currentUser = user;
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 发送邮箱验证码
  Future<bool> sendVerificationCode({required String email, String codeType = 'login'}) async {
    try {
      final response = await ApiService.instance.sendVerificationCode(
        email: email,
        codeType: codeType,
      );

      return response.success;
    } catch (e) {
      return false;
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      // 使用SpUtil完整登出
      await SpUtil.completeLogout();

      // 更新内存状态
      _isLoggedIn = false;
      _currentUser = null;
    } catch (e) {
      // 即使清除失败，也要更新内存状态
      _isLoggedIn = false;
      _currentUser = null;
    }
  }
  
  /// 获取当前登录状态（同步）
  bool get currentLoginStatus => _isLoggedIn;

  /// 获取当前用户信息（同步）
  UserModel? get currentUser => _currentUser;

  /// 获取当前用户信息（异步，从存储中读取）
  Future<UserModel?> getCurrentUser() async {
    try {
      _currentUser = await SpUtil.getUserInfo();
      return _currentUser;
    } catch (e) {
      return null;
    }
  }

  /// 更新用户信息
  Future<bool> updateUserInfo(UserModel user) async {
    try {
      final success = await SpUtil.updateUserInfo(user);
      if (success) {
        _currentUser = user;
      }
      return success;
    } catch (e) {
      return false;
    }
  }

  /// 初始化认证服务（应用启动时调用）
  Future<void> initialize() async {
    try {
      // 初始化SpUtil
      await SpUtil.init();

      // 检查登录状态
      _isLoggedIn = await SpUtil.getLoginStatus();

      // 如果已登录，加载用户信息
      if (_isLoggedIn) {
        _currentUser = await SpUtil.getUserInfo();
      }
    } catch (e) {
      _isLoggedIn = false;
      _currentUser = null;
    }
  }
}
