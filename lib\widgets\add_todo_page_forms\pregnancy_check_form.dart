import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import '../../utils/color_util.dart';
import '../../utils/text_util.dart';
import '../../utils/toast_util.dart';
import '../../data/preparation_data.dart';
import '../prenatal_checkup_selection_bottom_sheet.dart';
import '../preparation_info_bottom_sheet.dart';

/// 产检表单组件
class PregnancyCheckForm extends StatefulWidget {
  final Color cardColor;
  final Color cardBgColor;
  final bool isDetailMode;
  final VoidCallback? onSave;

  const PregnancyCheckForm({
    super.key,
    required this.cardColor,
    required this.cardBgColor,
    this.isDetailMode = false,
    this.onSave,
  });

  @override
  State<PregnancyCheckForm> createState() => _PregnancyCheckFormState();
}

class _PregnancyCheckFormState extends State<PregnancyCheckForm> {
  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  // 表单控制器
  final TextEditingController _timeController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _reminderTimeController = TextEditingController();
  final TextEditingController _reminderAdvanceController =
      TextEditingController();
  final TextEditingController _pregnancyCheckRecordController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _preparationNotesController =
      TextEditingController();

  // 重点项目所有存在的标签（包括预设和动态添加的）
  final Set<String> _allKeyProjects = {'无创DNA', '羊水穿刺', '中期筛查', '血压测量'};

  // 重点项目选中的标签
  final Set<String> _selectedKeyProjects = {};

  // 项目准备选中的标签
  final Set<String> _selectedPreparationItems = {};

  // 图片选择器
  final ImagePicker _picker = ImagePicker();

  // 选中的产检单图片
  File? _selectedImage;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // 释放滚动控制器
    _scrollController.dispose();

    // 释放表单控制器
    _timeController.dispose();
    _locationController.dispose();
    _reminderTimeController.dispose();
    _reminderAdvanceController.dispose();
    _notesController.dispose();
    _preparationNotesController.dispose();
    _pregnancyCheckRecordController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: GestureDetector(
        onTap: () {
          // 点击空白处收起键盘
          FocusScope.of(context).unfocus();
        },
        child: _buildScrollView(),
      ),
    );
  }

  Widget _buildScrollView() {
    return Container(
      color: widget.cardBgColor,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: EdgeInsets.only(
          left: 24.w,
          right: 24.w,
          bottom: 100.h, // 底部间距，防止软键盘遮挡
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPregnancyCheckDetailSection(),
            SizedBox(height: 24.h),

            // 产检前提示
            _buildReminderSection(),
            SizedBox(height: 24.h),

            // 产检笔记
            _buildNotesSection(),
            SizedBox(height: 24.h),

            // 产检单记录
            _buildPregnancyCheckRecordSection(),
            SizedBox(height: 32.h),

            // 保存按钮（仅在非详情模式下显示）
            if (!widget.isDetailMode) _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildPregnancyCheckDetailSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              '产检详细',
              style: TextUtil.base.sp(18).semiBold.withColor(widget.cardColor),
            ),
          ),
          SizedBox(height: 16.h),

          // 产检时间
          Row(
            children: [
              Text(
                '产检时间',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _showTimePicker,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 8.h,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: widget.cardColor),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    _timeController.text.isEmpty
                        ? '00:00'
                        : _timeController.text,
                    style: TextUtil.base
                        .sp(14)
                        .withColor(
                          _timeController.text.isEmpty
                              ? CupertinoColors.systemGrey
                              : widget.cardColor,
                        ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 地点
          Row(
            children: [
              Text(
                '地点',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              SizedBox(width: 60.w),
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 12.h,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: widget.cardColor),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        CupertinoIcons.search,
                        size: 20.sp,
                        color: widget.cardColor,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: CupertinoTextField(
                          controller: _locationController,
                          placeholder: '搜索',
                          placeholderStyle: TextUtil.base
                              .sp(14)
                              .withColor(CupertinoColors.systemGrey),
                          style: TextUtil.base
                              .sp(14)
                              .withColor(widget.cardColor),
                          decoration: null,
                          padding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 重点项目
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '重点项目',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              SizedBox(height: 12.h),
              Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: [
                  // 显示所有存在的标签
                  ..._allKeyProjects.map((tag) => _buildKeyProjectTagButton(tag)),
                  // 添加按钮
                  _buildKeyProjectTagButton('+'),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建重点项目标签按钮
  Widget _buildKeyProjectTagButton(String text) {
    final isAddButton = text == '+';

    // 所有非添加按钮的标签都根据是否在_selectedKeyProjects中决定选中状态
    final isSelected = !isAddButton && _selectedKeyProjects.contains(text);

    return GestureDetector(
      onTap: () {
        if (isAddButton) {
          _showAddKeyProjectBottomSheet();
        } else {
          // 所有标签：切换选中状态
          setState(() {
            if (isSelected) {
              _selectedKeyProjects.remove(text);
            } else {
              _selectedKeyProjects.add(text);
            }
          });
        }
      },
      child: Stack(
        clipBehavior: Clip.none, // 允许子组件超出边界，避免裁剪
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: isSelected
                  ? widget.cardColor
                  : CupertinoColors.transparent,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: widget.cardColor),
            ),
            child: Text(
              text,
              style: TextUtil.base
                  .sp(14)
                  .withColor(
                    isSelected ? CupertinoColors.white : widget.cardColor,
                  ),
            ),
          ),
          // 选中状态的小勾图标 - 仅对非添加按钮显示
          if (!isAddButton)
            Positioned(
              right: 0,
              bottom: 0,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 150),
                opacity: isSelected ? 1.0 : 0.0,
                child: Container(
                  width: 16.w,
                  height: 16.w,
                  decoration: BoxDecoration(
                    color: widget.cardColor,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: CupertinoColors.white,
                      width: 1.5,
                    ),
                  ),
                  child: Icon(
                    CupertinoIcons.checkmark,
                    size: 10.sp,
                    color: CupertinoColors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 构建项目准备标签按钮
  Widget _buildPreparationTagButton(String text) {
    final isSelected = _selectedPreparationItems.contains(text);
    final isAddButton = text == '+';

    return GestureDetector(
      onTap: () {
        if (isAddButton) {
          _showAddPreparationTagDialog();
        } else {
          // 显示准备信息bottomsheet
          _showPreparationInfoBottomSheet(text);
        }
      },
      child: Stack(
        clipBehavior: Clip.none, // 允许子组件超出边界，避免裁剪
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: isSelected
                  ? widget.cardColor
                  : CupertinoColors.transparent,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: widget.cardColor),
            ),
            child: Text(
              text,
              style: TextUtil.base
                  .sp(14)
                  .withColor(
                    isSelected ? CupertinoColors.white : widget.cardColor,
                  ),
            ),
          ),
          // 选中状态的小勾图标 - 仅对非添加按钮显示
          if (!isAddButton)
            Positioned(
              right: 0,
              bottom: 0,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 150),
                opacity: isSelected ? 1.0 : 0.0,
                child: Container(
                  width: 16.w,
                  height: 16.w,
                  decoration: BoxDecoration(
                    color: widget.cardColor,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: CupertinoColors.white,
                      width: 1.5,
                    ),
                  ),
                  child: Icon(
                    CupertinoIcons.checkmark,
                    size: 10.sp,
                    color: CupertinoColors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildReminderSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              '产检前提示',
              style: TextUtil.base.sp(16).semiBold.withColor(widget.cardColor),
            ),
          ),

          SizedBox(height: 16.h),

          // 出行时间
          Row(
            children: [
              Text(
                '出行时间',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _showReminderTimePicker,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: widget.cardColor),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Text(
                    _reminderTimeController.text.isEmpty
                        ? '00 分钟'
                        : _reminderTimeController.text,
                    style: TextUtil.base
                        .sp(12)
                        .withColor(
                          _reminderTimeController.text.isEmpty
                              ? CupertinoColors.systemGrey
                              : widget.cardColor,
                        ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),

          // 设置提醒
          Row(
            children: [
              Text(
                '设置提醒',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _showAdvanceReminderPicker,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: widget.cardColor),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _reminderAdvanceController.text.isEmpty
                            ? '00 分钟'
                            : _reminderAdvanceController.text,
                        style: TextUtil.base
                            .sp(12)
                            .withColor(
                              _reminderAdvanceController.text.isEmpty
                                  ? CupertinoColors.systemGrey
                                  : widget.cardColor,
                            ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '前',
                        style: TextUtil.base.sp(12).withColor(widget.cardColor),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 项目要做什么准备
          Row(
            children: [
              Text(
                '项目要做什么准备',
                style: TextUtil.base.sp(14).withColor(widget.cardColor),
              ),
              Spacer(),
              SizedBox(height: 8.h),
              _buildPreparationTagButton('无创DNA'),
              SizedBox(width: 4.w),
              _buildPreparationTagButton('羊水穿刺'),
            ],
          ),

          SizedBox(height: 16.h),
          Container(
            width: double.infinity,
            height: 120.h,
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              border: Border.all(color: widget.cardColor),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: CupertinoTextField(
              controller: _pregnancyCheckRecordController,
              placeholder: '产检前笔记',
              placeholderStyle: TextUtil.base
                  .sp(14)
                  .withColor(CupertinoColors.systemGrey),
              style: TextUtil.base.sp(14).withColor(widget.cardColor),
              decoration: null,
              padding: EdgeInsets.zero,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              '产检笔记',
              style: TextUtil.base.sp(16).semiBold.withColor(widget.cardColor),
            ),
          ),
          SizedBox(height: 12.h),
          Container(
            width: double.infinity,
            height: 120.h,
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              border: Border.all(color: widget.cardColor),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: CupertinoTextField(
              controller: _notesController,
              placeholder: '医生讲的注意事项',
              placeholderStyle: TextUtil.base
                  .sp(14)
                  .withColor(CupertinoColors.systemGrey),
              style: TextUtil.base.sp(14).withColor(widget.cardColor),
              decoration: null,
              padding: EdgeInsets.zero,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
            ),
          ),
        ],
      ),
    );
  }

  // 显示时间选择器
  void _showTimePicker() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          color: CupertinoColors.white,
          child: Column(
            children: [
              Container(
                height: 50.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        '取消',
                        style: TextUtil.base
                            .sp(16)
                            .withColor(CupertinoColors.systemGrey),
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.time,
                  use24hFormat: true,
                  onDateTimeChanged: (DateTime dateTime) {
                    setState(() {
                      _timeController.text =
                          '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
                    });
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 显示提醒时间选择器
  void _showReminderTimePicker() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          color: CupertinoColors.white,
          child: Column(
            children: [
              Container(
                height: 50.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        '取消',
                        style: TextUtil.base
                            .sp(16)
                            .withColor(CupertinoColors.systemGrey),
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32.h,
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      _reminderTimeController.text = '${(index + 1) * 5} 分钟';
                    });
                  },
                  children: List.generate(24, (index) {
                    final minutes = (index + 1) * 5;
                    return Center(
                      child: Text('$minutes 分钟', style: TextUtil.base.sp(16)),
                    );
                  }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 显示提前提醒选择器
  void _showAdvanceReminderPicker() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          color: CupertinoColors.white,
          child: Column(
            children: [
              Container(
                height: 50.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        '取消',
                        style: TextUtil.base
                            .sp(16)
                            .withColor(CupertinoColors.systemGrey),
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextUtil.base.sp(16).withColor(widget.cardColor),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32.h,
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      _reminderAdvanceController.text =
                          '${(index + 1) * 10} 分钟';
                    });
                  },
                  children: List.generate(12, (index) {
                    final minutes = (index + 1) * 10;
                    return Center(
                      child: Text('$minutes 分钟', style: TextUtil.base.sp(16)),
                    );
                  }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 显示添加重点项目标签底部弹出sheet
  void _showAddKeyProjectBottomSheet() {
    showCupertinoModalBottomSheet<void>(
      context: context,
      backgroundColor: CupertinoColors.white,
      builder: (BuildContext context) {
        return PrenatalCheckupSelectionBottomSheet(
          color: widget.cardColor,
          bgColor: widget.cardBgColor,
          onCheckupSelected: (String checkup) {
            setState(() {
              // 将新标签添加到所有标签集合中（如果不存在的话）
              _allKeyProjects.add(checkup);
              // 新添加的标签默认不选中，用户需要手动点击选中
            });
            ToastUtil.showSuccess('已添加：$checkup');
          },
        );
      },
    );
  }

  // 显示项目准备信息bottomsheet
  void _showPreparationInfoBottomSheet(String projectName) {
    final preparationInfo = PreparationData.getPreparationInfo(projectName);

    if (preparationInfo == null) {
      ToastUtil.showError('暂无该项目的准备信息');
      return;
    }

    showCupertinoModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return PreparationInfoBottomSheet(
          preparationInfo: preparationInfo,
          cardColor: widget.cardColor,
          cardBgColor: widget.cardBgColor,
        );
      },
    );
  }

  // 显示添加项目准备标签对话框
  void _showAddPreparationTagDialog() {
    final TextEditingController tagController = TextEditingController();

    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text('添加准备项目', style: TextUtil.base.sp(18).semiBold),
          content: Padding(
            padding: EdgeInsets.only(top: 16.h),
            child: CupertinoTextField(
              controller: tagController,
              placeholder: '输入准备项目名称',
              placeholderStyle: TextUtil.base
                  .sp(14)
                  .withColor(CupertinoColors.systemGrey),
              style: TextUtil.base.sp(14),
              autofocus: true,
            ),
          ),
          actions: [
            CupertinoDialogAction(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: TextUtil.base
                    .sp(16)
                    .withColor(CupertinoColors.systemGrey),
              ),
            ),
            CupertinoDialogAction(
              onPressed: () {
                if (tagController.text.trim().isNotEmpty) {
                  setState(() {
                    _selectedPreparationItems.add(tagController.text.trim());
                  });
                }
                Navigator.of(context).pop();
              },
              child: Text(
                '确定',
                style: TextUtil.base.sp(16).withColor(widget.cardColor),
              ),
            ),
          ],
        );
      },
    );
  }

  // 选择产检单图片（直接从相册选择）
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85, // 压缩图片质量
        maxWidth: 1920,
        maxHeight: 1920,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
        ToastUtil.showSuccess('图片选择成功');
      }
    } on PlatformException catch (e) {
      // 处理平台异常
      String errorMessage = '选择图片失败';
      if (e.code == 'photo_access_denied') {
        errorMessage = '相册访问权限被拒绝，请在设置中开启权限';
      } else if (e.code == 'camera_access_denied') {
        errorMessage = '相机访问权限被拒绝，请在设置中开启权限';
      } else if (e.code == 'invalid_image') {
        errorMessage = '选择的文件不是有效的图片格式';
      } else {
        errorMessage = '选择图片失败：${e.message ?? e.code}';
      }
      ToastUtil.showError(errorMessage);
    } catch (e) {
      ToastUtil.showError('选择图片失败：$e');
    }
  }



  Widget _buildPregnancyCheckRecordSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: CupertinoColors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: ColorUtil.borderGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              '产检单记录',
              style: TextUtil.base.sp(18).semiBold.withColor(widget.cardColor),
            ),
          ),
          SizedBox(height: 16.h),

          // 图片显示区域或上传按钮
          _selectedImage != null ? _buildImageDisplay() : _buildUploadButton(),
        ],
      ),
    );
  }

  // 构建上传按钮
  Widget _buildUploadButton() {
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        width: double.infinity,
        height: 50.h,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.cardColor,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.cloud_upload,
              color: widget.cardColor,
              size: 20.sp,
            ),
            SizedBox(width: 8.w),
            Text(
              '上传产检单',
              style: TextUtil.base.sp(14).withColor(widget.cardColor),
            ),
            
          ],
        ),
      ),
    );
  }

  // 构建图片显示区域
  Widget _buildImageDisplay() {
    return Container(
      width: double.infinity,
      height: 200.h,
      decoration: BoxDecoration(
        border: Border.all(color: widget.cardColor),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Stack(
        children: [
          // 图片显示
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: SizedBox(
              width: double.infinity,
              height: 200.h,
              child: Image.file(
                _selectedImage!,
                fit: BoxFit.fitHeight, // 保持宽高比，高度填满
                alignment: Alignment.center,
              ),
            ),
          ),
          // 重新选择按钮
          Positioned(
            top: 8.h,
            right: 8.w,
            child: GestureDetector(
              onTap: _pickImage,
              child: Container(
                padding: EdgeInsets.all(6.w),
                decoration: BoxDecoration(
                  color: widget.cardBgColor,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Icon(
                  CupertinoIcons.refresh,
                  color: widget.cardColor,
                  size: 16.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建保存按钮
  Widget _buildSaveButton() {
    return Container(
      width: double.infinity,
      height: 48.h,
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        color: widget.cardColor,
        borderRadius: BorderRadius.circular(24.r),
        onPressed: _handleSave,
        child: Text(
          '保存',
          style: TextUtil.base
              .sp(16)
              .semiBold
              .withColor(CupertinoColors.white),
        ),
      ),
    );
  }

  // 处理保存逻辑
  void _handleSave() {
    if (widget.isDetailMode && widget.onSave != null) {
      // 详情模式下调用外部保存回调
      widget.onSave!();
    } else {
      // 默认保存逻辑
      // TODO: 实现保存逻辑
      // 这里可以添加表单验证和数据保存逻辑

      // 显示保存成功提示
      ToastUtil.showSaveSuccess();
      print('产检表单保存');
    }
  }
}
