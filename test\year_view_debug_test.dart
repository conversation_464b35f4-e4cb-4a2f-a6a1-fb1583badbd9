import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:align/widgets/swipeable_calendar.dart';

void main() {
  group('Year View Debug Tests', () {
    testWidgets('Debug year view PageView behavior', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 15);
      String? currentMonthTitle;
      CustomCalendarFormat? currentFormat;
      int pageChangeCount = 0;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: Safe<PERSON><PERSON>(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    onDaySelected: (day) {},
                    onMonthChanged: (month) {
                      currentMonthTitle = month;
                      pageChangeCount++;
                      print('Page change #$pageChangeCount: $month');
                    },
                    onViewChanged: (format) {
                      currentFormat = format;
                      print('View changed to: $format');
                    },
                    initialFormat: CustomCalendarFormat.year,
                    swipeDirection: SwipeDirection.horizontal,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      print('Initial state:');
      print('  Format: $currentFormat');
      print('  Title: $currentMonthTitle');
      print('  Page changes: $pageChangeCount');

      // 查找PageView
      final pageViewFinder = find.byType(PageView);
      expect(pageViewFinder, findsOneWidget);
      
      final pageView = tester.widget<PageView>(pageViewFinder);
      print('PageView scroll direction: ${pageView.scrollDirection}');
      
      // 重置计数器
      pageChangeCount = 0;
      
      // 尝试滑动（使用fling模拟快速滑动）
      print('\nAttempting to fling PageView...');
      await tester.fling(pageViewFinder, Offset(-300, 0), 1000); // 快速滑动
      await tester.pump();
      
      print('After drag (before settle):');
      print('  Format: $currentFormat');
      print('  Title: $currentMonthTitle');
      print('  Page changes: $pageChangeCount');
      
      await tester.pumpAndSettle();
      
      print('After settle:');
      print('  Format: $currentFormat');
      print('  Title: $currentMonthTitle');
      print('  Page changes: $pageChangeCount');
      
      // 检查PageView的controller状态
      if (pageView.controller != null) {
        print('PageView controller page: ${pageView.controller!.page}');
      }
    });

    testWidgets('Test PageView controller directly', (WidgetTester tester) async {
      final testDate = DateTime(2024, 6, 15);
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) {
            return CupertinoApp(
              home: CupertinoPageScaffold(
                child: SafeArea(
                  child: SwipeableCalendar(
                    selectedDay: testDate,
                    initialFormat: CustomCalendarFormat.year,
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 查找PageView
      final pageViewFinder = find.byType(PageView);
      final pageView = tester.widget<PageView>(pageViewFinder);
      
      print('PageView properties:');
      print('  Controller: ${pageView.controller}');
      print('  Scroll direction: ${pageView.scrollDirection}');
      print('  Physics: ${pageView.physics}');
      print('  OnPageChanged: ${pageView.onPageChanged}');
      
      // PageView的基本信息已经足够
    });
  });
}
